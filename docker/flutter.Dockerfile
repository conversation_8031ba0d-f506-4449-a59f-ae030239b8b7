FROM debian:bullseye-slim

ENV DEBIAN_FRONTEND="noninteractive"

# run apk update --no-cache && apk upgrade --no-cache
RUN apt-get update
RUN apt-get install -y \
          curl \
          git \
          unzip \
          xz-utils \
          zip \
          libglu1-mesa \
          clang \
          cmake \
          ninja-build \
          pkg-config \
          libgtk-3-dev \
          libblkid1

RUN apt-get clean
RUN rm -rf /var/lib/apt/lists/*

# download the sdk from https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.29.0-stable.tar.xz
# RUN curl -o /tmp/flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.29.0-stable.tar.xz
# RUN tar xf /tmp/flutter.tar.xz -C /usr/local
RUN git clone https://github.com/flutter/flutter.git -b 3.27.4 /usr/local/flutter

ENV PATH="/usr/local/flutter/bin:/usr/local/flutter/bin/cache/dart-sdk/bin:$PATH"
RUN git config --global --add safe.directory /usr/local/flutter
# RUN flutter doctor -v
