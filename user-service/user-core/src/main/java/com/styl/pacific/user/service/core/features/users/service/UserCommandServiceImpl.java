/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.users.service;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.DefaultSystemRole;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessQueryService;
import com.styl.pacific.user.service.core.features.alternativeaccess.entities.AlternativeUserAccess;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationCommandService;
import com.styl.pacific.user.service.core.features.notifications.request.ControlNotificationCommand;
import com.styl.pacific.user.service.core.features.userroles.UserRoleQueryService;
import com.styl.pacific.user.service.core.features.userroles.request.GetSingleUserRoleQuery;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.service.core.features.users.config.UserSchedulingDeletionProperties;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.events.UserDeletedEvent;
import com.styl.pacific.user.service.core.features.users.handler.UserTypeCommandHandler;
import com.styl.pacific.user.service.core.features.users.mapper.UserMapper;
import com.styl.pacific.user.service.core.features.users.request.ActivateUserTenantCommand;
import com.styl.pacific.user.service.core.features.users.request.DeleteUserCommand;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserCommand;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import com.styl.pacific.user.shared.enums.UserNonCompletedAction;
import com.styl.pacific.user.shared.enums.UserPermissionStatus;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.exceptions.NotAllowAssignTenantOwnerException;
import com.styl.pacific.user.shared.exceptions.TenantOwnerExistedRestrictException;
import com.styl.pacific.user.shared.exceptions.UserDomainException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserRoleChangeNotAllowException;
import com.styl.pacific.user.shared.exceptions.UserRoleNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserTypeCommandHandlerException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserCommandServiceImpl implements UserCommandService {
	private final UserRepository userRepository;
	private final UserQueryService userQueryService;
	private final UserRoleQueryService userRoleQueryService;
	private final UserSchedulingDeletionProperties userSchedulingDeletionProperties;
	private final UserInvitationCommandService userInvitationCommandService;
	private final List<UserTypeCommandHandler> userTypeHandlers;
	private final AlternativeUserAccessQueryService alternativeUserAccessQueryService;

	private final ApplicationEventPublisher eventPublisher;

	public User addSingleUser(UpsertTenantUserCommand command, ControlNotificationCommand controlNotificationCommand) {
		final var handler = userTypeHandlers.stream()
				.filter(it -> it.isSupportedUserType(command.userType()))
				.findFirst()
				.orElseThrow(() -> new UserTypeCommandHandlerException("User type is not supported"));

		final var user = handler.addSingleUser(handler.validateAddUser(command));
		handler.postAddSingleUser(command.tenantId(), user, controlNotificationCommand);
		return user;
	}

	public User addSingleUser(UpsertTenantUserCommand command) {
		return addSingleUser(command, ControlNotificationCommand.builder()
				.isSkipNotification(false)
				.build());
	}

	@Override
	public boolean isAllowedUserAccess(String realmId, User user) {
		return UserStatus.ACTIVE == user.getUserStatus();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public User activateUser(ActivateUserTenantCommand request) {
		final var user = userRepository.getUserByCondition(FilterUserQuery.builder()
				.byUserId(request.getUserId())
				.build(), UserFetchRule.builder()
						.isFetchPermissions(true)
						.isFetchUserGroup(true)
						.build())
				.orElseThrow(() -> new UserNotFoundException("User has not found"));

		// In case SubAccount customer, status would be ACTIVE but not active from Keycloak
		final var isAlreadyActiveKeycloak = !Optional.ofNullable(user.getUserNonCompletedActions())
				.orElse(List.of())
				.contains(UserNonCompletedAction.KEYCLOAK_REGISTRATION) || StringUtils.isNotBlank(user.getSsoId());

		if (UserStatus.ACTIVE.equals(user.getUserStatus()) && isAlreadyActiveKeycloak) {
			log.info("User has been active");
			return user;
		}

		final var updatedUser = userRepository.save(user.withUserStatus(UserStatus.ACTIVE)
				.withPermissions(user.getPermissions()
						.stream()
						.map(it -> it.withPermissionStatus(UserPermissionStatus.ACTIVE))
						.toList())
				.withSsoId(request.getSsoId())
				.withFirstName(request.getFirstName())
				.withLastName(request.getLastName())
				.withUserNonCompletedActions(user.getUserNonCompletedActions()
						.stream()
						.filter(it -> !UserNonCompletedAction.KEYCLOAK_REGISTRATION.equals(it))
						.toList()));
		userInvitationCommandService.deleteInvitationByUserIdAndType(user.getId(),
				UserInvitationType.USER_REGISTRATION);
		return updatedUser;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteUser(User user) {
		userRepository.deleteUser(user);
		eventPublisher.publishEvent(UserDeletedEvent.builder()
				.user(user)
				.userIds(Set.of(user.getId()))
				.build());
	}

	@Override
	public void deleteUsersInSchedule(DeleteUserCommand command) {
		if (command.getByUserId() == null && CollectionUtils.isEmpty(command.getByUserIds())) {
			throw new UserDomainException("UserId is required");
		}
		final var userIds = Optional.ofNullable(command.getByUserId())
				.map(it -> Optional.ofNullable(command.getByUserIds())
						.map(st -> {
							st.add(it);
							return st;
						})
						.orElseGet(() -> Set.of(it)))
				.orElseGet(command::getByUserIds);

		final var alternativeUsers = alternativeUserAccessQueryService.getAllAlternativeUserAccessByUserIds(command
				.getTenantId(), userIds)
				.stream()
				.map(AlternativeUserAccess::getAlternativeUser)
				.map(User::getId)
				.collect(Collectors.toSet());

		userRepository.deleteUsersInSchedule(command.getTenantId(), Stream.concat(userIds.stream(), alternativeUsers
				.stream())
				.collect(Collectors.toSet()), Optional.ofNullable(command.getSchedulingDeletedAt())
						.orElseGet(() -> Instant.now()
								.plus(userSchedulingDeletionProperties.getDeletingUserAfter())));
	}

	@Override
	public int changeUserIntoDeletingProgress(Integer maxUserSize) {
		return userRepository.changeUserIntoDeletingProgress(maxUserSize);
	}

	@Override
	public User updateSingleUser(UpdateUserCommand command) {

		final var user = userQueryService.getSingleUserById(command.getId())
				.orElseThrow(() -> new UserNotFoundException("User not found"));

		final var handler = userTypeHandlers.stream()
				.filter(it -> it.isSupportedUserType(user.getUserType()))
				.findFirst()
				.orElseThrow(() -> new UserTypeCommandHandlerException("User type is not supported"));

		handler.validateUpdateUserCommand(user, command);
		return handler.updateSingleUser(user, command);
	}

	@Override
	public User changeUserRole(TenantId tenantId, UserId targetUserId, UserRoleId targetUserRoleId,
			TokenClaim request) {
		if (MapstructCommonDomainMapper.INSTANCE.stringToUserId(request.getUserId())
				.equals(targetUserId)) {
			throw new UserRoleChangeNotAllowException("Cannot change role in yourself");
		}

		final var user = userQueryService.getSingleUserById(targetUserId)
				.orElseThrow(() -> new UserNotFoundException("User not found"));

		final var currentPermission = user.getPermissions()
				.stream()
				.filter(it -> tenantId.equals(it.getTenantId()))
				.findFirst()
				.orElseThrow(() -> new UserRoleNotFoundException("User role has not found"));

		final var currentUserRoleId = currentPermission.getUserRoleId();

		if (currentUserRoleId.equals(targetUserRoleId)) {
			return user;
		}

		final var currentUserRole = userRoleQueryService.getUserRole(GetSingleUserRoleQuery.builder()
				.byTenantId(tenantId)
				.byUserRoleId(currentUserRoleId)
				.build());

		if (!UserType.SYSTEM_ADMIN.equals(request.getUserType()) && currentUserRole.isPresent()
				&& DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId()
						.equals(currentUserRole.get()
								.getExternalId())) {
			throw new UserRoleChangeNotAllowException("Cannot assign Tenant Owner role due not to Admin");
		}

		final var newUserRole = userRoleQueryService.getSingleUserRole(GetSingleUserRoleQuery.builder()
				.byTenantId(tenantId)
				.byUserRoleId(targetUserRoleId)
				.build());

		if (DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId()
				.equals(newUserRole.getExternalId()) && !UserType.SYSTEM_ADMIN.equals(request.getUserType())) {
			throw new NotAllowAssignTenantOwnerException("Only Admin can assign Tenant Owner role.");
		}

		if (DefaultSystemRole.TENANT_OWNER_ROLE.getExternalId()
				.equals(newUserRole.getExternalId()) && userRepository.countUserByRoleIdAndTenantId(tenantId,
						newUserRole.getId()) != 0) {
			throw new TenantOwnerExistedRestrictException(
					"Owner has been existed. Please un-assign Tenant Owner role first.");
		}

		final var newPermissions = user.getPermissions()
				.stream()
				.map(it -> {
					if (tenantId.equals(it.getTenantId())) {
						return it.withUserRoleId(newUserRole.getId());
					}
					return it;
				})
				.toList();
		return updateSingleUser(UserMapper.INSTANCE.toUpdateCommand(user.withPermissions(newPermissions)));

	}
}
