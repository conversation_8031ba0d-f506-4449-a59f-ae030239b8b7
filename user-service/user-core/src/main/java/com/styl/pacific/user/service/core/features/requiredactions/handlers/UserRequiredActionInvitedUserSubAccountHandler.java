/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.requiredactions.handlers;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.user.service.core.features.notifications.NotificationPublisher;
import com.styl.pacific.user.service.core.features.requiredactions.UserRequiredActionRepository;
import com.styl.pacific.user.service.core.features.requiredactions.config.UserRequiredActionProperties;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredAction;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredActionSubAccountInvitedMetadata;
import com.styl.pacific.user.service.core.features.requiredactions.request.CreateUserRequiredActionCommand;
import com.styl.pacific.user.service.core.features.requiredactions.request.ExistUserRequireActionQuery;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountCommandService;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;
import com.styl.pacific.user.service.core.features.subaccounts.request.ChangeStatusSubAccountCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.DeleteSubAccountCommand;
import com.styl.pacific.user.shared.enums.UserRequiredActionReplyOption;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import com.styl.pacific.user.shared.enums.UserSubAccountStatus;
import com.styl.pacific.user.shared.exceptions.UserRequiredActionException;
import java.util.Set;
import org.springframework.stereotype.Component;

@Component
public class UserRequiredActionInvitedUserSubAccountHandler extends UserRequiredActionHandler {

	private final UserSubAccountCommandService subAccountCommandService;
	private final UserSubAccountQueryService userSubAccountQueryService;
	private final NotificationPublisher notificationPublisher;

	public UserRequiredActionInvitedUserSubAccountHandler(UserRequiredActionRepository repository,
			UserRequiredActionProperties configProperties, UserSubAccountCommandService subAccountCommandService,
			UserSubAccountQueryService userSubAccountQueryService, NotificationPublisher notificationPublisher) {
		super(repository, configProperties);
		this.subAccountCommandService = subAccountCommandService;
		this.userSubAccountQueryService = userSubAccountQueryService;
		this.notificationPublisher = notificationPublisher;
	}

	@Override
	public boolean isSupported(UserRequiredActionType actionType) {
		return UserRequiredActionType.INVITED_USER_SUB_ACCOUNT.equals(actionType);
	}

	@Override
	public void validate(CreateUserRequiredActionCommand command) {
		final var metadata = (UserRequiredActionSubAccountInvitedMetadata) command.getMetadata();
		if (repository.existsUserRequiredAction(ExistUserRequireActionQuery.builder()
				.byUserId(command.getUserId())
				.byActionType(UserRequiredActionType.INVITED_USER_SUB_ACCOUNT)
				.byTenantId(command.getTenantId())
				.byMetadataUserSubAccountId(metadata.getUserSubAccountId())
				.build())) {
			throw new UserRequiredActionException("Cannot create user required action for already existing one");
		}

	}

	@Override
	public void replyUserRequiredAction(UserRequiredAction userRequiredAction,
			UserRequiredActionReplyOption repliedOption) {
		final var metadata = (UserRequiredActionSubAccountInvitedMetadata) userRequiredAction.getMetadata();
		final var subAccountId = MapstructCommonDomainMapper.INSTANCE.longToUserSubAccountId(metadata
				.getUserSubAccountId());
		UserSubAccount userSubAccount = userSubAccountQueryService.getSubAccountBySubAccountId(subAccountId);
		deleteUserRequiredAction(userRequiredAction);
		if (UserRequiredActionReplyOption.ACCEPT.equals(repliedOption)) {
			subAccountCommandService.changeStatusById(ChangeStatusSubAccountCommand.builder()
					.subAccountId(subAccountId)
					.subAccountStatus(UserSubAccountStatus.ACTIVE)
					.build());
			notificationPublisher.publish(userSubAccount);
			notificationPublisher.publish(userSubAccount, Action.ACCEPT_SUB_ACCOUNT_INVITATION);
		} else {
			subAccountCommandService.deleteBySubAccountId(DeleteSubAccountCommand.builder()
					.tenantId(userRequiredAction.getTenantId())
					.subAccountId(subAccountId)
					.build());
			notificationPublisher.publish(userSubAccount, Action.REJECT_SUB_ACCOUNT_INVITATION);
		}
	}

	@Override
	public Set<UserRequiredActionReplyOption> getValidReplyOptions() {
		return Set.of(UserRequiredActionReplyOption.ACCEPT, UserRequiredActionReplyOption.CANCEL);
	}
}
