/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.userinternal.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.userinternal.request.ActivateUserInternalCommand;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.request.ActivateUserTenantCommand;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserCommand;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.shared.enums.UserStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserInternalRequestCommandMapper {
	UserInternalRequestCommandMapper INSTANCE = Mappers.getMapper(UserInternalRequestCommandMapper.class);

	@Mapping(target = "userId", source = "userId")
	ActivateUserTenantCommand toActivateCommand(UserId userId, ActivateUserInternalCommand command);

	@Mapping(target = "id", source = "user.id")
	@Mapping(target = "userGroupId", source = "user.userGroup.id")
	@Mapping(target = "ssoId", source = "command.ssoId")
	@Mapping(target = "firstName", source = "command.firstName")
	@Mapping(target = "lastName", source = "command.lastName")
	@Mapping(target = "email", source = "command.email")
	@Mapping(target = "trustedSource", source = "command.trustedSource")
	@Mapping(target = "phoneNumber", source = "user.phoneNumber")
	@Mapping(target = "avatarPath", source = "user.avatarPath")
	@Mapping(target = "avatarHash", source = "user.avatarHash")
	@Mapping(target = "externalId", source = "user.externalId")
	@Mapping(target = "userStatus", source = "userStatus")
	@Mapping(target = "updatePermissions", ignore = true)
	@Mapping(target = "isInternalUpdating", constant = "true")
	UpdateUserCommand toUpdateUserCommand(User user, UserStatus userStatus, ActivateUserInternalCommand command);

	@Mapping(target = "userId", ignore = true)
	@Mapping(target = "permissionStatus", ignore = true)
	@Mapping(target = "userRoleId", ignore = true)
	@Mapping(target = "userType", ignore = true)
	@Mapping(target = "userStatus", ignore = true)
	@Mapping(target = "userNonCompletedActions", ignore = true)
	@Mapping(target = "userRole", ignore = true)
	@Mapping(target = "userGroupId", ignore = true)
	@Mapping(target = "phoneNumber", ignore = true)
	@Mapping(target = "avatarPath", ignore = true)
	@Mapping(target = "avatarHash", ignore = true)
	@Mapping(target = "externalId", ignore = true)
	@Mapping(target = "defaultDateFormat", ignore = true)
	@Mapping(target = "defaultTimeFormat", ignore = true)
	@Mapping(target = "defaultTimezone", ignore = true)
	@Mapping(target = "uniqueExternalId", ignore = true)
	@Mapping(target = "migrationId", ignore = true)
	@Mapping(target = "familyCode", ignore = true)
	@Mapping(target = "isAlternativeUser", ignore = true)
	UpsertTenantUserCommand toUpsertTenantUserCommand(ActivateUserInternalCommand command);
}