/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.userinternal.service;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationCommandService;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationQueryService;
import com.styl.pacific.user.service.core.features.invitations.request.GetUserInvitationQuery;
import com.styl.pacific.user.service.core.features.userinternal.UserInternalCommandService;
import com.styl.pacific.user.service.core.features.userinternal.events.UserActivatedEvent;
import com.styl.pacific.user.service.core.features.userinternal.mapper.UserInternalRequestCommandMapper;
import com.styl.pacific.user.service.core.features.userinternal.request.ActivateUserInternalCommand;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserPermissionCommand;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import com.styl.pacific.user.shared.enums.UserPermissionStatus;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserInternalCommandServiceImpl implements UserInternalCommandService {
	private final UserInvitationQueryService queryService;
	private final UserCommandService userCommandService;
	private final UserQueryService userQueryService;
	private final UserInvitationCommandService userInvitationCommandService;
	private final ApplicationEventPublisher eventPublisher;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public User activateUserByInvitationCode(ActivateUserInternalCommand command) {
		final var userInvitation = queryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.realmId(command.getRealmId())
				.byContentCode(command.getInvitationCode())
				.build());

		log.debug("activateUser: RealmID: {} - SSO_ID: {} - Email: {} - Type: {} - Code: {}", command.getRealmId(),
				command.getSsoId(), command.getEmail(), userInvitation.getType(), command.getInvitationCode());

		if (UserInvitationType.USER_SELF_REGISTRATION.equals(userInvitation.getType())) {
			final var customer = activateCustomer(command);
			// This would cover for exact Invitation (Self registration link and tenant user invited)
			userInvitationCommandService.deleteInvitationById(userInvitation.getId());
			// Ensure the user invitation from tenant user to be cleaned
			userInvitationCommandService.deleteInvitationByUserIdAndType(customer.getId(), userInvitation.getType());

			eventPublisher.publishEvent(UserActivatedEvent.builder()
					.user(customer)
					.tenantId(command.getTenantId())
					.build());
			return customer;
		}

		final var user = userCommandService.activateUser(UserInternalRequestCommandMapper.INSTANCE.toActivateCommand(
				userInvitation.getUser()
						.getId(), command));
		eventPublisher.publishEvent(UserActivatedEvent.builder()
				.user(user)
				.tenantId(command.getTenantId())
				.build());
		return user;
	}

	private User activateCustomer(ActivateUserInternalCommand command) {
		return userQueryService.getUserByConditions(FilterUserQuery.builder()
				.byEmail(command.getEmail())
				.byRealmId(command.getRealmId())
				.build(), UserFetchRule.builder()
						.isFetchPermissions(true)
						.build())
				.map(user -> userCommandService.updateSingleUser(UserInternalRequestCommandMapper.INSTANCE
						.toUpdateUserCommand(user, UserStatus.ACTIVE, command)
						.withUpdatePermissions(user.getPermissions()
								.stream()
								.map(it -> UpdateUserPermissionCommand.builder()
										.tenantId(it.getTenantId())
										.userRoleId(it.getUserRoleId())
										.permissionStatus(UserPermissionStatus.ACTIVE)
										.build())
								.toList())))
				.orElseGet(() -> {
					if (!command.isAllowedUserCreation()) {
						throw new UserNotFoundException("User not found");
					}
					return userCommandService.addSingleUser(UserInternalRequestCommandMapper.INSTANCE
							.toUpsertTenantUserCommand(command)
							.withUserType(UserType.CUSTOMER)
							.withUserStatus(UserStatus.ACTIVE)
							.withPermissionStatus(UserPermissionStatus.ACTIVE));
				});
	}

	@Override
	public User activateUserByTrustedSource(ActivateUserInternalCommand command) {
		final var customer = activateCustomer(command);
		eventPublisher.publishEvent(UserActivatedEvent.builder()
				.user(customer)
				.tenantId(command.getTenantId())
				.build());
		return customer;
	}
}
