/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.subaccounts.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.service.core.features.subaccounts.request.UpdateSubAccountUserCommand;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserCommand;
import com.styl.pacific.user.shared.http.subaccounts.request.UpdateSubAccountUserRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, })
public interface UserSubAccountMapper {
	UserSubAccountMapper INSTANCE = Mappers.getMapper(UserSubAccountMapper.class);

	@Mapping(target = "updatePermissions", source = "user.permissions")
	@Mapping(target = "email", source = "email")
	@Mapping(target = "userGroupId", ignore = true)
	@Mapping(target = "isInternalUpdating", ignore = true)
	UpdateUserCommand toUpdateUserCommand(User user, String email);

	@Mapping(target = "id", source = "user.id")
	@Mapping(target = "userGroupId", source = "command.userGroupId")
	@Mapping(target = "ssoId", source = "user.ssoId")
	@Mapping(target = "firstName", source = "command.firstName")
	@Mapping(target = "lastName", source = "command.lastName")
	@Mapping(target = "phoneNumber", source = "command.phoneNumber")
	@Mapping(target = "email", source = "user.email")
	@Mapping(target = "userStatus", source = "user.userStatus")
	@Mapping(target = "avatarPath", source = "command.avatarPath")
	@Mapping(target = "updatePermissions", source = "user.permissions")
	@Mapping(target = "isInternalUpdating", ignore = true)
	UpdateUserCommand toUpdateUserCommand(User user, UpdateSubAccountUserCommand command);

	@Mapping(target = "subAccountId", source = "subAccountId")
	@Mapping(target = "sponsorUserId", source = "sponsorUserId")
	@Mapping(target = "firstName", source = "request.firstName")
	@Mapping(target = "lastName", source = "request.lastName")
	@Mapping(target = "phoneNumber", source = "request.phoneNumber")
	@Mapping(target = "avatarPath", source = "request.avatarPath")
	@Mapping(target = "userGroupId", source = "request.userGroupId", qualifiedByName = "longToUserGroupId")
	UpdateSubAccountUserCommand toUpdateSubAccountUserCommand(UserId sponsorUserId, UserSubAccountId subAccountId,
			UpdateSubAccountUserRequest request);
}
