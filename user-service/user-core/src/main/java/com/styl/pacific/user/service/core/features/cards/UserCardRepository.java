/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.cards;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserCardId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.cards.entities.UserCard;
import com.styl.pacific.user.service.core.features.cards.request.UserCardPaginationQuery;
import java.util.Optional;
import java.util.Set;

public interface UserCardRepository {
	UserCard save(UserCard entity);

	void deleteUserCard(UserCardId id);

	Optional<UserCard> getUserCardByUserIdAndTenantIdAndCardId(UserId userId, TenantId tenantId, String cardId);

	Optional<UserCard> getUserCardByUserIdAndId(UserId userId, UserCardId id);

	Paging<UserCard> queryUserCards(UserCardPaginationQuery pageRequest);

	void deleteUserCardByUserIds(Set<UserId> userIds);

	boolean existsByCardIdAndTenantId(String cardId, TenantId tenantId);

	boolean existsByCardIdAndTenantIdAndNotId(String cardId, TenantId tenantId, Long id);

	Optional<UserCard> getUserCardByUserIdAndTenantIdAndId(UserId userId, UserCardId userCardId, TenantId tenantId);

	Optional<UserCard> getUserCardByTenantIdAndId(TenantId tenantId, String cardId);
}
