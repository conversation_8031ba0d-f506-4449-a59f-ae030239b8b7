/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.userinternal;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.user.api.alternativeaccess.mapper.AlternativeAccessRequestMapper;
import com.styl.pacific.user.api.invitations.mapper.UserInvitationRequestMapper;
import com.styl.pacific.user.api.invitations.mapper.UserInvitationResponseMapper;
import com.styl.pacific.user.api.preferences.mapper.UserPreferenceResponseMapper;
import com.styl.pacific.user.api.userinternal.mapper.UserInternalRequestMapper;
import com.styl.pacific.user.api.users.mapper.UserAccessResponseMapper;
import com.styl.pacific.user.api.users.mapper.UserResponseMapper;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessQueryService;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.FilterAlternativeUserAccess;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationQueryService;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceQueryService;
import com.styl.pacific.user.service.core.features.preferences.request.FindUserPreferenceQuery;
import com.styl.pacific.user.service.core.features.userinternal.UserInternalCommandService;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import com.styl.pacific.user.shared.exceptions.UserAccessDomainForbiddenException;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import com.styl.pacific.user.shared.http.alternativecustomers.response.FullAlternativeUserAccessResponse;
import com.styl.pacific.user.shared.http.apis.UserInternalApi;
import com.styl.pacific.user.shared.http.internal.request.FindAlternativeUserAccessRequest;
import com.styl.pacific.user.shared.http.invitations.request.GetUserInvitationRequest;
import com.styl.pacific.user.shared.http.invitations.response.UserInvitationResponse;
import com.styl.pacific.user.shared.http.preferences.response.UserPreferenceResponse;
import com.styl.pacific.user.shared.http.users.request.ActivateUserInternalRequest;
import com.styl.pacific.user.shared.http.users.response.UserAccessResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserInternalController implements UserInternalApi {

	private final UserQueryService userQueryService;

	private final UserCommandService userCommandService;

	private final UserInvitationQueryService queryService;

	private final UserInternalCommandService internalCommandService;

	private final UserPreferenceQueryService userPreferenceQueryService;

	private final AlternativeUserAccessQueryService alternativeUserAccessQueryService;

	@Override
	public UserAccessResponse getUserAccess(String realmId, String ssoId) {
		final var user = userQueryService.getSingleUserByRealmIdAndSsoId(realmId, ssoId);
		if (user.isEmpty()) {
			throw new UserNotFoundException(String.format("User has not found %s - %s", realmId, ssoId));
		}

		return user.filter(usr -> userCommandService.isAllowedUserAccess(realmId, usr))
				.map(UserAccessResponseMapper.INSTANCE::toAccessResponse)
				.orElseThrow(() -> new UserAccessDomainForbiddenException("User access is forbidden"));
	}

	@Override
	public FullAlternativeUserAccessResponse getAlternativeUserAccess(String realmId,
			FindAlternativeUserAccessRequest request) {
		return AlternativeAccessRequestMapper.INSTANCE.toResponse(alternativeUserAccessQueryService
				.getAlternativeUserAccess(FilterAlternativeUserAccess.builder()
						.byAlternativeUserId(MapstructCommonDomainMapper.INSTANCE.longToUserId(request
								.getByAlternativeUserId()))
						.byRealmId(realmId)
						.build()));
	}

	@Override
	public UserPreferenceResponse getUserPreferenceByKey(String realmId, String ssoId,
			UserPreferenceKey userPreferenceKey) {
		final var user = userQueryService.getUserByConditions(FilterUserQuery.builder()
				.byRealmId(realmId)
				.bySsoId(ssoId)
				.build(), UserFetchRule.builder()
						.isFetchPermissions(false)
						.isFetchUserGroup(false)
						.isCountSponsors(false)
						.isCountSubAccounts(false)
						.build())
				.orElseThrow(() -> new UserNotFoundException("User has not found"));

		return UserPreferenceResponseMapper.INSTANCE.toResponse(userPreferenceQueryService.getUserPreference(
				FindUserPreferenceQuery.builder()
						.byUserId(user.getId())
						.byKey(userPreferenceKey)
						.isUseTemplateIfNonExisted(true)
						.build()));
	}

	@Override
	public UserInvitationResponse getTenantUserInvitation(String realmId, @Valid GetUserInvitationRequest request) {
		final var userInvitation = queryService.getTenantUserInvitation(UserInvitationRequestMapper.INSTANCE
				.toGetUserInvitationQuery(request, realmId));
		return UserInvitationResponseMapper.INSTANCE.toResponse(userInvitation);
	}

	@Override
	public UserResponse activateUser(ActivateUserInternalRequest request) {
		final var isInvitationCodeActivation = StringUtils.isNotBlank(request.getInvitationCode());
		final var activateCommand = UserInternalRequestMapper.INSTANCE.toActivateCommand(request);
		return UserResponseMapper.INSTANCE.toUserResponse(isInvitationCodeActivation
				? internalCommandService.activateUserByInvitationCode(activateCommand.withAllowedUserCreation(true))
				: internalCommandService.activateUserByTrustedSource(activateCommand));
	}

}
