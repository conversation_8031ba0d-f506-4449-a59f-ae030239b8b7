/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.groups.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.shared.http.groups.response.UserGroupNodeResponse;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserGroupResponseMapper {
	UserGroupResponseMapper INSTANCE = Mappers.getMapper(UserGroupResponseMapper.class);

	@Mapping(target = "id", source = "id", qualifiedByName = "userGroupIdToString")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToString")
	@Mapping(target = "isDefaultGroup", expression = "java(com.styl.pacific.user.service.core.features.groups.constants.UserGroupConstants.isMainGroup(source.getGroupKey()))")
	UserGroupResponse toResponse(UserGroup source);

	@Mapping(target = "id", source = "id", qualifiedByName = "userGroupIdToString")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToString")
	@Mapping(target = "isDefaultGroup", expression = "java(com.styl.pacific.user.service.core.features.groups.constants.UserGroupConstants.isMainGroup(source.getGroupKey()))")
	UserGroupNodeResponse toUserGroupNodeResponse(UserGroup source);
}
