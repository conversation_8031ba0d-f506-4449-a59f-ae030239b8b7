/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.groups.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.user.service.core.features.groups.request.GetAllUserGroupFilter;
import com.styl.pacific.user.service.core.features.groups.request.UpsertUserGroupCommand;
import com.styl.pacific.user.shared.http.groups.request.CreateUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.FilterUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.UpdateUserGroupRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserGroupRequestMapper {

	UserGroupRequestMapper INSTANCE = Mappers.getMapper(UserGroupRequestMapper.class);

	@Mapping(target = "userGroupId", ignore = true)
	@Mapping(target = "parentId", source = "request.parentId", qualifiedByName = "longToUserGroupId")
	UpsertUserGroupCommand toUpsertCommand(TenantId tenantId, CreateUserGroupRequest request);

	@Mapping(target = "userGroupId", source = "request.userGroupId", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "parentId", source = "request.parentId", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "migrationId", ignore = true)
	UpsertUserGroupCommand toUpsertCommand(TenantId tenantId, UpdateUserGroupRequest request);

	@Mapping(target = "byUserGroupPath", source = "query.byUserGroupPath")
	@Mapping(target = "byUserGroupId", source = "query.byUserGroupId", qualifiedByName = "longToUserGroupId")
	@Mapping(target = "groupLevel", source = "query.groupLevel")
	@Mapping(target = "byTenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "byUserGroupIds", source = "query.byUserGroupIds", qualifiedByName = "longsToUserGroupIds")
	GetAllUserGroupFilter toUserGroupQuery(FilterUserGroupRequest query, Long tenantId);
}
