/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.customers.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.customers.valueobject.CustomerSelfRegistrationLink;
import java.net.URI;
import lombok.SneakyThrows;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface CustomerResponseMapper {
	CustomerResponseMapper INSTANCE = Mappers.getMapper(CustomerResponseMapper.class);

	@SneakyThrows
	default ResponseEntity<Void> toRedirectView(CustomerSelfRegistrationLink source) {
		final var redirectUrl = "%s&expiredAt=%s".formatted(source.getLink(), source.getExpiredAt()
				.toEpochMilli());
		return ResponseEntity.status(HttpStatus.FOUND)
				.location(new URI(redirectUrl))
				.build();
	}

}