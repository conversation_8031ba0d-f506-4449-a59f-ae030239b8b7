/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.groups;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.user.api.groups.mapper.UserGroupPaginationMapper;
import com.styl.pacific.user.api.groups.mapper.UserGroupRequestMapper;
import com.styl.pacific.user.api.groups.mapper.UserGroupResponseMapper;
import com.styl.pacific.user.service.core.features.groups.UserGroupCommandService;
import com.styl.pacific.user.service.core.features.groups.UserGroupQueryService;
import com.styl.pacific.user.service.core.features.groups.request.GetUserGroupQuery;
import com.styl.pacific.user.shared.http.apis.UserGroupApi;
import com.styl.pacific.user.shared.http.groups.request.CreateUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.QueryUserGroupPaginationRequest;
import com.styl.pacific.user.shared.http.groups.request.UpdateUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.response.UserGroupNodeResponse;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserGroupController implements UserGroupApi {

	private final UserGroupCommandService userGroupCommandService;
	private final UserGroupQueryService userGroupQueryService;
	private final RequestContext requestContext;

	@Override
	public UserGroupResponse createUserGroup(@Valid CreateUserGroupRequest request) {
		return UserGroupResponseMapper.INSTANCE.toResponse(userGroupCommandService.upsertUserGroup(
				UserGroupRequestMapper.INSTANCE.toUpsertCommand(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
						requestContext.getTenantId()), request)));
	}

	@Override
	public UserGroupResponse getUserGroup(Long userGroupId) {
		return UserGroupResponseMapper.INSTANCE.toResponse(userGroupQueryService.getUserGroup(GetUserGroupQuery
				.builder()
				.tenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()))
				.userGroupId(MapstructCommonDomainMapper.INSTANCE.longToUserGroupId(userGroupId))
				.build()));
	}

	@Override
	public void deleteUserGroup(Long userGroupId) {
		userGroupCommandService.deleteUserGroupById(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext
				.getTenantId()), new UserGroupId(userGroupId));
	}

	@Override
	public UserGroupResponse updateUserGroup(UpdateUserGroupRequest request) {
		return UserGroupResponseMapper.INSTANCE.toResponse(userGroupCommandService.upsertUserGroup(
				UserGroupRequestMapper.INSTANCE.toUpsertCommand(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
						requestContext.getTenantId()), request)));
	}

	@Override
	public Paging<UserGroupResponse> queryUserGroups(@Valid QueryUserGroupPaginationRequest request) {
		return UserGroupPaginationMapper.INSTANCE.toPagingResponse(userGroupQueryService.queryUserGroup(
				UserGroupPaginationMapper.INSTANCE.toPagingQuery(request, requestContext.getTenantId())));
	}

	@Override
	public List<UserGroupNodeResponse> getUserGroupTree() {
		return userGroupQueryService.getUserGroupTreeByTenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
				requestContext.getTenantId()))
				.stream()
				.map(UserGroupResponseMapper.INSTANCE::toUserGroupNodeResponse)
				.toList();
	}
}