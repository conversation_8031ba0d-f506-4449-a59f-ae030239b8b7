/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.requiredactions.repository;

import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.styl.pacific.user.service.data.access.relational.requiredactions.entities.QUserRequiredActionEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AdvanceQueryDslUserRequiredActionRepositoryImpl implements AdvanceQueryDslUserRequiredActionRepository {
	private final JPAQueryFactory queryFactory;

	@Override
	public void deleteByPredicate(Predicate predicate) {
		if (predicate == null) {
			return;
		}
		queryFactory.delete(QUserRequiredActionEntity.userRequiredActionEntity)
				.where(predicate)
				.execute();
	}
}
