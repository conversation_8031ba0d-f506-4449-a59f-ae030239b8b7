/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.cards.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.service.core.features.cards.entities.UserCard;
import com.styl.pacific.user.service.data.access.relational.cards.entities.UserCardEntity;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.service.data.access.relational.users.mapper.UserEntityMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface UserCardEntityMapper {
	UserCardEntityMapper INSTANCE = Mappers.getMapper(UserCardEntityMapper.class);

	@Mapping(target = "id", source = "id", qualifiedByName = "longToUserCardId")
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	UserCard toModel(UserCardEntity entity);

	@Mapping(target = "cardId", source = "userCard.cardId")
	@Mapping(target = "qCardId", source = "userCard.cardId")
	@Mapping(target = "cardAlias", source = "userCard.cardAlias")
	@Mapping(target = "status", source = "userCard.status")
	@Mapping(target = "migrationId", source = "userCard.migrationId")
	@Mapping(target = "userEntity", source = "userEntity")
	@Mapping(target = "tenantId", source = "userCard.tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	UserCardEntity toCreateEntity(UserEntity userEntity, UserCard userCard);

	@AfterMapping
	default void afterMappingModel(UserCardEntity entity, @MappingTarget UserCard.UserCardBuilder builder) {
		builder.user(UserEntityMapper.INSTANCE.toLiteModel(entity.getUserEntity()));
	}
}
