/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.users.repository;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.service.data.access.relational.users.projections.UserProjection;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface AdvanceQueryDslUserRepository {
	Page<UserProjection> findPaginationByPredicate(Predicate predicate, Pageable pageable);

	Optional<UserEntity> findOneByCondition(Predicate predicate, UserFetchRule fetchRule);

	List<UserEntity> getUsersByCondition(Predicate predicate, UserFetchRule fetchRule);
}
