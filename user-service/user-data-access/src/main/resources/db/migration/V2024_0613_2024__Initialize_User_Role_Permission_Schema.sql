CREATE
    COLLATION IF NOT EXISTS email_case_insensitive_unicode(
        provider = icu,
        locale = 'und-u-ks-level2',
        DETERMINISTIC = TRUE
    );

CREATE
    TABLE
        IF NOT EXISTS users(
            id BIGSERIAL NOT NULL CONSTRAINT users_pk PRIMARY KEY,
            sso_id VARCHAR(50),
            user_status VARCHAR(20) NOT NULL,
            user_type VARCHAR(20) NOT NULL,
            email VARCHAR(50) COLLATE email_case_insensitive_unicode NOT NULL,
            first_name <PERSON><PERSON><PERSON><PERSON>(120),
            last_name <PERSON><PERSON><PERSON><PERSON>(120),
            phone_number VARCHAR(20),
            realm_id VARCHAR(50),
            created_at TIMESTAMP(6) DEFAULT NOW(),
            created_by <PERSON><PERSON>IN<PERSON>,
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            updated_by BIGINT,
            deleted_at TIMESTAMP(6)
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS users_sso_id_uidx ON
    users(sso_id);

CREATE
    INDEX IF NOT EXISTS users_deleted_at_email_uidx ON
    users(
        deleted_at,
        email
    );

CREATE
    UNIQUE INDEX IF NOT EXISTS users_realm_id_email_uidx ON
    users(
        realm_id,
        email
    );

CREATE
    UNIQUE INDEX IF NOT EXISTS users_realm_id_sso_id_uidx ON
    users(
        realm_id,
        sso_id
    );

CREATE
    TABLE
        IF NOT EXISTS user_permissions(
            id BIGSERIAL NOT NULL CONSTRAINT user_permissions_pk PRIMARY KEY,
            user_id BIGINT NOT NULL,
            tenant_id BIGINT NOT NULL,
            user_role_id BIGINT NOT NULL,
            user_permission_status VARCHAR(20) NOT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            created_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            updated_by BIGINT NULL,
            CONSTRAINT user_permissions_users_user_id_fk FOREIGN KEY(user_id) REFERENCES users(id)
        );

CREATE
    INDEX IF NOT EXISTS user_permissions_user_id_tenant_id_user_role_id_idx ON
    user_permissions(
        user_id,
        user_role_id,
        tenant_id
    );

CREATE
    TABLE
        IF NOT EXISTS user_invitations(
            id BIGSERIAL NOT NULL CONSTRAINT user_invitations_pk PRIMARY KEY,
            invitation_type VARCHAR(50) NOT NULL,
            user_id BIGINT NOT NULL,
            tenant_id BIGINT NOT NULL,
            realm_id VARCHAR(50) NOT NULL,
            email VARCHAR(50) COLLATE email_case_insensitive_unicode NOT NULL,
            invitation_content JSONB NULL,
            expired_at TIMESTAMP(6),
            created_at TIMESTAMP(6) DEFAULT NOW(),
            created_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            updated_by BIGINT NULL,
            CONSTRAINT user_invitations_users_user_id_fk FOREIGN KEY(user_id) REFERENCES users(id)
        );

CREATE
    INDEX IF NOT EXISTS user_invitations_tenant_id_idx ON
    user_invitations(
        tenant_id,
        invitation_type,
        user_id,
        email
    );

CREATE
    INDEX IF NOT EXISTS user_invitations_realm_id_idx ON
    user_invitations(
        realm_id,
        invitation_type,
        user_id,
        email
    );
