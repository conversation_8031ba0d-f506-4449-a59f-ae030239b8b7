/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.user.shared.http.users.request.FetchRuleRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserMeRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface UserProfileApi {

	@PutMapping(path = "/api/user/users/profile")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	UserResponse updateUserProfileMe(@RequestBody @Valid UpdateUserMeRequest request);

	@GetMapping(path = "/api/user/users/{userId}/profile")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	UserResponse getUserProfile(@PathVariable("userId") Long userId,
			@SpringQueryMap @ModelAttribute @Valid FetchRuleRequest request);

}
