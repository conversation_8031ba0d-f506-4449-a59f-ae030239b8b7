/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.preferences.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.user.shared.constants.UserPreferenceKeyConstants;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(UserPreferenceKeyConstants.SECURITY_MFA)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateSecurityMfaRequest extends CreateUserPreferenceRequest {

	private final Boolean isEnabledEmailOtp;

	@JsonCreator
	@Builder
	public CreateSecurityMfaRequest(@JsonProperty("isEnabledEmailOtp") Boolean isEnabledEmailOtp) {
		super(UserPreferenceKey.SECURITY_MFA);
		this.isEnabledEmailOtp = isEnabledEmailOtp;
	}
}
