/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.users.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

@Getter
@Builder
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActivateUserInternalRequest {
	@Email
	@NotEmpty
	@Length(max = 255)
	private String email;

	@NotEmpty
	private String realmId;

	@NotEmpty
	private String ssoId;

	@NotNull
	private Long tenantId;

	@Length(max = 120)
	private String firstName;

	@Length(max = 120)
	private String lastName;

	private String invitationCode;

	private String trustedSource;

	//default: Not Allow for auto-create user
	private boolean isAllowedUserCreation;
}