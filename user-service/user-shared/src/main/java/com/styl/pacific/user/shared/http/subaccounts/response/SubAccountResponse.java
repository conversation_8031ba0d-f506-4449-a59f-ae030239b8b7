/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.subaccounts.response;

import com.styl.pacific.user.shared.enums.UserSubAccountStatus;
import com.styl.pacific.user.shared.http.users.response.UserLiteResponse;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class SubAccountResponse {
	private final String id;
	private final UserLiteResponse sponsorUser;
	private final UserLiteResponse subUser;
	private final UserSubAccountStatus subAccountStatus;
	private final Instant createdAt;
	private final Instant updatedAt;
}
