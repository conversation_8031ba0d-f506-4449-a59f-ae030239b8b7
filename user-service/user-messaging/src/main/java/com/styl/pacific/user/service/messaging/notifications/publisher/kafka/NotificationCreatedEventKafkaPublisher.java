/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.messaging.notifications.publisher.kafka;

import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.user.service.messaging.notifications.config.NotificationKafkaPublisherConfigProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecordBase;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class NotificationCreatedEventKafkaPublisher {

	private final KafkaProducer<Serializable, SpecificRecordBase> kafkaProducer;
	private final NotificationKafkaPublisherConfigProperties kafkaPublisherConfigProperties;

	@Async
	public void publish(NotificationCreatedAvroEvent event) {
		kafkaProducer.send(kafkaPublisherConfigProperties.getNotificationCreatedEvent()
				.getTopicName(), event.getId(), event, (result, error) -> {
					if (error == null) {
						log.info("Sent NotificationCreatedAvroEvent with eventId: {}, tenantId: {}", event.getId(),
								event.getTenantId());
					} else {
						log.error("Error in sending message to topic: [{}] with eventId: {} and tenantId: {}",
								kafkaPublisherConfigProperties.getNotificationCreatedEvent()
										.getTopicName(), event.getId(), event.getTenantId());
						log.error(error.getMessage(), error);
					}
				});
	}

}
