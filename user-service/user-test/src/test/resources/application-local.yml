logging:
  level:
    com.styl.pacific: DEBUG
    org.hibernate.SQL: TRACE
    org.hibernate.orm.jdbc.bind: TRACE
    org.hibernate.stat: TRACE
    org.hibernate.SQL_SLOW: TRACE

---
server:
  port: 9202

spring:
  application:
    name: user-service
  jpa:
    open-in-view: true
    show-sql: true
    database-platform: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
    properties:
      hibernate:
        dialect: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
        jdbc:
          time_zone: UTC
          batch_size: 50
        format_sql: false
        generate_statistics: false
        order_inserts: true
        order_updates: true
        query:
          fail_on_pagination_over_collection_fetch: true
          in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: *******************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    locations: classpath:db/migration
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 5000
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            requestInterceptors:
              - com.styl.pacific.common.feign.interceptor.PlatformFeignHeaderForwarderInterceptor
  sql:
    init:
      platform: postgres
pacific:
  tracing:
    enabled: true
    otlp:
      endpoint: http://localhost:4317
  aws:
    s3:
      endpoint: http://localhost:9000
      region: ap-southeast-1
      accessKey: test
      secretKey: test
  kafka:
    consumers:
      user-service:
        keycloak-user-event:
          enabled: true
          group-id: user-service-keycloak-user-event
          topic-name: keycloak-user-event
        tenant-created-event:
          enabled: true
          group-id: user-service-tenant-created-event
          topic-name: tenant-service-tenant-created-event
  clients:
    tenant-service:
      url: http://localhost:9201
    authz-service:
      url: http://localhost:9209
  users:
    default-system-user:
      id: 0
      email: <EMAIL>
      sid: 21321321
    invitations:
      register-expiry:
        default-expiry: PT48H
  keycloak:
    keycloakUrl: http://localhost:8280
    customer-register-link-pattern: |
      ${KEYCLOAK_URL}/realms/${REALM_ID}/protocol/openid-connect/registrations
      ?client_id=customer-portal
      &response_type=code
      &scope=openid
    backoffice-register-link-pattern: |
      ${KEYCLOAK_URL}/realms/${REALM_ID}/protocol/openid-connect/registrations
      ?client_id=bo-portal
      &response_type=code
      &scope=openid
    default-back-office-redirect-url: https://google.com
    default-customer-redirect-url: https://google.com

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: true
  auto-startup: true
  concurrency-level: 3
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150
