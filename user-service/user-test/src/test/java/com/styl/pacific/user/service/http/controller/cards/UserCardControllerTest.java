/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.http.controller.cards;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserCardId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.api.cards.UserCardController;
import com.styl.pacific.user.service.config.MvcTestConfiguration;
import com.styl.pacific.user.service.core.features.cards.UserCardCommandService;
import com.styl.pacific.user.service.core.features.cards.UserCardQueryService;
import com.styl.pacific.user.service.core.features.cards.entities.UserCard;
import com.styl.pacific.user.service.core.features.cards.request.CreateUserCardCommand;
import com.styl.pacific.user.service.core.features.cards.request.UpdateUserCardCommand;
import com.styl.pacific.user.service.core.features.cards.request.UserCardPaginationQuery;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.shared.enums.UserCardStatus;
import com.styl.pacific.user.shared.http.cards.request.CreateCardRequest;
import com.styl.pacific.user.shared.http.cards.request.FilterUserCardRequest;
import com.styl.pacific.user.shared.http.cards.request.QueryUserCardPaginationRequest;
import com.styl.pacific.user.shared.http.cards.request.UpdateCardRequest;
import com.styl.pacific.user.shared.http.cards.response.UserCardResponse;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.LinkedMultiValueMap;

@DirtiesContext
@WebMvcTest(UserCardController.class)
@Import(value = { UserCardController.class, PresignerConfiguration.class, S3ConfigProperties.class,
		PresignerContextProvider.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class UserCardControllerTest {

	@MockitoBean
	private UserCardQueryService userCardQueryService;

	@MockitoBean
	private UserCardCommandService userCardCommandService;

	@MockitoBean
	private UserSubAccountQueryService userSubAccountQueryService;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@Test
	void testCreateUserCardWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();
		final var userCard = UserCard.builder()
				.id(new UserCardId(1L))
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.user(User.builder()
						.id(new UserId(1L))
						.build())
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userCardResponse = UserCardResponse.builder()
				.id("1")
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var request = CreateCardRequest.builder()
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.build();

		when(userCardCommandService.createUserCard(any(CreateUserCardCommand.class))).thenReturn(userCard);

		// Act & Assert
		mockMvc.perform(post("/api/user/users/{userId}/cards", 1L).contentType(MediaType.APPLICATION_JSON)
				.headers(HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.tenantId(1L)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.build()))
				.content(objectMapper.writeValueAsBytes(request)))

				.andExpect(status().isCreated())
				.andExpect(content().json(objectMapper.writeValueAsString(userCardResponse)));
	}

	@Test
	void testUpdateUserCardWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();
		final var userCard = UserCard.builder()
				.id(new UserCardId(1L))
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.user(User.builder()
						.id(new UserId(1L))
						.build())
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userCardResponse = UserCardResponse.builder()
				.id("1")
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var request = UpdateCardRequest.builder()
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.build();

		final var captor = ArgumentCaptor.forClass(UpdateUserCardCommand.class);

		when(userCardCommandService.updateUserCard(any(UpdateUserCardCommand.class))).thenReturn(userCard);

		// Act & Assert
		mockMvc.perform(put("/api/user/users/{userId}/cards/{id}", 1L, 1L).contentType(MediaType.APPLICATION_JSON)
				.headers(HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.tenantId(1L)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.build()))
				.content(objectMapper.writeValueAsBytes(request)))

				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(userCardResponse)));

		verify(userCardCommandService, times(1)).updateUserCard(captor.capture());

		final var actualCommand = captor.getValue();

		Assertions.assertEquals("Card Alias 0001", actualCommand.getCardAlias());
		Assertions.assertEquals(new UserCardId(1L), actualCommand.getId());
		Assertions.assertEquals(new UserId(1L), actualCommand.getUserId());
		Assertions.assertEquals(UserCardStatus.ACTIVE, actualCommand.getStatus());
	}

	@Test
	void testGetCardByCardIdWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();
		final var userCard = UserCard.builder()
				.id(new UserCardId(1L))
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.user(User.builder()
						.id(new UserId(1L))
						.build())
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userCardResponse = UserCardResponse.builder()
				.id("1")
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userIdCaptor = ArgumentCaptor.forClass(UserId.class);
		final var cardIdCaptor = ArgumentCaptor.forClass(String.class);

		when(userCardQueryService.getUserCardByUserIdAndTenantIdAndCardId(any(UserId.class), any(TenantId.class), any(
				String.class))).thenReturn(userCard);

		// Act & Assert
		mockMvc.perform(get("/api/user/users/{userId}/cards/card-id/{cardId}", 1L, "card-id-0001").contentType(
				MediaType.APPLICATION_JSON)
				.headers(HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.tenantId(1L)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.build())))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(userCardResponse)));

		verify(userCardQueryService, times(1)).getUserCardByUserIdAndTenantIdAndCardId(userIdCaptor.capture(), any(
				TenantId.class), cardIdCaptor.capture());

		Assertions.assertEquals("card-id-0001", cardIdCaptor.getValue());
		Assertions.assertEquals(new UserId(1L), userIdCaptor.getValue());
	}

	@Test
	void testGetCardByIdWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();
		final var userCard = UserCard.builder()
				.id(new UserCardId(1L))
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.user(User.builder()
						.id(new UserId(1L))
						.build())
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userCardResponse = UserCardResponse.builder()
				.id("1")
				.cardId("card-id-0001")
				.cardAlias("Card Alias 0001")
				.status(UserCardStatus.ACTIVE)
				.createdAt(now)
				.updatedAt(now)
				.build();

		final var userIdCaptor = ArgumentCaptor.forClass(UserId.class);
		final var cardIdCaptor = ArgumentCaptor.forClass(UserCardId.class);

		when(userCardQueryService.getUserCardByUserIdAndId(any(UserId.class), any(UserCardId.class))).thenReturn(
				userCard);

		// Act & Assert
		mockMvc.perform(get("/api/user/users/{userId}/cards/{id}", 1L, 1L).contentType(MediaType.APPLICATION_JSON)
				.headers(HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.tenantId(1L)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.build())))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(userCardResponse)));

		verify(userCardQueryService, times(1)).getUserCardByUserIdAndId(userIdCaptor.capture(), cardIdCaptor.capture());

		Assertions.assertEquals(new UserCardId(1L), cardIdCaptor.getValue());
		Assertions.assertEquals(new UserId(1L), userIdCaptor.getValue());
	}

	@Test
	void testQueryUserCardsWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		final var pageResult = Paging.<UserCard>builder()
				.page(0)
				.totalPages(10)
				.content(List.of(UserCard.builder()
						.id(new UserCardId(1L))
						.cardId("card-id-0001")
						.cardAlias("Card Alias 0001")
						.status(UserCardStatus.ACTIVE)
						.createdAt(now)
						.updatedAt(now)
						.build()))
				.build();

		final var pageResponse = Paging.<UserCardResponse>builder()
				.page(0)
				.totalPages(10)
				.content(List.of(UserCardResponse.builder()
						.id("1")
						.cardId("card-id-0001")
						.cardAlias("Card Alias 0001")
						.status(UserCardStatus.ACTIVE)
						.createdAt(now)
						.updatedAt(now)
						.build()))
				.build();

		final var request = QueryUserCardPaginationRequest.builder()
				.filter(FilterUserCardRequest.builder()
						.build())
				.size(10)
				.page(0)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build();

		when(userCardQueryService.queryUserCards(any())).thenReturn(pageResult);

		final Map<String, String> map = new HashMap<>();

		map.put("size", String.valueOf(request.getSize()));
		map.put("page", String.valueOf(request.getPage()));
		map.put("sortDirection", request.getSortDirection());
		map.put("sortBy", request.getSortFields()
				.stream()
				.findFirst()
				.orElse(null));

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(map.entrySet()
				.stream()
				.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		mockMvc.perform(get("/api/user/users/{userId}/cards", 1L).queryParams(requestParams)
				.headers(HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(1L)
						.build())))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(pageResponse)));

		final var captor = ArgumentCaptor.forClass(UserCardPaginationQuery.class);
		verify(userCardQueryService, times(1)).queryUserCards(captor.capture());
		final var actual = captor.getValue();
		Assertions.assertEquals(new UserId(1L), actual.getFilter()
				.getByUserId());
	}

}
