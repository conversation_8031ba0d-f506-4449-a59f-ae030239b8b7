/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.http.controller.userinternal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.authz.shared.http.responses.UserRoleResponse;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.enums.DefaultSystemRole;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserRoleId;
import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.user.service.config.IntegrationTestConfiguration;
import com.styl.pacific.user.service.config.UserInitializationIntegrationSupporter;
import com.styl.pacific.user.service.config.UserIntegrationTestContainer;
import com.styl.pacific.user.service.core.features.alternativeaccess.AlternativeUserAccessCommandService;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.CreateAlternativeUserAccessCommand;
import com.styl.pacific.user.service.core.features.customers.CustomerCommandService;
import com.styl.pacific.user.service.core.features.groups.UserGroupCommandService;
import com.styl.pacific.user.service.core.features.groups.constants.UserGroupConstants;
import com.styl.pacific.user.service.core.features.groups.request.UpsertUserGroupCommand;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationQueryService;
import com.styl.pacific.user.service.core.features.invitations.entities.UserInvitationContentCode;
import com.styl.pacific.user.service.core.features.invitations.request.GetUserInvitationQuery;
import com.styl.pacific.user.service.core.features.notifications.request.ControlNotificationCommand;
import com.styl.pacific.user.service.core.features.permissions.entities.UserPermission;
import com.styl.pacific.user.service.core.features.preferences.UserPreferenceCommandService;
import com.styl.pacific.user.service.core.features.preferences.entities.SecurityMfaPreferenceData;
import com.styl.pacific.user.service.core.features.preferences.request.CreateUserPreferenceCommand;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountCommandService;
import com.styl.pacific.user.service.core.features.subaccounts.request.CreateSubAccountNewUserCommand;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.UserQueryService;
import com.styl.pacific.user.service.core.features.users.mapper.UserMappingFunctionSupport;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserCommand;
import com.styl.pacific.user.service.core.features.users.request.UpdateUserPermissionCommand;
import com.styl.pacific.user.service.core.features.users.request.UpsertTenantUserCommand;
import com.styl.pacific.user.service.data.access.clients.catalogs.AllergenClient;
import com.styl.pacific.user.service.data.access.clients.keycloak.KeycloakServiceAccountClient;
import com.styl.pacific.user.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.user.service.data.access.clients.userroles.UserRoleClient;
import com.styl.pacific.user.service.messaging.notifications.publisher.kafka.NotificationCreatedEventKafkaPublisher;
import com.styl.pacific.user.service.messaging.users.publisher.kafka.UserCreatedEventKafkaPublisher;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import com.styl.pacific.user.shared.enums.UserPermissionStatus;
import com.styl.pacific.user.shared.enums.UserPreferenceGroup;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.exceptions.UserInvitationNotFoundException;
import com.styl.pacific.user.shared.http.alternativecustomers.response.FullAlternativeUserAccessResponse;
import com.styl.pacific.user.shared.http.invitations.response.UserInvitationContentResponseCode;
import com.styl.pacific.user.shared.http.invitations.response.UserInvitationResponse;
import com.styl.pacific.user.shared.http.preferences.response.SecurityMfaPreferenceDataResponse;
import com.styl.pacific.user.shared.http.preferences.response.UserPreferenceResponse;
import com.styl.pacific.user.shared.http.users.request.ActivateUserInternalRequest;
import com.styl.pacific.user.shared.http.users.response.UserAccessResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.LinkedMultiValueMap;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class UserInternalControllerIntegrationTest extends BaseWebClientWithDbTest implements UserIntegrationTestContainer {

	@MockitoBean
	private UserRoleClient userRoleClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoBean
	private UserCreatedEventKafkaPublisher userCreatedEventKafkaPublisher;

	@MockitoBean
	private NotificationCreatedEventKafkaPublisher notificationCreatedEventKafkaPublisher;

	@MockitoBean
	private KeycloakServiceAccountClient keycloakServiceAccountClient;

	@MockitoBean
	private AllergenClient allergenClient;

	@Autowired
	private UserCommandService userCommandService;

	@Autowired
	private UserInvitationQueryService userInvitationQueryService;

	@Autowired
	private CustomerCommandService customerCommandService;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private UserSubAccountCommandService subAccountCommandService;

	@Autowired
	private UserQueryService userQueryService;

	@Autowired
	private UserPreferenceCommandService userPreferenceCommandService;

	@Autowired
	private AlternativeUserAccessCommandService alternativeUserAccessCommandService;

	@Autowired
	private UserGroupCommandService userGroupCommandService;

	@Value("${pacific.platform.brand-name}")
	private String platformBrandName;

	@Value("${pacific.keycloak.default-back-office-redirect-url}")
	private String backOfficeUserUrl;

	private UserInitializationIntegrationSupporter userSupporter;

	@BeforeEach
	void setUp() {
		userSupporter = new UserInitializationIntegrationSupporter(userRoleClient, tenantClient, userCommandService);
	}

	@Test
	void testGetCustomerAccessWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.ssoId("user-********")
				.userStatus(UserStatus.ACTIVE)
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-rl-001")
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/internal/user/users/access/{realmId}/{ssoId}", "customer-rl-001", user.getSsoId())
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserAccessResponse.class)
				.consumeWith(response -> {
					final var actualUserAccessResponse = response.getResponseBody();
					assertNotNull(actualUserAccessResponse);
					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(actualUserAccessResponse.getUser()
									.getId()));
					Assertions.assertEquals(user.getRealmId(), actualUserAccessResponse.getUser()
							.getRealmId());
					Assertions.assertEquals(user.getUserType(), actualUserAccessResponse.getUser()
							.getUserType());
					Assertions.assertEquals(user.getUserStatus(), actualUserAccessResponse.getUser()
							.getUserStatus());
					Assertions.assertEquals(user.getEmail(), actualUserAccessResponse.getUser()
							.getEmail());
					Assertions.assertEquals(user.getFirstName(), actualUserAccessResponse.getUser()
							.getFirstName());
					Assertions.assertEquals(user.getLastName(), actualUserAccessResponse.getUser()
							.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), actualUserAccessResponse.getUser()
									.getFullName());

					Assertions.assertEquals(user.getSsoId(), actualUserAccessResponse.getUser()
							.getSsoId());

					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(actualUserAccessResponse.getClaim()
									.getUserId()));
					Assertions.assertEquals(user.getUserType(), actualUserAccessResponse.getClaim()
							.getUserType());
					Assertions.assertEquals(1, actualUserAccessResponse.getClaim()
							.getPermissions()
							.size());
					final var permission = actualUserAccessResponse.getClaim()
							.getPermissions()
							.stream()
							.findFirst()
							.get();
					Assertions.assertEquals("1", permission.getTenantId());
					Assertions.assertEquals("1", permission.getUserRoleId());
				});
	}

	@Test
	void testThrowForbiddenCustomerAccessWhenUserIsNotActive() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.ssoId("user-40060001")
				.userStatus(UserStatus.ACTIVE)
				.permissionStatus(UserPermissionStatus.ACTIVE)
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-rl-001")
				.build());

		userSupporter.updateUser(user.withUserStatus(UserStatus.ARCHIVED));

		// Act & Assert
		webClient.get()
				.uri("/api/internal/user/users/access/{realmId}/{ssoId}", "customer-rl-001", user.getSsoId())
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isForbidden()
				.expectBody(ErrorResponse.class)
				.consumeWith(errorResponse -> {
					final var bodyResponse = errorResponse.getResponseBody();
					Assertions.assertNotNull(bodyResponse);
					Assertions.assertEquals(GlobalErrorCode.FORBIDDEN.getValue(), bodyResponse.getCode());
				});
	}

	@Test
	void testGetBackOfficeAccessWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.activateUser("user-40050002", userSupporter.initializeNewUser(
				UpsertTenantUserCommand.builder()
						.userRoleId(new UserRoleId(1L))
						.tenantId(new TenantId(1L))
						.userType(UserType.BACK_OFFICE)
						.email("<EMAIL>")
						.firstName("first name")
						.lastName("last name")
						.phoneNumber("**********")
						.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
						.build()));
		user.getPermissions()
				.add(UserPermission.builder()
						.permissionStatus(UserPermissionStatus.CREATED)
						.userRoleId(new UserRoleId(2L))
						.tenantId(new TenantId(2L))
						.build());

		userSupporter.updateUser(user);

		// Act & Assert
		webClient.get()
				.uri("/api/internal/user/users/access/{realmId}/{ssoId}", CommonDomainConstants.BACK_OFFICE_REALM_ID,
						user.getSsoId())
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserAccessResponse.class)
				.consumeWith(response -> {
					final var actualUserAccessResponse = response.getResponseBody();
					assertNotNull(actualUserAccessResponse);
					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(actualUserAccessResponse.getUser()
									.getId()));
					Assertions.assertEquals(user.getRealmId(), actualUserAccessResponse.getUser()
							.getRealmId());
					Assertions.assertEquals(user.getUserType(), actualUserAccessResponse.getUser()
							.getUserType());
					Assertions.assertEquals(user.getUserStatus(), actualUserAccessResponse.getUser()
							.getUserStatus());
					Assertions.assertEquals(user.getEmail(), actualUserAccessResponse.getUser()
							.getEmail());
					Assertions.assertEquals(user.getFirstName(), actualUserAccessResponse.getUser()
							.getFirstName());
					Assertions.assertEquals(user.getLastName(), actualUserAccessResponse.getUser()
							.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), actualUserAccessResponse.getUser()
									.getFullName());

					Assertions.assertEquals("user-40050002", actualUserAccessResponse.getUser()
							.getSsoId());

					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(actualUserAccessResponse.getClaim()
									.getUserId()));
					Assertions.assertEquals(user.getUserType(), actualUserAccessResponse.getClaim()
							.getUserType());
					Assertions.assertEquals(2, actualUserAccessResponse.getClaim()
							.getPermissions()
							.size());
					final var permission1 = actualUserAccessResponse.getClaim()
							.getPermissions()
							.stream()
							.filter(it -> it.getTenantId()
									.equals("1"))
							.findFirst()
							.get();
					Assertions.assertEquals("1", permission1.getTenantId());
					Assertions.assertEquals("1", permission1.getUserRoleId());

					final var permission2 = actualUserAccessResponse.getClaim()
							.getPermissions()
							.stream()
							.filter(it -> it.getTenantId()
									.equals("2"))
							.findFirst()
							.get();
					Assertions.assertEquals("2", permission2.getTenantId());
					Assertions.assertEquals("2", permission2.getUserRoleId());
				});
	}

	@Test
	void testGetTenantUserInvitationWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var userInvitation = userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.byUserId(user.getId())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.byType(UserInvitationType.USER_REGISTRATION)
				.build());

		final Map<String, String> map = new HashMap<>();
		map.put("byContentCode", ((UserInvitationContentCode) userInvitation.getContent()).getCode());

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(map.entrySet()
				.stream()
				.filter(it -> StringUtils.isNotBlank(it.getValue()))
				.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/internal/user/invitations/single/pacific-backoffice/query")
							.queryParams(requestParams);
					return uriBuilder.build();
				})
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserInvitationResponse.class)
				.consumeWith(response -> {
					final var userInvitationResponse = response.getResponseBody();
					assertNotNull(userInvitationResponse);
					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(userInvitationResponse.getUser()
									.getId()));
					Assertions.assertEquals(user.getRealmId(), userInvitationResponse.getUser()
							.getRealmId());
					Assertions.assertEquals(user.getUserType(), userInvitationResponse.getUser()
							.getUserType());
					Assertions.assertEquals(user.getUserStatus(), userInvitationResponse.getUser()
							.getUserStatus());
					Assertions.assertEquals(user.getEmail(), userInvitationResponse.getUser()
							.getEmail());
					Assertions.assertEquals(user.getFirstName(), userInvitationResponse.getUser()
							.getFirstName());
					Assertions.assertEquals(user.getLastName(), userInvitationResponse.getUser()
							.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userInvitationResponse.getUser()
									.getFullName());

					Assertions.assertNotNull(userInvitationResponse.getContent());
					Assertions.assertEquals(((UserInvitationContentCode) userInvitation.getContent()).getCode(),
							((UserInvitationContentResponseCode) userInvitationResponse.getContent()).getCode());

				});
	}

	@Test
	void testActivateBackOfficeUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.BACK_OFFICE)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.build());

		final var userInvitation = userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.byUserId(user.getId())
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.byType(UserInvitationType.USER_REGISTRATION)
				.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("1234567")
				.email(user.getEmail())
				.firstName("first name")
				.lastName("last name")
				.tenantId(1L)
				.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
				.invitationCode(((UserInvitationContentCode) userInvitation.getContent()).getCode())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(userResponse.getId()));
					Assertions.assertEquals(user.getRealmId(), userResponse.getRealmId());
					Assertions.assertEquals(user.getUserType(), userResponse.getUserType());
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(user.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(user.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());

					Assertions.assertEquals("1234567", userResponse.getSsoId());

					Assertions.assertThrowsExactly(UserInvitationNotFoundException.class,
							() -> userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
									.byContentCode(((UserInvitationContentCode) userInvitation.getContent()).getCode())
									.realmId(CommonDomainConstants.BACK_OFFICE_REALM_ID)
									.build()));

					verify(notificationCreatedEventKafkaPublisher, times(2)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals("WELCOME_USER", notificationEvent.getAction());
					Assertions.assertEquals(user.getId()
							.getValue(), notificationEvent.getUserId());
					Assertions.assertEquals(List.of(NotificationAvroChannel.EMAIL, NotificationAvroChannel.IN_APP),
							notificationEvent.getChannels());
					Assertions.assertEquals(user.getFirstName(), notificationEvent.getData()
							.get("userName"));
					Assertions.assertEquals(backOfficeUserUrl, notificationEvent.getData()
							.get("loginUrl"));
					Assertions.assertEquals(platformBrandName, notificationEvent.getData()
							.get("systemName"));
					Assertions.assertEquals(user.getEmail(), notificationEvent.getData()
							.get("to"));

				});
	}

	@Test
	void testActivateCustomerUserWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var customerRealmId = "customerRealmId-11000";
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(customerRealmId)
				.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var userInvitation = userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.byUserId(user.getId())
				.realmId(customerRealmId)
				.byType(UserInvitationType.USER_REGISTRATION)
				.build());

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-40070001-1234567")
				.email(user.getEmail())
				.firstName("first name")
				.lastName("last name")
				.tenantId(1L)
				.realmId(customerRealmId)
				.invitationCode(((UserInvitationContentCode) userInvitation.getContent()).getCode())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(userResponse.getId()));
					Assertions.assertEquals(user.getRealmId(), userResponse.getRealmId());
					Assertions.assertEquals(user.getUserType(), userResponse.getUserType());
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(user.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(user.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(user.getLastName(), userResponse.getLastName());

					Assertions.assertEquals(user.getUserGroup()
							.getId()
							.getValue()
							.toString(), userResponse.getUserGroup()
									.getId());

					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());

					Assertions.assertEquals("ssoId-40070001-1234567", userResponse.getSsoId());

					verify(notificationCreatedEventKafkaPublisher, times(2)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals("WELCOME_USER", notificationEvent.getAction());
					Assertions.assertEquals(user.getId()
							.getValue(), notificationEvent.getUserId());
					Assertions.assertEquals(List.of(NotificationAvroChannel.EMAIL, NotificationAvroChannel.IN_APP),
							notificationEvent.getChannels());
					Assertions.assertEquals(user.getFirstName(), notificationEvent.getData()
							.get("userName"));
					Assertions.assertEquals("pacific.domain.sg", notificationEvent.getData()
							.get("loginUrl"));
					Assertions.assertEquals(platformBrandName, notificationEvent.getData()
							.get("systemName"));
					Assertions.assertEquals(user.getEmail(), notificationEvent.getData()
							.get("to"));
				});
	}

	@Test
	void testActivateSelfRegistrationCustomerUserWhenValidRequestThenReturnSuccessfully() throws URISyntaxException {
		// Arrange
		final var customerRealmId = "customerRealmId-12000";

		when(tenantClient.getTenant(1L)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(1L)
				.realmId(customerRealmId)
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.defaultDomain("pacific.domain.sg")
						.build())
				.build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), 1L, true)).thenReturn(
				UserRoleResponse.builder()
						.tenantId("1")
						.id("1")
						.build());

		final var invitation = customerCommandService.generateSelfRegistrationAccess(new TenantId(1L),
				"http://styl.sg");

		final var uri = new URI(invitation.getLink());

		final var params = uri.getQuery()
				.lines()
				.map(s -> s.split("="))
				.collect(Collectors.toMap(a -> a[0], a -> a.length > 1 ? a[1] : ""));
		final var invitationCode = params.get("invitationCode");

		final var userInvitation = userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.byContentCode(invitationCode)
				.realmId(customerRealmId)
				.byType(UserInvitationType.USER_SELF_REGISTRATION)
				.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-12000-1234567")
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.tenantId(1L)
				.realmId(customerRealmId)
				.invitationCode(((UserInvitationContentCode) userInvitation.getContent()).getCode())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals("ssoId-12000-1234567", userResponse.getSsoId());
					Assertions.assertEquals(customerRealmId, userResponse.getRealmId());
					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey(), userResponse
							.getUserGroup()
							.getGroupKey());

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals("WELCOME_USER", notificationEvent.getAction());
					Assertions.assertEquals(userResponse.getId(), notificationEvent.getUserId()
							.toString());
					Assertions.assertEquals(List.of(NotificationAvroChannel.EMAIL, NotificationAvroChannel.IN_APP),
							notificationEvent.getChannels());
					Assertions.assertEquals(userResponse.getFirstName(), notificationEvent.getData()
							.get("userName"));
					Assertions.assertEquals("pacific.domain.sg", notificationEvent.getData()
							.get("loginUrl"));
					Assertions.assertEquals(platformBrandName, notificationEvent.getData()
							.get("systemName"));
					Assertions.assertEquals(userResponse.getEmail(), notificationEvent.getData()
							.get("to"));
				});
	}

	@Test
	void testActivateCustomerUserInCaseSubAccountWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var sponsorUser = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-rl-11112")
				.build());

		final var subAccount1 = subAccountCommandService.createSubAccount(CreateSubAccountNewUserCommand.builder()
				.sponsorUserId(sponsorUser.getId())
				.firstName("first name 2")
				.lastName("last name 2")
				.phoneNumber("**********")
				.tenantId(new TenantId(1L))
				.userTypeCreator(UserType.CUSTOMER)
				.build());

		final var subAccountUser = userQueryService.getSingleUserById(subAccount1.getSubUser()
				.getId())
				.get();

		final var updatedSubAccountUser = userCommandService.updateSingleUser(UpdateUserCommand.builder()
				.id(subAccountUser.getId())
				.email(subAccountUser.getEmail())
				.userGroupId(subAccountUser.getUserGroup()
						.getId())
				.userStatus(subAccountUser.getUserStatus())
				.phoneNumber(subAccountUser.getPhoneNumber())
				.firstName(subAccountUser.getFirstName())
				.lastName(subAccountUser.getLastName())
				.email("<EMAIL>")
				.updatePermissions(subAccountUser.getPermissions()
						.stream()
						.map(it -> UpdateUserPermissionCommand.builder()
								.userRoleId(it.getUserRoleId())
								.permissionStatus(it.getPermissionStatus())
								.tenantId(it.getTenantId())
								.build())
						.toList())
				.build());

		final var userInvitation = userInvitationQueryService.getTenantUserInvitation(GetUserInvitationQuery.builder()
				.byUserId(updatedSubAccountUser.getId())
				.realmId("customer-rl-11112")
				.byType(UserInvitationType.USER_REGISTRATION)
				.build());

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-11111-1234567")
				.email(updatedSubAccountUser.getEmail())
				.firstName("first name")
				.lastName("last name")
				.tenantId(1L)
				.realmId("customer-rl-11112")
				.invitationCode(((UserInvitationContentCode) userInvitation.getContent()).getCode())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(updatedSubAccountUser.getId()
							.getValue(), Long.valueOf(userResponse.getId()));
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals("ssoId-11111-1234567", userResponse.getSsoId());
					Assertions.assertEquals("customer-rl-11112", userResponse.getRealmId());
					Assertions.assertEquals(updatedSubAccountUser.getUserGroup()
							.getId()
							.getValue()
							.toString(), userResponse.getUserGroup()
									.getId());
					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());

				});
	}

	@Test
	void testActivateNewCustomerWhenRegisteringViaTrustedSourceAndAllowedUserCreationThenReturnSuccessfully() {
		// Arrange
		final var customerRealmId = "customerRealmId-29000";
		final var tenantId = 2900L;
		final var trustedSource = "GOOGLE";

		when(tenantClient.getTenant(tenantId)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.defaultDomain("pacific.domain.sg")
						.build())
				.build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), tenantId, true))
				.thenReturn(UserRoleResponse.builder()
						.tenantId("" + tenantId)
						.id("2901")
						.build());

		final var notificationEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent.class);

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-29000-1234567")
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.trustedSource(trustedSource)
				.isAllowedUserCreation(true)
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getSsoId(), userResponse.getSsoId());
					Assertions.assertEquals(request.getTrustedSource(), userResponse.getTrustedSource());
					Assertions.assertEquals(customerRealmId, userResponse.getRealmId());
					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(Boolean.TRUE, userResponse.getIsTrustedSourceAccess());
					Assertions.assertEquals(UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey(), userResponse
							.getUserGroup()
							.getGroupKey());

					verify(notificationCreatedEventKafkaPublisher, times(1)).publish(notificationEventCaptor.capture());
					final var notificationEvent = notificationEventCaptor.getValue();
					Assertions.assertEquals("WELCOME_USER", notificationEvent.getAction());
					Assertions.assertEquals(userResponse.getId(), notificationEvent.getUserId()
							.toString());
					Assertions.assertEquals(List.of(NotificationAvroChannel.EMAIL, NotificationAvroChannel.IN_APP),
							notificationEvent.getChannels());
					Assertions.assertEquals(userResponse.getFirstName(), notificationEvent.getData()
							.get("userName"));
					Assertions.assertEquals("pacific.domain.sg", notificationEvent.getData()
							.get("loginUrl"));
					Assertions.assertEquals(platformBrandName, notificationEvent.getData()
							.get("systemName"));
					Assertions.assertEquals(userResponse.getEmail(), notificationEvent.getData()
							.get("to"));
				});
	}

	@Test
	void testThrowExceptionWhenActivateNewCustomerWhenRegisteringViaTrustedSourceWithoutAllowedUserCreationThenReturnSuccessfully() {
		// Arrange
		final var customerRealmId = "customerRealmId-29000";
		final var tenantId = 2900L;
		final var trustedSource = "GOOGLE";

		when(tenantClient.getTenant(tenantId)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.defaultDomain("pacific.domain.sg")
						.build())
				.build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), tenantId, true))
				.thenReturn(UserRoleResponse.builder()
						.tenantId("" + tenantId)
						.id("2901")
						.build());

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-20251806-01")
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.trustedSource(trustedSource)
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var error = response.getResponseBody();
					assertNotNull(error);
					assertTrue(error.getDetails()
							.contains("User not found"));
					assertTrue(error.getMessage()
							.contains("User not found"));
				});
	}

	@Test
	void testActivateExistingCustomerWhenRegisteringViaTrustedSourceThenReturnSuccessfully() {
		// Arrange
		final var customerRealmId = "customerRealmId-29000";
		final var tenantId = 2900L;
		final var trustedSource = "GOOGLE";

		when(tenantClient.getTenant(tenantId)).thenReturn(ResponseEntity.ok(TenantResponse.builder()
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.settings(TenantSettingsResponse.builder()
						.timeZone(MapstructCommonMapper.INSTANCE.timezoneToTimeZoneResponse(TimeZone.getTimeZone(
								"Asia/Ho_Chi_Minh")))
						.dateFormat("YYYY-MM-dd")
						.timeFormat("HH:mm:ss")
						.defaultDomain("pacific.domain.sg")
						.build())
				.build()));

		when(userRoleClient.getUserRole(null, DefaultSystemRole.CUSTOMER_ROLE.getExternalId(), tenantId, true))
				.thenReturn(UserRoleResponse.builder()
						.tenantId("" + tenantId)
						.id("2901")
						.build());

		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(2901L))
				.tenantId(new TenantId(2900L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name 1")
				.lastName("last name 1")
				.phoneNumber("**********")
				.realmId(customerRealmId)
				.build());

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-29002-1234567")
				.email(user.getEmail())
				.firstName("first name")
				.lastName("last name")
				.tenantId(tenantId)
				.realmId(customerRealmId)
				.trustedSource(trustedSource)
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getSsoId(), userResponse.getSsoId());
					Assertions.assertEquals(request.getTrustedSource(), userResponse.getTrustedSource());
					Assertions.assertEquals(customerRealmId, userResponse.getRealmId());
					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
					Assertions.assertEquals(Boolean.TRUE, userResponse.getIsTrustedSourceAccess());
					Assertions.assertEquals(user.getUserGroup()
							.getId()
							.getValue()
							.toString(), userResponse.getUserGroup()
									.getId());

				});
	}

	@Test
	void testGetCustomerPreferenceKeyWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId("customer-9919")
				.build());
		userSupporter.activateUser("sso-customer-9919", user);

		final var preference = userPreferenceCommandService.createUserPreference(CreateUserPreferenceCommand.builder()
				.tenantId(new TenantId(1L))
				.userId(user.getId())
				.group(UserPreferenceGroup.SECURITY)
				.key(UserPreferenceKey.SECURITY_MFA)
				.data(SecurityMfaPreferenceData.builder()
						.isEnabledEmailOtp(true)
						.build())
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/internal/user/preferences/{realmId}/{ssoId}/key/{userPreferenceKey}", "customer-9919",
						"sso-customer-9919", UserPreferenceKey.SECURITY_MFA)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserPreferenceResponse.class)
				.consumeWith(response -> {
					final var actualUserPreference = response.getResponseBody();
					assertNotNull(actualUserPreference);

					Assertions.assertEquals(user.getId()
							.getValue()
							.toString(), actualUserPreference.getUserId());
					assertEquals(preference.getId()
							.getValue()
							.toString(), actualUserPreference.getId());
					Assertions.assertEquals(UserPreferenceKey.SECURITY_MFA, actualUserPreference.getKey());
					Assertions.assertEquals(UserPreferenceKey.SECURITY_MFA.getGroup(), actualUserPreference.getGroup());
					assertTrue(((SecurityMfaPreferenceDataResponse) actualUserPreference.getData())
							.getIsEnabledEmailOtp());
				});
	}

	@Test
	void testGetAlternativeUserAccessByRealmIdWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var realmId = "customer-9919";
		final var tenantId = new TenantId(1L);
		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(tenantId)
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(realmId)
				.build());
		userSupporter.activateUser(realmId, user);

		final var alternative1 = alternativeUserAccessCommandService.createAlternativeUserAccess(user.getId(), tenantId,
				CreateAlternativeUserAccessCommand.builder()
						.firstName("First Name")
						.lastName("Last Name")
						.email("<EMAIL>")
						.phoneNumber("**********")
						.externalId("customer-alternative-9999-02")
						.migrationId("customer-alternative-9999-02")
						.userType(UserType.CUSTOMER)
						.build(), ControlNotificationCommand.builder()
								.isSkipNotification(false)
								.build());

		final Map<String, String> map = new HashMap<>();
		map.put("byAlternativeUserId", alternative1.getAlternativeUser()
				.getId()
				.getValue()
				.toString());

		final LinkedMultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>(map.entrySet()
				.stream()
				.filter(it -> StringUtils.isNotBlank(it.getValue()))
				.collect(Collectors.toMap(Map.Entry::getKey, it -> List.of(it.getValue()))));

		// Act & Assert
		webClient.get()
				.uri((uriBuilder -> {
					uriBuilder.path("/api/internal/user/alternative-access/{realmId}/query");
					uriBuilder.queryParams(requestParams);
					return uriBuilder.build(realmId);
				}))
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(FullAlternativeUserAccessResponse.class)
				.consumeWith(response -> {
					final var alternativeUserAccessResponse = response.getResponseBody();
					assertNotNull(alternativeUserAccessResponse);

					final var userResponse = alternativeUserAccessResponse.getUser();

					Assertions.assertEquals(user.getId()
							.getValue(), Long.valueOf(userResponse.getId()));
					Assertions.assertEquals(user.getRealmId(), userResponse.getRealmId());
					Assertions.assertEquals(user.getUserType(), userResponse.getUserType());
					Assertions.assertEquals(user.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(user.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(user.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(UserMappingFunctionSupport.buildFullName(user.getFirstName(), user
							.getLastName()), userResponse.getFullName());
				});
	}

	@Test
	void testActivateCustomerUserWithoutMissingGroupIdWhenValidRequestThenReturnSuccessfully() {
		// Arrange
		final var customerRealmId = "customerRealmId-11000";
		final var school = userGroupCommandService.upsertUserGroup(UpsertUserGroupCommand.builder()
				.tenantId(new TenantId(1L))
				.groupName("School AAAA")
				.description("School AAAA")
				.build());
		final var grade = userGroupCommandService.upsertUserGroup(UpsertUserGroupCommand.builder()
				.tenantId(new TenantId(1L))
				.groupName("Grade B")
				.description("Grade B")
				.parentId(school.getId())
				.build());

		final var clazz = userGroupCommandService.upsertUserGroup(UpsertUserGroupCommand.builder()
				.tenantId(new TenantId(1L))
				.groupName("Class C")
				.description("Class C")
				.parentId(grade.getId())
				.build());

		final var user = userSupporter.initializeNewUser(UpsertTenantUserCommand.builder()
				.userRoleId(new UserRoleId(1L))
				.tenantId(new TenantId(1L))
				.userGroupId(clazz.getId())
				.userType(UserType.CUSTOMER)
				.email("<EMAIL>")
				.firstName("first name")
				.lastName("last name")
				.phoneNumber("**********")
				.realmId(customerRealmId)
				.build());

		final var request = ActivateUserInternalRequest.builder()
				.ssoId("ssoId-20250618-1458")
				.email(user.getEmail())
				.firstName("first name")
				.lastName("last name")
				.tenantId(1L)
				.realmId(customerRealmId)
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/internal/user/users/activate")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.exchange()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectStatus()
				.isOk()
				.expectBody(UserResponse.class)
				.consumeWith(response -> {
					final var userResponse = response.getResponseBody();
					assertNotNull(userResponse);
					Assertions.assertEquals(UserStatus.ACTIVE, userResponse.getUserStatus());
					Assertions.assertEquals(request.getEmail(), userResponse.getEmail());
					Assertions.assertEquals(request.getFirstName(), userResponse.getFirstName());
					Assertions.assertEquals(request.getLastName(), userResponse.getLastName());
					Assertions.assertEquals(request.getSsoId(), userResponse.getSsoId());
					Assertions.assertEquals(customerRealmId, userResponse.getRealmId());
					Assertions.assertEquals(clazz.getId()
							.getValue()
							.toString(), userResponse.getUserGroup()
									.getId());
					Assertions.assertEquals(UserType.CUSTOMER, userResponse.getUserType());
				});
	}
}
