/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.data.access.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.tenant.service.data.access.entity.TenantDomainNameEntity;
import com.styl.pacific.tenant.service.data.access.specification.TenantDomainNameEntitySpecification;
import com.styl.pacific.tenant.service.domain.entity.TenantDomain;
import com.styl.pacific.tenant.service.domain.mapper.TenantDataMapper;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.DomainFilterRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, TenantDataMapper.class,
		TenantDataAccessMapper.class })
public interface TenantDomainNameDataAccessMapper {

	TenantDomainNameDataAccessMapper INSTANCE = Mappers.getMapper(TenantDomainNameDataAccessMapper.class);

	@Mapping(target = "tenant", source = "tenant")
	TenantDomainNameEntity toTenantDomainNameEntity(TenantDomain tenantDomain);

	TenantDomain toTenantDomain(TenantDomainNameEntity tenantDomainNameEntity);

	TenantDomainNameEntitySpecification toTenantDomainNameEntitySpecification(DomainFilterRequest filter);
}
