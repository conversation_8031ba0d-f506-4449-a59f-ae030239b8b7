/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.TaxId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.TaxDomainServiceImpl;
import com.styl.pacific.tenant.service.domain.dto.CreateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.DeleteTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.GetTaxQuery;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.dto.UpdateTaxCommand;
import com.styl.pacific.tenant.service.domain.entity.Tax;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.id.TaxIdGenerator;
import com.styl.pacific.tenant.service.domain.exception.TaxNotFoundException;
import com.styl.pacific.tenant.service.domain.exception.TenantNotFoundException;
import com.styl.pacific.tenant.service.domain.helper.QueryTenantHelper;
import com.styl.pacific.tenant.service.domain.output.repository.TaxRepository;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TaxDomainServiceImplTest {

	@Mock
	private TaxRepository taxRepository;

	@Mock
	private QueryTenantHelper queryTenantHelper;

	@Mock
	TaxIdGenerator taxIdGenerator;

	@InjectMocks
	private TaxDomainServiceImpl taxDomainService;

	private Tenant tenant;

	@BeforeEach
	public void setUp() {

		tenant = Tenant.builder()
				.id(new TenantId(1L))
				.name("Test Tenant")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.address(new Address("123 Test St", "Apt 4", "Test Country", "Test City", "12345"))
				.createdAt(Instant.now())
				.updatedAt(Instant.now())
				.build();
	}

	@Test
	void testGetListTaxShouldOk() {
		// Arrange
		Long tenantId = 1L;
		Tax tax = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(true)
				.name("VAT")
				.description("description")
				.enabled(true)
				.taxRegNo("12345566")
				.rate(BigDecimal.valueOf(0.2))
				.effectiveDate(Instant.now())
				.build();

		when(taxRepository.getTaxes(any())).thenReturn(List.of(tax));

		// Act & Assert
		List<Tax> taxes = taxDomainService.getTaxes(tenantId);
		assertNotNull(taxes);
		assertEquals(1, taxes.size());
	}

	@Test
	void testUpdateTaxShouldOk() {
		// Arrange
		UpdateTaxCommand updateTaxCommand = UpdateTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.taxId(1L)
				.name("VAT")
				.description("description1")
				.taxRegNo("12345")
				.enabled(true)
				.includeInPrice(true)
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();

		Tax taxExisting = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(false)
				.name("name1")
				.description("description1")
				.enabled(false)
				.taxRegNo("12345")
				.rate(BigDecimal.valueOf(0.5))
				.effectiveDate(Instant.now())
				.build();

		Tax newTax = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(updateTaxCommand.getIncludeInPrice())
				.name(updateTaxCommand.getName())
				.description(updateTaxCommand.getDescription())
				.enabled(updateTaxCommand.getEnabled())
				.taxRegNo(updateTaxCommand.getTaxRegNo())
				.rate(updateTaxCommand.getRate())
				.effectiveDate(updateTaxCommand.getEffectiveDate())
				.build();

		when(queryTenantHelper.getTenantOrThrowNotFound(any(GetTenantQuery.class))).thenReturn(tenant);

		when(taxRepository.getTax(any(GetTaxQuery.class))).thenReturn(Optional.of(taxExisting));

		when(taxRepository.save(any(Tax.class))).thenReturn(newTax);
		// Act & Assert

		Tax tax = taxDomainService.updateTax(updateTaxCommand);

		assertEquals(updateTaxCommand.getTaxId(), tax.getId()
				.getValue());
		assertEquals(updateTaxCommand.getTenantId(), tax.getTenantId()
				.getValue());
		assertEquals(updateTaxCommand.getTaxRegNo(), tax.getTaxRegNo());
		assertEquals(updateTaxCommand.getRate(), tax.getRate());
		assertEquals(updateTaxCommand.getDescription(), tax.getDescription());
		assertEquals(updateTaxCommand.getName(), tax.getName());
	}

	@Test
	void testUpdateTaxWhenTenantNotFoundThenThrowTenantNotFoundException() {
		// Arrange
		UpdateTaxCommand updateTaxCommand = UpdateTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.taxId(1L)
				.name("VAT")
				.description("description1")
				.taxRegNo("12345")
				.enabled(true)
				.includeInPrice(true)
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();
		when(queryTenantHelper.getTenantOrThrowNotFound(any(GetTenantQuery.class))).thenThrow(
				new TenantNotFoundException("Tenant not found"));

		// Act & Assert
		assertThrows(TenantNotFoundException.class, () -> taxDomainService.updateTax(updateTaxCommand));
	}

	@Test
	void testUpdateTaxWhenTaxNotFoundThenThrowTaxNotFoundException() {
		// Arrange
		UpdateTaxCommand updateTaxCommand = UpdateTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.taxId(1L)
				.name("VAT")
				.description("description1")
				.taxRegNo("12345")
				.enabled(true)
				.includeInPrice(true)
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();

		when(queryTenantHelper.getTenantOrThrowNotFound(any(GetTenantQuery.class))).thenReturn(tenant);

		when(taxRepository.getTax(any(GetTaxQuery.class))).thenReturn(Optional.empty());

		// Act & Assert
		assertThrows(TaxNotFoundException.class, () -> taxDomainService.updateTax(updateTaxCommand));
	}

	@Test
	void testGetCurrentTaxShouldOK() {
		// Arrange
		Tax taxExisting = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(false)
				.name("name1")
				.description("description1")
				.enabled(false)
				.taxRegNo("12345")
				.rate(BigDecimal.valueOf(0.5))
				.effectiveDate(Instant.now())
				.build();
		when(taxRepository.getCurrentTax(any())).thenReturn(Optional.of(taxExisting));

		// Act & Assert
		Tax tax = taxDomainService.getCurrentTax(tenant.getId()
				.getValue());

		assertEquals(taxExisting.getId(), tax.getId());
		assertEquals(taxExisting.getTaxRegNo(), tax.getTaxRegNo());
		assertEquals(taxExisting.getRate(), tax.getRate());
		assertEquals(taxExisting.getIncludeInPrice(), tax.getIncludeInPrice());
		assertEquals(taxExisting.getName(), tax.getName());
		assertEquals(taxExisting.getDescription(), tax.getDescription());
		assertEquals(taxExisting.getEnabled(), tax.getEnabled());
		assertEquals(taxExisting.getEffectiveDate(), tax.getEffectiveDate());
	}

	@Test
	void testCreateTaxShouldOk() {
		// Arrange
		CreateTaxCommand createTaxCommand = CreateTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.name("VAT")
				.description("description1")
				.taxRegNo("12345")
				.enabled(true)
				.includeInPrice(true)
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();

		Tax newTax = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(createTaxCommand.getIncludeInPrice())
				.name(createTaxCommand.getName())
				.description(createTaxCommand.getDescription())
				.enabled(createTaxCommand.getEnabled())
				.taxRegNo(createTaxCommand.getTaxRegNo())
				.rate(createTaxCommand.getRate())
				.effectiveDate(createTaxCommand.getEffectiveDate())
				.build();

		when(queryTenantHelper.getTenantOrThrowNotFound(any(GetTenantQuery.class))).thenReturn(tenant);

		when(taxRepository.save(any(Tax.class))).thenReturn(newTax);

		when(taxIdGenerator.nextId()).thenReturn(new TaxId(1L));

		// Act & Assert
		Tax tax = taxDomainService.createTax(createTaxCommand);
		assertEquals(new TaxId(1L), tax.getId());
		assertEquals(createTaxCommand.getTenantId(), tax.getTenantId()
				.getValue());
		assertEquals(createTaxCommand.getTaxRegNo(), tax.getTaxRegNo());
		assertEquals(createTaxCommand.getRate(), tax.getRate());
		assertEquals(createTaxCommand.getDescription(), tax.getDescription());
		assertEquals(createTaxCommand.getName(), tax.getName());
	}

	@Test
	void testCreateTaxWhenTenantNotFoundThenThrowTenantNotFoundException() {
		// Arrange
		CreateTaxCommand createTaxCommand = CreateTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.name("VAT")
				.description("description1")
				.taxRegNo("12345")
				.enabled(true)
				.includeInPrice(true)
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();
		when(queryTenantHelper.getTenantOrThrowNotFound(any(GetTenantQuery.class))).thenThrow(
				new TenantNotFoundException("Tenant not found"));

		// Act & Assert
		assertThrows(TenantNotFoundException.class, () -> taxDomainService.createTax(createTaxCommand));
	}

	@Test
	void testDeleteTaxShouldOk() {
		// Arrange
		DeleteTaxCommand deleteTaxCommand = DeleteTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.taxId(1L)
				.build();

		Tax tax = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenant.getId())
				.includeInPrice(true)
				.name("name1")
				.description("description12")
				.enabled(true)
				.taxRegNo("23456")
				.rate(BigDecimal.valueOf(0.4))
				.effectiveDate(Instant.now())
				.build();

		when(taxRepository.getTax(any(GetTaxQuery.class))).thenReturn(Optional.of(tax));

		taxDomainService.deleteTax(deleteTaxCommand);

		// Act & Assert
		verify(taxRepository, times(1)).deleteTax(tax);
	}

	@Test
	void testDeleteTaxWhenNotFoundTaxShouldFail() {
		// Arrange
		DeleteTaxCommand deleteTaxCommand = DeleteTaxCommand.builder()
				.tenantId(tenant.getId()
						.getValue())
				.taxId(1L)
				.build();

		when(taxRepository.getTax(any(GetTaxQuery.class))).thenReturn(Optional.empty());

		// Act & Assert
		assertThrows(TaxNotFoundException.class, () -> taxDomainService.deleteTax(deleteTaxCommand));
	}

}