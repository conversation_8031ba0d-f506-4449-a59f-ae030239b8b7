---
server:
  port: 9201
logging:
  level:
    com.styl.pacific: DEBUG
spring:
  application:
    name: tenant-service
    version: '@project.version@'
    author: <EMAIL>
  jpa:
    hibernate.ddl-auto: none
    open-in-view: false
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    url: *********************************************************************
    username: postgres
    password: postgres
    platform: postgres
    driver-class-name: org.postgresql.Driver
#    replica:
#      url: *********************************************************************-2
#      username: postgres
#      password: postgres
#      platform: postgres
#      driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
keycloak:
  theme:
    login: pacific-customer-portal-v1
  protocol-mapper:
    id: rest-external-mapper
    name: rest-mapper
    uri:
  invitation-validation:
    uri:
  complete-registration:
    uri:
  user-details:
    uri:
  event-listeners:
    - jboss-logging

management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
    metrics:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "*"

pacific:
  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317
  aws:
    s3:
      endpoint:
      region: ap-southeast-1
      accessKey:
      secretKey:
  kafka:
    tenant-service:
      tenant-created-event:
        topic-name: tenant-service-tenant-created-event
    notification-service:
      notification-event:
        topic-name: notification-service-notification-command
  clients:
    user-service:
      url: http://user-svc.application.svc.cluster.local:9202
    authorization-service:
      url: http://auth-svc.application.svc.cluster.local:9209

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
