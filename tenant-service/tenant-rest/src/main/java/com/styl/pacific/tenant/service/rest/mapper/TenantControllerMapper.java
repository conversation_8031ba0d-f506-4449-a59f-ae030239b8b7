/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.mapper;

import com.styl.pacific.aws.s3.mapper.Mapstruct3SMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.dto.CreateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.CreateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.SaveAgreementTermCommand;
import com.styl.pacific.tenant.service.domain.dto.SaveFeedbackEmailCommand;
import com.styl.pacific.tenant.service.domain.dto.SubmitBusinessFeatureCommand;
import com.styl.pacific.tenant.service.domain.dto.TenantFilter;
import com.styl.pacific.tenant.service.domain.dto.UpdateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantSettingsCommand;
import com.styl.pacific.tenant.service.domain.entity.AgreementTerm;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeature;
import com.styl.pacific.tenant.service.domain.entity.FeedbackEmail;
import com.styl.pacific.tenant.service.domain.entity.Tax;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantCheckList;
import com.styl.pacific.tenant.service.domain.entity.TenantSetting;
import com.styl.pacific.tenant.service.domain.mapper.TenantDataMapper;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTaxRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SaveAgreementTermRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SaveFeedbackEmailRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SubmitBusinessFeatureRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.TenantFilterRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTaxRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantSettingsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.AgreementTermResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.BusinessFeatureMasterDataResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.BusinessFeatureResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.FeedbackEmailResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TaxResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantInfoResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.tenant.service.shared.http.api.response.TenantCheckListResponse;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class, TenantDataMapper.class, Mapstruct3SMapper.class })
public interface TenantControllerMapper {

	TenantControllerMapper INSTANCE = Mappers.getMapper(TenantControllerMapper.class);

	@Mapping(target = "tenantId", source = "tenantId.value")
	UpdateTenantCommand toUpdateTenantCommand(UpdateTenantRequest request, TenantId tenantId);

	@Mapping(target = "addressLine1", source = "address.addressLine1")
	@Mapping(target = "addressLine2", source = "address.addressLine2")
	@Mapping(target = "city", source = "address.city")
	@Mapping(target = "country", source = "address.country", qualifiedByName = "countryCodeToCountryResponse")
	@Mapping(target = "postalCode", source = "address.postalCode")
	@Mapping(target = "tenantId", source = "id", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "activatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "logo", source = "logoPath", qualifiedByName = "pathToFileResponse")
	@Mapping(target = "settings", source = "settings")
	TenantResponse tenantToTenantResponse(Tenant tenant);

	@Mapping(target = "addressLine1", source = "tenant.address.addressLine1")
	@Mapping(target = "addressLine2", source = "tenant.address.addressLine2")
	@Mapping(target = "city", source = "tenant.address.city")
	@Mapping(target = "country", source = "tenant.address.country", qualifiedByName = "countryCodeToCountryResponse")
	@Mapping(target = "postalCode", source = "tenant.address.postalCode")
	@Mapping(target = "tenantId", source = "tenant.id", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "logo", source = "tenant.logoPath", qualifiedByName = "pathToFileResponse")
	@Mapping(target = "settings", source = "tenant.settings")
	@Mapping(target = "businessFeatures", source = "businessFeatures", qualifiedByName = "toBusinessFeatureResponse")
	TenantInfoResponse tenantToTenantInfoResponse(Tenant tenant, List<BusinessFeature> businessFeatures);

	@Mapping(target = "timeZone", source = "timeZone", qualifiedByName = "timezoneToTimeZoneResponse")
	@Mapping(target = "currency", source = "currency", qualifiedByName = "currencyCodeToResponse")
	TenantSettingsResponse tenantSettingToTenantSettingsResponse(TenantSetting tenantSetting);

	TenantFilter toTenantFilter(TenantFilterRequest request);

	CreateTenantCommand createTenantRequestToCreateTenantCommand(CreateTenantRequest request);

	@Mapping(target = "tenantId", source = "tenantId.value")
	UpdateTenantSettingsCommand toUpdateTenantSettingsCommand(UpdateTenantSettingsRequest request, TenantId tenantId);

	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	TenantCheckListResponse toTenantCheckListResponse(TenantCheckList result);

	SubmitBusinessFeatureCommand toAddBusinessFeatureCommand(SubmitBusinessFeatureRequest request, Long tenantId);

	BusinessFeatureMasterDataResponse toBusinessFeatureMasterDataResponse(BusinessFeature businessFeature);

	@Named("toBusinessFeatureResponse")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "id", qualifiedByName = "businessFeatureIdToLong")
	BusinessFeatureResponse toBusinessFeatureResponse(BusinessFeature businessFeature);

	@Mapping(target = "id", qualifiedByName = "taxIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "effectiveDate", qualifiedByName = "instantToLong")
	TaxResponse toTaxResponse(Tax tax);

	@Mapping(target = "effectiveDate", qualifiedByName = "longToInstant")
	CreateTaxCommand toCreateTaxCommand(CreateTaxRequest request, Long tenantId);

	@Mapping(target = "effectiveDate", qualifiedByName = "longToInstant")
	UpdateTaxCommand toUpdateTaxCommand(UpdateTaxRequest request, Long tenantId, Long taxId);

	SaveAgreementTermCommand toSaveAgreementTermCommand(SaveAgreementTermRequest request, Long tenantId);

	@Mapping(target = "id", qualifiedByName = "agreementTermIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	AgreementTermResponse toAgreementTermResponse(AgreementTerm agreementTerm);

	@Mapping(target = "id", qualifiedByName = "feedbackEmailIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	FeedbackEmailResponse toFeedbackEmailResponse(FeedbackEmail feedbackEmail);

	SaveFeedbackEmailCommand toSaveFeedbackEmailCommand(SaveFeedbackEmailRequest request, Long tenantId);
}
