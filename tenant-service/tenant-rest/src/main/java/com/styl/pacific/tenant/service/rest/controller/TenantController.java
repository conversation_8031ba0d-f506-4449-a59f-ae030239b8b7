/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.controller;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.exception.UnimplementedOperationException;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.KeycloakRealmGenerator;
import com.styl.pacific.tenant.service.domain.dto.ActivateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.CreateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.DeleteTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.FindTenantsQuery;
import com.styl.pacific.tenant.service.domain.dto.GetTenantCheckListQuery;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.dto.SubmitBusinessFeatureCommand;
import com.styl.pacific.tenant.service.domain.dto.SubmitTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.ToggleCheckItemCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantSettingsCommand;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeature;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantCheckList;
import com.styl.pacific.tenant.service.domain.entity.TenantDomain;
import com.styl.pacific.tenant.service.domain.entity.TenantSetting;
import com.styl.pacific.tenant.service.domain.exception.TenantNotFoundException;
import com.styl.pacific.tenant.service.domain.port.input.service.TenantDomainNameService;
import com.styl.pacific.tenant.service.domain.port.input.service.TenantDomainService;
import com.styl.pacific.tenant.service.rest.mapper.TenantControllerMapper;
import com.styl.pacific.tenant.service.shared.http.api.TenantApi;
import com.styl.pacific.tenant.service.shared.http.api.TenantOnboardingApi;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindTenantsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.SubmitBusinessFeatureRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantSettingsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListBusinessFeatureMasterDataResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListBusinessFeatureResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListTenantsResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantInfoResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.tenant.service.shared.http.api.response.TenantCheckListResponse;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class TenantController implements TenantApi, TenantOnboardingApi {

	private static final Logger logger = LoggerFactory.getLogger(TenantController.class);

	private final TenantDomainService tenantDomainService;
	private final KeycloakRealmGenerator keycloakRealmGenerator;
	private final TenantDomainNameService tenantDomainNameService;

	public ResponseEntity<TenantResponse> createTenant(@RequestBody @Valid CreateTenantRequest request) {
		CreateTenantCommand command = TenantControllerMapper.INSTANCE.createTenantRequestToCreateTenantCommand(request);
		Tenant tenant = tenantDomainService.createTenant(command);
		TenantResponse response = TenantControllerMapper.INSTANCE.tenantToTenantResponse(tenant);
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<TenantResponse> updateTenant(long id, @RequestBody @Valid UpdateTenantRequest request) {
		UpdateTenantCommand command = TenantControllerMapper.INSTANCE.toUpdateTenantCommand(request, new TenantId(id));
		Tenant tenant = tenantDomainService.updateTenant(command);
		return ResponseEntity.ok(TenantControllerMapper.INSTANCE.tenantToTenantResponse(tenant));
	}

	public ResponseEntity<ListTenantsResponse> findTenants(@ModelAttribute @Valid FindTenantsRequest request) {

		FindTenantsQuery query = new FindTenantsQuery(TenantControllerMapper.INSTANCE.toTenantFilter(request
				.getFilter()), request);

		Paging<Tenant> tenants = tenantDomainService.findTenants(query);

		return ResponseEntity.ok(new ListTenantsResponse(tenants.getContent()
				.stream()
				.map(TenantControllerMapper.INSTANCE::tenantToTenantResponse)
				.toList(), tenants.getTotalElements(), tenants.getTotalPages(), tenants.getPage(), tenants.getSort()));
	}

	public ResponseEntity<TenantResponse> getTenant(long id) {
		Tenant tenant = tenantDomainService.getTenant(GetTenantQuery.builder()
				.tenantId(id)
				.build());
		return ResponseEntity.ok(TenantControllerMapper.INSTANCE.tenantToTenantResponse(tenant));
	}

	@Override
	public ResponseEntity<TenantInfoResponse> getTenantInfo(String hostName, String forwardedHost) {
		logger.debug("getTenantInfo: hostName={}, forwardedHost={}", hostName, forwardedHost);
		// Forwarded host might be a comma separated list of hosts
		String domainName = Optional.ofNullable(forwardedHost)
				.filter(StringUtils::isNotBlank)
				.map(header -> header.split(","))
				.map(hosts -> hosts[0])
				.orElse(hostName);
		logger.debug("domainName={}", domainName);
		if (StringUtils.isBlank(domainName)) {
			throw new TenantNotFoundException("Domain name is invalid");
		}
		return ResponseEntity.ok(tenantDomainNameService.findDomain(domainName)
				.map(TenantDomain::getTenant)
				.map(tenant -> TenantControllerMapper.INSTANCE.tenantToTenantInfoResponse(tenant, tenantDomainService
						.getBusinessFeatures(tenant.getId()
								.getValue())))
				.orElseThrow(() -> new TenantNotFoundException("Tenant not found or domain is invalid")));
	}

	public ResponseEntity<Void> deleteTenant(long id) {
		tenantDomainService.deleteTenant(new DeleteTenantCommand(id));
		return ResponseEntity.noContent()
				.build();
	}

	public ResponseEntity<String> downloadRealmProfile(long id) {
		String result = keycloakRealmGenerator.generate(id);

		// return with header
		return ResponseEntity.ok()
				.header("Content-Disposition", "attachment; filename=realm.json")
				.body(result);
	}

	@Override
	public ResponseEntity<Void> submitTenant(long id) {
		tenantDomainService.submitTenant(SubmitTenantCommand.builder()
				.tenantId(id)
				.build());
		return ResponseEntity.noContent()
				.build();
	}

	@Override
	public ResponseEntity<Void> activateTenant(long id) {
		tenantDomainService.activateTenant(ActivateTenantCommand.builder()
				.tenantId(id)
				.build());
		return ResponseEntity.noContent()
				.build();
	}

	@Override
	public ResponseEntity<TenantSettingsResponse> getTenantSettings(long id) {
		Tenant tenant = tenantDomainService.getTenant(GetTenantQuery.builder()
				.tenantId(id)
				.build());
		TenantSetting settings = Optional.ofNullable(tenant)
				.map(Tenant::getSettings)
				.orElse(TenantSetting.builder()
						.build());
		return ResponseEntity.ok(TenantControllerMapper.INSTANCE.tenantSettingToTenantSettingsResponse(settings));
	}

	@Override
	public ResponseEntity<TenantSettingsResponse> updateTenantSettings(long id,
			@RequestBody @Valid UpdateTenantSettingsRequest request) {
		UpdateTenantSettingsCommand updateTenantSettingsCommand = TenantControllerMapper.INSTANCE
				.toUpdateTenantSettingsCommand(request, new TenantId(id));
		TenantSetting tenantSettings = tenantDomainService.updateTenantSettings(updateTenantSettingsCommand);
		return ResponseEntity.ok(TenantControllerMapper.INSTANCE.tenantSettingToTenantSettingsResponse(tenantSettings));
	}

	@Override
	public ResponseEntity<List<TenantCheckListResponse>> getTenantCheckList(long id) {
		List<TenantCheckList> result = tenantDomainService.getTenantCheckList(GetTenantCheckListQuery.builder()
				.tenantId(id)
				.build());

		return ResponseEntity.ok(result.stream()
				.map(TenantControllerMapper.INSTANCE::toTenantCheckListResponse)
				.sorted((o1, o2) -> Long.compare(o1.getItemId(), o2.getItemId()))
				.toList());
	}

	@Override
	public ResponseEntity<Void> syncCheckList(long id) {
		throw new UnimplementedOperationException();
	}

	@Override
	public ResponseEntity<Void> completeCheckListItem(long id, long itemId) {
		tenantDomainService.toggleCheckListItem(ToggleCheckItemCommand.builder()
				.tenantId(id)
				.itemId(itemId)
				.completed(true)
				.build());
		return ResponseEntity.noContent()
				.build();
	}

	@Override
	public ResponseEntity<Void> unCompleteCheckListItem(long id, long itemId) {
		tenantDomainService.toggleCheckListItem(ToggleCheckItemCommand.builder()
				.tenantId(id)
				.itemId(itemId)
				.completed(false)
				.build());
		return ResponseEntity.noContent()
				.build();
	}

	public ResponseEntity<ListBusinessFeatureResponse> submitBusinessFeatures(
			@RequestBody @Valid SubmitBusinessFeatureRequest request, long id) {
		SubmitBusinessFeatureCommand command = TenantControllerMapper.INSTANCE.toAddBusinessFeatureCommand(request, id);
		List<BusinessFeature> businessFeatures = tenantDomainService.submitBusinessFeatures(command);
		return ResponseEntity.ok(ListBusinessFeatureResponse.builder()
				.content(businessFeatures.stream()
						.map(TenantControllerMapper.INSTANCE::toBusinessFeatureResponse)
						.toList())
				.build());
	}

	public ResponseEntity<ListBusinessFeatureResponse> getBusinessFeatures(long id) {
		List<BusinessFeature> result = tenantDomainService.getBusinessFeatures(id);
		return ResponseEntity.ok(ListBusinessFeatureResponse.builder()
				.content(result.stream()
						.map(TenantControllerMapper.INSTANCE::toBusinessFeatureResponse)
						.toList())
				.build());
	}

	public ResponseEntity<ListBusinessFeatureMasterDataResponse> getBusinessFeaturesMasterData() {
		List<BusinessFeature> result = tenantDomainService.getBusinessFeatureMasterData();
		return ResponseEntity.ok(new ListBusinessFeatureMasterDataResponse(result.stream()
				.map(TenantControllerMapper.INSTANCE::toBusinessFeatureMasterDataResponse)
				.toList()));
	}
}
