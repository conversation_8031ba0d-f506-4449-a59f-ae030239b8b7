/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.rest.controller;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.exception.UnimplementedOperationException;
import com.styl.pacific.tenant.service.domain.entity.TenantDomain;
import com.styl.pacific.tenant.service.domain.port.input.service.TenantDomainNameService;
import com.styl.pacific.tenant.service.rest.mapper.TenantDomainControllerMapper;
import com.styl.pacific.tenant.service.shared.http.api.TenantDomainApi;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.AddDomainNameRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindDomainsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.DomainAvailabilityResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.DomainResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListDomainResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
public class TenantDomainController implements TenantDomainApi {

	private final TenantDomainNameService tenantDomainNameService;

	@Override
	public ResponseEntity<DomainAvailabilityResponse> checkAvailability(String domain) {
		boolean isAvailable = tenantDomainNameService.checkAvailability(domain);
		return ResponseEntity.ok(new DomainAvailabilityResponse(isAvailable));
	}

	@Override
	public ResponseEntity<ListDomainResponse> getDomains(@SpringQueryMap @Valid FindDomainsRequest request) {
		Paging<TenantDomain> domains = tenantDomainNameService.findDomains(request);
		return ResponseEntity.ok(new ListDomainResponse(domains.getContent()
				.stream()
				.map(TenantDomainControllerMapper.INSTANCE::toDomainResponse)
				.toList(), domains.getTotalElements(), domains.getTotalPages(), domains.getPage(), domains.getSort()));
	}

	@Override
	public DomainResponse addDomain(AddDomainNameRequest request) {
		throw new UnimplementedOperationException();
	}

	@Override
	public void activateDomain(long domainId) {
		tenantDomainNameService.activateDomain(domainId);
	}

	@Override
	public void deleteDomain(long domainId) {
		tenantDomainNameService.deleteDomain(domainId);
	}

}
