<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ /******************************************************************************
  ~  *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
  ~  *                                                                             *
  ~  *  This source code and any compilation or derivative thereof is the sole     *
  ~  *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
  ~  *  Software License Agreement.  This code is the proprietary information of   *
  ~  *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
  ~  *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
  ~  *  limited by the confidential information provisions of the Agreement        *
  ~  *  referenced above.                                                          *
  ~  ******************************************************************************/
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.tenant.service</groupId>
        <artifactId>tenant-service</artifactId>
        <version>1.2.5</version>
    </parent>

    <artifactId>tenant-shared</artifactId>
    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

    </dependencies>

</project>
