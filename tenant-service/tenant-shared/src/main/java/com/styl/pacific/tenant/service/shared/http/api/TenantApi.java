/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.shared.http.api;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.CreateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindTenantsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.UpdateTenantSettingsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListTenantsResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantInfoResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

public interface TenantApi {

	@PostMapping(path = "/api/tenant/tenants")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_MGMT_NEW_TENANT)
	public ResponseEntity<TenantResponse> createTenant(@RequestBody @Valid CreateTenantRequest command);

	@GetMapping(path = "/api/tenant/tenants")
	@PacificApiAuthorized
	public ResponseEntity<ListTenantsResponse> findTenants(
			@SpringQueryMap @ModelAttribute @Valid FindTenantsRequest query);

	@GetMapping(path = "/api/tenant/tenants/{id}")
	@PacificApiAuthorized
	public ResponseEntity<TenantResponse> getTenant(@PathVariable long id);

	@GetMapping(path = "/api/tenant/tenants/info")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.PUBLIC)
	ResponseEntity<TenantInfoResponse> getTenantInfo(@RequestHeader(name = "Host", required = false) String hostName,
			@RequestHeader(name = "X-Forwarded-Host", required = false) String forwardedHost);

	@PutMapping(path = "/api/tenant/tenants/{id}")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_MGMT_UPDATE)
	public ResponseEntity<TenantResponse> updateTenant(@PathVariable long id,
			@RequestBody @Valid UpdateTenantRequest request);

	@DeleteMapping(path = "/api/tenant/tenants/{id}")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_MGMT_DELETE)
	public ResponseEntity<Void> deleteTenant(@PathVariable long id);

	@GetMapping(path = "/api/tenant/tenants/{id}/settings")
	@PacificApiAuthorized
	public ResponseEntity<TenantSettingsResponse> getTenantSettings(@PathVariable long id);

	@PutMapping(path = "/api/tenant/tenants/{id}/settings")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_MGMT_SETTING_UPDATE)
	public ResponseEntity<TenantSettingsResponse> updateTenantSettings(@PathVariable long id,
			@RequestBody @Valid UpdateTenantSettingsRequest request);

	@PacificApiAuthorized
	@GetMapping(path = "/api/tenant/tenants/{id}/keycloak/realm.json")
	public ResponseEntity<String> downloadRealmProfile(@PathVariable long id);
}
