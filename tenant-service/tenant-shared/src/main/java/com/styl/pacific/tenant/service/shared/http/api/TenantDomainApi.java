/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.shared.http.api;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.AddDomainNameRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindDomainsRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.DomainAvailabilityResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.DomainResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.ListDomainResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface TenantDomainApi {

	@PacificApiAuthorized
	@GetMapping(path = "/api/tenant/domains/availability")
	public ResponseEntity<DomainAvailabilityResponse> checkAvailability(@RequestParam String domain);

	@PacificApiAuthorized
	@GetMapping(path = "/api/tenant/domains")
	public ResponseEntity<ListDomainResponse> getDomains(@SpringQueryMap FindDomainsRequest request);

	@PacificApiAuthorized
	@PostMapping(path = "/api/tenant/domains")
	public DomainResponse addDomain(@RequestBody @Valid AddDomainNameRequest request);

	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_DOMAIN_MGMT_ACTIVATE)
	@PutMapping(path = "/api/tenant/domains/{domainId}/activate")
	public void activateDomain(@PathVariable long domainId);

	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.TENANT_DOMAIN_MGMT_DELETE)
	@DeleteMapping(path = "/api/tenant/domains/{domainId}/delete")
	public void deleteDomain(@PathVariable long domainId);

}
