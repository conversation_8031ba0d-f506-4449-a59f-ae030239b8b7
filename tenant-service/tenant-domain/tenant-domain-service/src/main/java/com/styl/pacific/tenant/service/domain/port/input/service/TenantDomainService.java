/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.port.input.service;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.tenant.service.domain.dto.ActivateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.CreateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.DeleteTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.GetTenantCheckListQuery;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.dto.SubmitBusinessFeatureCommand;
import com.styl.pacific.tenant.service.domain.dto.SubmitTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.TenantFilter;
import com.styl.pacific.tenant.service.domain.dto.ToggleCheckItemCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantSettingsCommand;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeature;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantCheckList;
import com.styl.pacific.tenant.service.domain.entity.TenantSetting;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TenantDomainService {

	Tenant createTenant(@Valid CreateTenantCommand command);

	Tenant updateTenant(@Valid UpdateTenantCommand command);

	void deleteTenant(@Valid DeleteTenantCommand command);

	Tenant getTenant(GetTenantQuery query);

	Paging<Tenant> findTenants(@Valid PaginationQuery<TenantFilter> query);

	TenantSetting updateTenantSettings(UpdateTenantSettingsCommand updateTenantSettingsCommand);

	void submitTenant(SubmitTenantCommand submitTenantCommand);

	void activateTenant(ActivateTenantCommand activateTenantCommand);

	List<TenantCheckList> getTenantCheckList(GetTenantCheckListQuery build);

	void toggleCheckListItem(ToggleCheckItemCommand build);

	List<BusinessFeature> submitBusinessFeatures(SubmitBusinessFeatureCommand command);

	List<BusinessFeature> getBusinessFeatures(Long tenantId);

	List<BusinessFeature> getBusinessFeatureMasterData();
}
