/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.port.input.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantDomain;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindDomainsRequest;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface TenantDomainNameService {

	Optional<TenantDomain> findDomain(String domainName);

	Paging<TenantDomain> findDomains(FindDomainsRequest request);

	void deleteDomain(long domainId);

	void activateDomain(long domainId);

	boolean checkAvailability(String domainName);

	TenantDomain addDomain(Tenant tenant, String domainName);
}
