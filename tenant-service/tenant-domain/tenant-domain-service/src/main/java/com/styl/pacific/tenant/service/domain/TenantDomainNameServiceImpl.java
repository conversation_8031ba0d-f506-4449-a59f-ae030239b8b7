/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.tenant.service.domain.dto.GetTenantQuery;
import com.styl.pacific.tenant.service.domain.entity.DomainNameStatus;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantDomain;
import com.styl.pacific.tenant.service.domain.entity.TenantSetting;
import com.styl.pacific.tenant.service.domain.entity.id.TenantDomainIdGenerator;
import com.styl.pacific.tenant.service.domain.exception.DomainNameInUsedException;
import com.styl.pacific.tenant.service.domain.exception.TenantDomainException;
import com.styl.pacific.tenant.service.domain.output.repository.TenantDomainNameRepository;
import com.styl.pacific.tenant.service.domain.output.repository.TenantRepository;
import com.styl.pacific.tenant.service.domain.port.input.service.TenantDomainNameService;
import com.styl.pacific.tenant.service.shared.http.api.dto.request.FindDomainsRequest;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class TenantDomainNameServiceImpl implements TenantDomainNameService {

	private final TenantDomainNameRepository tenantDomainNameRepository;
	private final TenantDomainIdGenerator tenantDomainIdGenerator;

	private final TenantRepository tenantRepository;

	@Override
	public Optional<TenantDomain> findDomain(String domainName) {
		return tenantDomainNameRepository.findDomainName(domainName);
	}

	@Override
	public Paging<TenantDomain> findDomains(FindDomainsRequest request) {
		return tenantDomainNameRepository.findDomains(request);
	}

	@Override
	public void deleteDomain(long domainId) {
		TenantDomain domain = tenantDomainNameRepository.findById(domainId)
				.orElse(null);
		if (domain == null) {
			throw new TenantDomainException("Domain not found");
		}

		Optional<Tenant> tenant = tenantRepository.getTenant(GetTenantQuery.builder()
				.tenantId(domain.getTenant()
						.getId()
						.getValue())
				.build());

		if (tenant.isPresent() && tenant.map(Tenant::getSettings)
				.map(TenantSetting::getDefaultDomain)
				.orElse("")
				.equalsIgnoreCase(domain.getDomainName())) {
			throw new DomainNameInUsedException("Cannot delete tenant's default domain");
		}

		tenantDomainNameRepository.deleteById(domainId);
	}

	@Override
	public void activateDomain(long domainId) {
		TenantDomain tenantDomain = tenantDomainNameRepository.findById(domainId)
				.orElseThrow(() -> new TenantDomainException("Domain not found"));

		String domainName = tenantDomain.getDomainName();
		// TODO check if domain name is existed
		// TODO Call to AWS API Gateway to create and activate domain

		tenantDomain.setStatus(DomainNameStatus.ACTIVATED);
		tenantDomainNameRepository.save(tenantDomain);
	}

	@Override
	public boolean checkAvailability(String domainName) {
		String[] paths = domainName.split("\\.");
		if (paths.length < 3) {
			return false;
		}
		String subDomain = paths[0];
		if (TenantDomain.EXHIBITION_SUBDOMAIN.contains(subDomain)) {
			return false;
		}

		return !tenantDomainNameRepository.existsByDomainName(domainName);
	}

	@Override
	public TenantDomain addDomain(Tenant tenant, String domainName) {
		String lowercaseDomainName = domainName.toLowerCase();
		if (!checkAvailability(lowercaseDomainName)) {
			throw new TenantDomainException("Domain name is not available");
		}
		TenantDomain domain = TenantDomain.builder()
				.id(tenantDomainIdGenerator.nextId())
				.tenant(tenant)
				.domainName(lowercaseDomainName)
				.status(DomainNameStatus.PENDING)
				.build();
		return tenantDomainNameRepository.save(domain);
	}
}
