/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.BusinessFeatureId;
import com.styl.pacific.domain.valueobject.TaxId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.dto.CreateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.CreateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.SaveAgreementTermCommand;
import com.styl.pacific.tenant.service.domain.dto.SaveFeedbackEmailCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantSettingsCommand;
import com.styl.pacific.tenant.service.domain.entity.AgreementTerm;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeature;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeatureEnum;
import com.styl.pacific.tenant.service.domain.entity.FeedbackEmail;
import com.styl.pacific.tenant.service.domain.entity.Tax;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.entity.TenantSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface TenantDataMapper {

	TenantDataMapper INSTANCE = Mappers.getMapper(TenantDataMapper.class);

	@Mapping(target = "checkLists", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "address.addressLine1", source = "addressLine1")
	@Mapping(target = "address.addressLine2", source = "addressLine2")
	@Mapping(target = "address.city", source = "city")
	@Mapping(target = "address.country", source = "country")
	@Mapping(target = "address.postalCode", source = "postalCode")
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "activatedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "realmId", ignore = true)
	@Mapping(target = "settings", ignore = true)
	public Tenant createTenantCommandToTenant(CreateTenantCommand command);

	@Mapping(target = "id", source = "command.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "name", source = "command.name")
	@Mapping(target = "businessRegNo", source = "command.businessRegNo")
	@Mapping(target = "businessType", source = "command.businessType")
	@Mapping(target = "email", source = "command.email")
	@Mapping(target = "phoneNumber", source = "command.phoneNumber")
	@Mapping(target = "contactRemarks", source = "command.contactRemarks")
	@Mapping(target = "email2", source = "command.email2")
	@Mapping(target = "phoneNumber2", source = "command.phoneNumber2")
	@Mapping(target = "contactRemarks2", source = "command.contactRemarks2")
	@Mapping(target = "email3", source = "command.email3")
	@Mapping(target = "phoneNumber3", source = "command.phoneNumber3")
	@Mapping(target = "contactRemarks3", source = "command.contactRemarks3")
	@Mapping(target = "address.addressLine1", source = "command.addressLine1")
	@Mapping(target = "address.addressLine2", source = "command.addressLine2")
	@Mapping(target = "address.city", source = "command.city")
	@Mapping(target = "address.country", source = "command.country")
	@Mapping(target = "address.postalCode", source = "command.postalCode")
	@Mapping(target = "website", source = "command.website")
	@Mapping(target = "logoPath", source = "command.logoPath")
	@Mapping(target = "createdAt", source = "existingTenant.createdAt")
	@Mapping(target = "realmId", source = "existingTenant.realmId")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "settings", ignore = true)
	public Tenant updateTenantCommandToTenant(UpdateTenantCommand command, Tenant existingTenant);

	// Do not allow update Currency if it is not null
	@Mapping(target = "tenantId", source = "command.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "timeZone", source = "command.timeZone", qualifiedByName = "stringToTimeZone")
	@Mapping(target = "dateFormat", source = "command.dateFormat")
	@Mapping(target = "timeFormat", source = "command.timeFormat")
	@Mapping(target = "currency", source = "command.currency")
	@Mapping(target = "defaultDomain", source = "command.defaultDomain")
	TenantSetting updateTenantSettingsCommandToTenantSetting(UpdateTenantSettingsCommand command,
			TenantSetting existing);

	@Mapping(target = "enabled", source = "enabled")
	BusinessFeature updateBusinessFeatureToNewBusinessFeature(BusinessFeature existing, Boolean enabled);

	BusinessFeature enumBusinessFeatureToBusinessFeature(BusinessFeatureEnum businessFeatureEnum, TenantId tenantId,
			BusinessFeatureId id, Boolean enabled);

	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "enabled", constant = "false")
	BusinessFeature enumBusinessFeatureToBusinessFeature(BusinessFeatureEnum businessFeatureEnum);

	@Named("longToBusinessFeatureId")
	default BusinessFeatureId longToBusinessFeatureId(Long id) {
		if (id == null) {
			return null;
		}
		return new BusinessFeatureId(id);
	}

	@Named("businessFeatureIdToLong")
	default Long businessFeatureIdToLong(BusinessFeatureId id) {
		if (id == null) {
			return null;
		}
		return id.getValue();
	}

	@Named("taxIdToLong")
	default Long taxIdToLong(TaxId id) {
		if (id == null) {
			return null;
		}
		return id.getValue();
	}

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "tenantId", source = "command.tenantId", qualifiedByName = "longToTenantId")
	Tax createTaxCommandToTax(CreateTaxCommand command, TaxId id);

	@Mapping(target = "id", source = "taxExisting.id")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", source = "taxExisting.createdAt")
	@Mapping(target = "tenantId", source = "taxExisting.tenantId")
	@Mapping(target = "name", source = "command.name")
	@Mapping(target = "taxRegNo", source = "command.taxRegNo")
	@Mapping(target = "rate", source = "command.rate")
	@Mapping(target = "description", source = "command.description")
	@Mapping(target = "includeInPrice", source = "command.includeInPrice")
	@Mapping(target = "effectiveDate", source = "command.effectiveDate")
	@Mapping(target = "enabled", source = "command.enabled")
	Tax updateTaxCommandToTax(UpdateTaxCommand command, Tax taxExisting);

	@Mapping(target = "id", source = "agreementTermExisting.id")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", source = "agreementTermExisting.createdAt")
	@Mapping(target = "tenantId", source = "agreementTermExisting.tenantId")
	@Mapping(target = "content", source = "command.content")
	AgreementTerm toAgreementTerm(SaveAgreementTermCommand command, AgreementTerm agreementTermExisting);

	@Mapping(target = "id", source = "feedbackEmail.id")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", source = "feedbackEmail.createdAt")
	@Mapping(target = "tenantId", source = "feedbackEmail.tenantId")
	@Mapping(target = "email", source = "command.email")
	FeedbackEmail toFeedbackEmail(SaveFeedbackEmailCommand command, FeedbackEmail feedbackEmail);
}
