/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.entity;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.AgreementTermId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class AgreementTerm extends BaseEntity<AgreementTermId> {

	private TenantId tenantId;
	private String content;
	private Instant createdAt;
	private Instant updatedAt;

	private AgreementTerm(Builder builder) {
		setId(builder.id);
		tenantId = builder.tenantId;
		content = builder.content;
		createdAt = builder.createdAt;
		updatedAt = builder.updatedAt;
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private AgreementTermId id;
		private TenantId tenantId;
		private String content;
		private Instant createdAt;
		private Instant updatedAt;

		private Builder() {
		}

		public Builder id(AgreementTermId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder content(String content) {
			this.content = content;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public AgreementTerm build() {
			return new AgreementTerm(this);
		}
	}

	public TenantId getTenantId() {
		return tenantId;
	}

	public String getContent() {
		return content;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof AgreementTerm that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(tenantId, that.tenantId) && Objects.equals(content, that.content) && Objects.equals(
				createdAt, that.createdAt) && Objects.equals(updatedAt, that.updatedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), tenantId, content, createdAt, updatedAt);
	}
}
