/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.mapper;

import com.styl.pacific.catalog.service.data.access.relational.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.mapper.NutritionDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductNutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductNutritionEntityId;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.product.dto.ProductNutritionDto;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.catalog.service.domain.product.mapper.ProductNutritionDataMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.ProductId;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { ProductNutritionDataMapper.class,
		NutritionDataAccessMapper.class, MapstructCommonMapper.class, CommonDataMapper.class,
		CommonDataAccessMapper.class, MapstructCommonDomainMapper.class })
public interface ProductNutritionDataAccessMapper {
	ProductNutritionDataAccessMapper INSTANCE = Mappers.getMapper(ProductNutritionDataAccessMapper.class);

	@Mapping(target = "nutritionId", source = "entity.id.nutritionId", qualifiedByName = "longToNutritionId")
	@Mapping(target = "productId", source = "entity.id.productId", qualifiedByName = "longToProductId")
	ProductNutrition toModel(ProductNutritionEntity entity);

	@Mapping(target = "id", expression = "java(toProductNutritionEntityId(nutrition.getProductId(),nutrition.getNutritionId()))")
	@Mapping(target = "product.id", source = "nutrition.productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "nutrition.id", source = "nutrition.nutritionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	ProductNutritionEntity toEntity(ProductNutrition nutrition);

	@Mapping(target = "id", source = "nutrition.id.nutritionId", qualifiedByName = "longToNutritionId")
	@Mapping(target = "name", source = "nutrition.nutrition.name")
	@Mapping(target = "unit", source = "nutrition.nutrition.unit")
	@Mapping(target = "tenantId", source = "nutrition.nutrition.tenantId", qualifiedByName = "longToTenantId")
	ProductNutritionDto toDto(ProductNutritionEntity nutrition);

	default ProductNutritionEntityId toProductNutritionEntityId(ProductId productId, NutritionId nutritionId) {
		if (productId == null || nutritionId == null) {
			return null;
		}
		return new ProductNutritionEntityId(productId.getValue(), nutritionId.getValue());
	}
}
