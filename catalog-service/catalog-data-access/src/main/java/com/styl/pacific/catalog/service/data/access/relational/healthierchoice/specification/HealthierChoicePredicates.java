/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.healthierchoice.specification;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity.QHealthierChoiceEntity;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HealthierChoicePredicates {
	public static BooleanBuilder withTenantIdAndId(Long tenantId, Long id) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.and(withId(id));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static BooleanBuilder withMultipleCriteria(Long tenantId, String name, String description) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.and(likeName(name));
		}
		if (StringUtils.isNotBlank(description)) {
			specifications.and(likeDescription(description));
		}
		specifications.and(withIsNotDeleted());
		return specifications;
	}

	public static Predicate likeName(String name) {
		return QHealthierChoiceEntity.healthierChoiceEntity.name.containsIgnoreCase(name);
	}

	public static Predicate likeDescription(String description) {
		return QHealthierChoiceEntity.healthierChoiceEntity.description.containsIgnoreCase(description);
	}

	public static Predicate withTenantId(Long tenantId) {
		return QHealthierChoiceEntity.healthierChoiceEntity.tenantId.eq(tenantId);
	}

	public static Predicate withId(Long id) {
		return QHealthierChoiceEntity.healthierChoiceEntity.id.eq(id);
	}

	public static Predicate withIsNotDeleted() {
		return QHealthierChoiceEntity.healthierChoiceEntity.deletedAt.isNull();
	}
}
