/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.specification;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductEntity;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import java.math.BigInteger;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductPredicates {
	public static Predicate byTenantIdAndSku(Long tenantId, String sku) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(sku)) {
			specifications.and(withSKu(sku));
		}
		specifications.and(withDeletedIsNull());
		return specifications;
	}

	public static Predicate byTenantIdAndMigrationId(Long tenantId, String migrationId) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (StringUtils.isNotBlank(migrationId)) {
			specifications.and(withMigrationId(migrationId));
		}
		return specifications;
	}

	public static Predicate byTenantIdAndSkuNotId(Long tenantId, Long productId, String sku) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(productId)) {
			specifications.and(withDifferentId(productId));
		}
		if (StringUtils.isNotBlank(sku)) {
			specifications.and(withSKu(sku));
		}
		specifications.and(withDeletedIsNull());
		return specifications;
	}

	public static Predicate byMultipleCriteria(Long tenantId, Long productId, Long categoryId, String sku) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(productId)) {
			specifications.and(withId(productId));
		}
		if (Objects.nonNull(categoryId)) {
			specifications.and(withCategoryId(categoryId));
		}
		if (StringUtils.isNotBlank(sku)) {
			specifications.and(withLikeSku(sku));
		}
		specifications.and(withDeletedIsNull());
		return specifications;
	}

	public static Predicate byTenantIdAndId(Long tenantId, Long id) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.and(withId(id));
		}
		specifications.and(withDeletedIsNull());
		return specifications;
	}

	public static Predicate byMultipleCriteria(Long tenantId, List<Long> ids, String migrationId, String name,
			List<Long> healthierChoiceIds, List<Long> categoryIds, Long storeId, String barcode, String sku,
			List<ProductStatus> statuses, BigInteger fromUnitPrice, BigInteger toUnitPrice) {
		BooleanBuilder specifications = new BooleanBuilder();
		if (Objects.nonNull(tenantId)) {
			specifications.and(withTenantId(tenantId));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.and(withIds(ids));
		}
		if (StringUtils.isNotBlank(migrationId)) {
			specifications.and(withMigrationId(migrationId));
		}
		if (Objects.nonNull(name)) {
			specifications.and(withLikeName(name));
		}
		if (Objects.nonNull(healthierChoiceIds) && !healthierChoiceIds.isEmpty()) {
			specifications.and(withHealthierChoiceIds(healthierChoiceIds));
		}
		if (Objects.nonNull(categoryIds) && !categoryIds.isEmpty()) {
			specifications.and(withCategoryIds(categoryIds));
		}
		if (Objects.nonNull(storeId)) {
			specifications.and(withStoreId(storeId));
		}
		if (StringUtils.isNotBlank(barcode)) {
			specifications.and(withBarcode(barcode));
		}
		if (StringUtils.isNotBlank(sku)) {
			specifications.and(withLikeSku(sku));
		}
		if (Objects.nonNull(statuses) && !statuses.isEmpty()) {
			specifications.and(withStatuses(statuses));
		}
		if (Objects.nonNull(fromUnitPrice)) {
			specifications.and(fromUnitPrice(fromUnitPrice));
		}
		if (Objects.nonNull(toUnitPrice)) {
			specifications.and(toUnitPrice(toUnitPrice));
		}
		specifications.and(withDeletedIsNull());
		return specifications;
	}

	public static Predicate withId(Long id) {
		return QProductEntity.productEntity.id.eq(id);
	}

	public static Predicate withDifferentId(Long id) {
		return QProductEntity.productEntity.id.ne(id);
	}

	public static Predicate withIds(List<Long> ids) {
		return QProductEntity.productEntity.id.in(ids);
	}

	public static Predicate withLikeName(String name) {
		return QProductEntity.productEntity.name.containsIgnoreCase(name);
	}

	public static Predicate withCategoryId(Long categoryId) {
		return QProductEntity.productEntity.categoryId.eq(categoryId);
	}

	public static Predicate withCategoryIds(List<Long> categoryIds) {
		return QProductEntity.productEntity.categoryId.in(categoryIds);
	}

	public static Predicate withHealthierChoiceIds(List<Long> healthierChoiceIds) {
		return QProductEntity.productEntity.healthierChoiceId.in(healthierChoiceIds);
	}

	public static Predicate withTenantId(Long tenantId) {
		return QProductEntity.productEntity.tenantId.eq(tenantId);
	}

	public static Predicate withStoreId(Long storeId) {
		return QProductEntity.productEntity.storeId.eq(storeId);
	}

	public static Predicate withLikeSku(String sku) {
		return QProductEntity.productEntity.sku.containsIgnoreCase(sku);
	}

	public static Predicate withSKu(String sku) {
		return QProductEntity.productEntity.sku.eq(sku);
	}

	public static Predicate withBarcode(String barcode) {
		return QProductEntity.productEntity.barcode.eq(barcode);
	}

	public static Predicate fromUnitPrice(BigInteger fromUnitPrice) {
		return QProductEntity.productEntity.unitPrice.goe(fromUnitPrice);
	}

	public static Predicate toUnitPrice(BigInteger toUnitPrice) {
		return QProductEntity.productEntity.unitPrice.loe(toUnitPrice);
	}

	public static Predicate withStatuses(List<ProductStatus> statuses) {
		return QProductEntity.productEntity.status.in(statuses);
	}

	public static Predicate withMigrationId(String migrationId) {
		return QProductEntity.productEntity.migrationId.eq(migrationId);
	}

	public static Predicate withDeletedIsNull() {
		return QProductEntity.productEntity.deletedAt.isNull();
	}

}
