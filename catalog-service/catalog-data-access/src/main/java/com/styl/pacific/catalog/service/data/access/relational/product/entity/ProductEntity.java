/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.entity;

import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity.HealthierChoiceEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.converter.KeywordsDataConverter;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "tb_product")
@SQLDelete(sql = "UPDATE tb_product SET status = 'ARCHIVED', deleted_at = current_timestamp WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
@DynamicUpdate
@EqualsAndHashCode
@SuperBuilder
@EntityListeners(AuditingEntityListener.class)
public class ProductEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_STORE_ID = "storeId";
	public static final String FIELD_NAME = "name";
	public static final String FIELD_UNIT_PRICE = "unitPrice";
	public static final String FIELD_SKU = "sku";
	public static final String FIELD_BARCODE = "barcode";
	public static final String FIELD_STATUS = "status";
	public static final String FIELD_CATEGORY_ID = "categoryId";
	public static final String FIELD_HEALTHIER_CHOICE_ID = "healthierChoiceId";
	public static final String FIELD_CATEGORY = "category";
	public static final String FIELD_HEALTHIER_CHOICE = "healthierChoice";
	public static final String FIELD_IMAGES = "images";
	public static final String FIELD_OPTIONS = "options";
	public static final String FIELD_NUTRITION = "nutrition";
	public static final String FIELD_ALLERGENS = "allergens";
	public static final String FIELD_CREATED_AT = "createdAt";
	public static final String FIELD_UPDATED_AT = "updatedAt";
	public static final String FIELD_DELETED_AT = "deletedAt";

	public static final Set<String> SORTABLE_FIELDS = Set.of(FIELD_ID, FIELD_NAME, FIELD_UNIT_PRICE, FIELD_STATUS,
			FIELD_SKU, FIELD_CREATED_AT);

	@Id
	private Long id;

	@Column(nullable = false)
	private Long tenantId;

	private String migrationId;

	@Column(name = "category_id", nullable = false)
	private Long categoryId;

	@Column(nullable = false)
	private Long storeId;

	@Column(nullable = false)
	private String name;

	@Column(nullable = false)
	private BigInteger unitPrice;

	private BigInteger listingPrice;

	@Column(nullable = false)
	private String currencyCode;

	@Column
	private String briefInformation;

	@Column(nullable = false)
	private String description;

	@Column(nullable = false)
	private String sku;

	private String ingredients;

	@Column(nullable = false)
	private Integer preparationTime;

	@Column
	private String barcode;

	@Column(columnDefinition = "varchar(32) default 'AVAILABLE'")
	@Enumerated(value = EnumType.STRING)
	private ProductStatus status;

	@Column(name = "healthier_choice_id")
	private Long healthierChoiceId;

	@OneToMany(mappedBy = "product", cascade = CascadeType.ALL)
	private List<ProductImageEntity> images = new ArrayList<>();

	@OneToMany(mappedBy = "product", cascade = CascadeType.DETACH)
	private List<ProductOptionEntity> options = new ArrayList<>();

	@OneToMany(mappedBy = "product", cascade = CascadeType.DETACH)
	private List<ProductNutritionEntity> nutrition = new ArrayList<>();

	@OneToMany(mappedBy = "product", cascade = CascadeType.DETACH)
	private List<ProductAllergenEntity> allergens = new ArrayList<>();

	@Convert(converter = KeywordsDataConverter.class)
	private List<String> keywords;

	@ManyToOne
	@JoinColumn(name = "category_id", referencedColumnName = "id", nullable = false, insertable = false, updatable = false)
	private CategoryEntity category;

	@OneToOne
	@JoinColumn(name = "healthier_choice_id", referencedColumnName = "id", insertable = false, updatable = false)
	private HealthierChoiceEntity healthierChoice;

	@CreatedDate
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable = false, updatable = false)
	protected Instant createdAt;

	@LastModifiedDate
	@Temporal(TemporalType.TIMESTAMP)
	protected Instant updatedAt;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(insertable = false)
	protected Instant deletedAt;
}
