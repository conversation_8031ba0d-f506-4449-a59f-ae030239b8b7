/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.adapter;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.nutrition.specification.ProductNutritionPredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductAllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductImageEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductNutritionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductOptionEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.mapper.ProductDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductAllergenJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductImageJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductNutritionJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductOptionItemJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductOptionJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.specification.ProductAllergenPredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.specification.ProductImagePredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.specification.ProductOptionPredicates;
import com.styl.pacific.catalog.service.data.access.relational.product.specification.ProductPredicates;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ProductRepositoryImpl implements ProductRepository {
	private final ProductImageJpaRepository productImageJpaRepository;
	private final ProductOptionJpaRepository productOptionJpaRepository;
	private final ProductOptionItemJpaRepository productOptionItemJpaRepository;
	private final ProductJpaRepository productJpaRepository;
	private final ProductNutritionJpaRepository productNutritionJpaRepository;
	private final ProductAllergenJpaRepository productAllergenJpaRepository;

	@Override
	public Optional<Product> findById(TenantId tenantId, ProductId id) {
		Predicate specification = ProductPredicates.byTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return productJpaRepository.findOneWithFetchJoin(specification)
				.map(ProductDataAccessMapper.INSTANCE::toModel);
	}

	@Override
	public Optional<ProductDto> findDtoById(TenantId tenantId, ProductId id) {
		Predicate specification = ProductPredicates.byTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return productJpaRepository.findOneWithFetchJoinDetails(specification)
				.map(ProductDataAccessMapper.INSTANCE::toDto);
	}

	@Override
	public ProductDto saveDto(Product product) {
		var entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
		var result = productJpaRepository.save(entity);
		return ProductDataAccessMapper.INSTANCE.toDto(result);
	}

	@Override
	public ProductDto updateDto(Product product) {
		var entity = ProductDataAccessMapper.INSTANCE.toEntity(product);
		if (!Objects.isNull(entity.getImages())) {
			productImageJpaRepository.deleteAllNotIn(entity.getId(), new ArrayList<>(entity.getImages()));
		}

		var result = productJpaRepository.saveAndFlush(entity);
		return ProductDataAccessMapper.INSTANCE.toDto(result);
	}

	@Override
	public void updateStatus(TenantId tenantId, ProductId id, ProductStatus status) {
		Predicate specification = ProductPredicates.byTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		productJpaRepository.findOne(specification)
				.ifPresent(entity -> {
					entity.setStatus(status);
					productJpaRepository.saveAndFlush(entity);
				});
	}

	@Override
	public Paging<ProductDto> findAllPaging(TenantId tenantId, PaginationProductQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), ProductEntity.SORTABLE_FIELDS, ProductEntity.FIELD_ID);

		ProductFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(ProductFilter.builder()
						.build());

		Predicate spec = ProductPredicates.byMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.ids(), filter.migrationId(), filter.name(), filter.healthierChoiceIds(), filter
						.categoryIds(), filter.storeId(), filter.barcode(), filter.sku(), filter.statuses(), filter
								.fromUnitPrice(), filter.toUnitPrice());

		Page<ProductEntity> page = productJpaRepository.findBy(spec, q -> q.project(ProductEntity.FIELD_CATEGORY,
				ProductEntity.FIELD_HEALTHIER_CHOICE)
				.page(pageable));

		Map<Long, List<ProductNutritionEntity>> nutritionEntities = findAllNutritionByProductIds(page.getContent()
				.stream()
				.map(ProductEntity::getId)
				.toList()).stream()
				.collect(Collectors.groupingBy(productNutritionEntity -> productNutritionEntity.getId()
						.getProductId()));
		Map<Long, List<ProductAllergenEntity>> allergenEntities = findAllAllergenByProductIds(page.getContent()
				.stream()
				.map(ProductEntity::getId)
				.toList()).stream()
				.collect(Collectors.groupingBy(productAllergenEntity -> productAllergenEntity.getId()
						.getProductId()));
		Map<Long, List<ProductImageEntity>> imageEntities = findAllImagesByProductIds(page.getContent()
				.stream()
				.map(ProductEntity::getId)
				.toList()).stream()
				.collect(Collectors.groupingBy(ProductImageEntity::getProductId));
		Map<Long, List<ProductOptionEntity>> optionEntities = findAllOptionsByProductIds(page.getContent()
				.stream()
				.map(ProductEntity::getId)
				.toList()).stream()
				.collect(Collectors.groupingBy(ProductOptionEntity::getProductId));
		page.getContent()
				.forEach(productEntity -> {
					productEntity.setNutrition(nutritionEntities.getOrDefault(productEntity.getId(), Collections
							.emptyList()));
					productEntity.setAllergens(allergenEntities.getOrDefault(productEntity.getId(), Collections
							.emptyList()));
					productEntity.setImages(imageEntities.getOrDefault(productEntity.getId(), Collections.emptyList()));
					productEntity.setOptions(optionEntities.getOrDefault(productEntity.getId(), Collections
							.emptyList()));
				});

		return Paging.<ProductDto>builder()
				.page(page.getPageable()
						.getPageNumber())
				.sort(page.getPageable()
						.getSort()
						.toList()
						.stream()
						.map(Sort.Order::toString)
						.toList())
				.content(page.getContent()
						.stream()
						.map(ProductDataAccessMapper.INSTANCE::toDto)
						.toList())
				.totalElements(page.getTotalElements())
				.totalPages(page.getTotalPages())
				.build();
	}

	private List<ProductNutritionEntity> findAllNutritionByProductIds(List<Long> productIds) {
		if (Objects.isNull(productIds) || productIds.isEmpty()) {
			return new ArrayList<>();
		}
		Predicate predicate = ProductNutritionPredicates.withInProductIds(productIds);
		return productNutritionJpaRepository.findBy(predicate, q -> q.project(ProductNutritionEntity.FIELD_NUTRITION)
				.all());
	}

	private List<ProductAllergenEntity> findAllAllergenByProductIds(List<Long> productIds) {
		if (Objects.isNull(productIds) || productIds.isEmpty()) {
			return new ArrayList<>();
		}
		Predicate predicate = ProductAllergenPredicates.joinAllergenWithInProductIds(productIds);
		return productAllergenJpaRepository.findBy(predicate, q -> q.project(ProductAllergenEntity.FIELD_ALLERGEN)
				.all());
	}

	private List<ProductImageEntity> findAllImagesByProductIds(List<Long> productIds) {
		if (Objects.isNull(productIds) || productIds.isEmpty()) {
			return new ArrayList<>();
		}
		Predicate predicate = ProductImagePredicates.withInProductIds(productIds);
		return productImageJpaRepository.findBy(predicate, FluentQuery.FetchableFluentQuery::all);
	}

	private List<ProductOptionEntity> findAllOptionsByProductIds(List<Long> productIds) {
		if (Objects.isNull(productIds) || productIds.isEmpty()) {
			return new ArrayList<>();
		}
		Predicate predicate = ProductOptionPredicates.withInProductIds(productIds);
		return productOptionJpaRepository.findBy(predicate, q -> q.project(ProductOptionEntity.FIELD_ITEMS)
				.all());
	}

	@Override
	public boolean existById(TenantId tenantId, ProductId id) {
		Predicate spec = ProductPredicates.byTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return productJpaRepository.exists(spec);
	}

	@Override
	public boolean existBySku(TenantId tenantId, String sku) {
		Predicate spec = ProductPredicates.byTenantIdAndSku(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), sku);
		return productJpaRepository.exists(spec);
	}

	@Override
	public boolean existByMigrationId(TenantId tenantId, String migrationId) {
		Predicate spec = ProductPredicates.byTenantIdAndMigrationId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), migrationId);
		return productJpaRepository.exists(spec);
	}

	@Override
	public boolean existBySkuNotId(TenantId tenantId, ProductId id, String sku) {
		Predicate spec = ProductPredicates.byTenantIdAndSkuNotId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue(), sku);
		return productJpaRepository.exists(spec);
	}

	@Override
	public boolean existByQuery(TenantId tenantId, ProductFilter query) {
		ProductFilter filter = Optional.ofNullable(query)
				.orElse(ProductFilter.builder()
						.build());

		Predicate spec = ProductPredicates.byMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.ids(), filter.migrationId(), filter.name(), filter.healthierChoiceIds(), filter
						.categoryIds(), filter.storeId(), filter.barcode(), filter.sku(), filter.statuses(), filter
								.fromUnitPrice(), filter.toUnitPrice());

		return productJpaRepository.exists(spec);
	}

	@Override
	public void deleteById(TenantId tenantId, ProductId id) {
		Predicate spec = ProductPredicates.byTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		Optional<ProductEntity> productOptional = productJpaRepository.findOne(spec);

		if (productOptional.isPresent()) {
			ProductEntity product = productOptional.get();

			// Delete Options
			if (Objects.nonNull(product.getOptions())) {
				List<Long> optionIds = product.getOptions()
						.stream()
						.map(ProductOptionEntity::getId)
						.toList();
				productOptionItemJpaRepository.deleteAllByOptionIds(optionIds);
				productOptionJpaRepository.deleteAll(product.getOptions());
				product.getOptions()
						.clear();
			}
			// Delete Images
			if (Objects.nonNull(product.getImages())) {
				productImageJpaRepository.deleteAll(product.getImages());
				product.getImages()
						.clear();
			}
			// Delete Nutrition
			if (Objects.nonNull(product.getNutrition())) {
				productNutritionJpaRepository.deleteAll(product.getNutrition());
				product.getNutrition()
						.clear();
			}
			// Delete Allergen
			if (Objects.nonNull(product.getAllergens())) {
				productAllergenJpaRepository.deleteAll(product.getAllergens());
				product.getAllergens()
						.clear();
			}
			productJpaRepository.delete(product);
		}

	}

	@Override
	public List<String> getAllProductName(TenantId tenantId) {
		return productJpaRepository.getAllProductName(MapstructCommonDomainMapper.INSTANCE.tenantIdToLong(tenantId));
	}
}
