/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.product;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.ProductNutritionDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.allergen.UpdateProductAllergenCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductAllergenService;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductNutritionService;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductService;
import com.styl.pacific.catalog.service.presenter.rest.allergen.mapper.AllergenRestMapper;
import com.styl.pacific.catalog.service.presenter.rest.product.mapper.ProductRestMapper;
import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.catalog.service.shared.http.product.ProductApi;
import com.styl.pacific.catalog.service.shared.http.product.request.CreateProductRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.PaginationProductQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.UpdateProductRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.allergen.UpdateProductAllergenRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.nutrition.UpdateProductNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.catalog.service.shared.http.product.response.nutrition.ListProductNutritionResponse;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
public class ProductController implements ProductApi {
	private final ProductService productDomainService;
	private final ProductAllergenService productAllergenService;
	private final ProductNutritionService productNutritionService;
	private final RequestContext requestContext;

	@Override
	public Paging<ProductResponse> findAllPaging(PaginationProductQueryRequest query) {
		PaginationProductQuery productsQuery = ProductRestMapper.INSTANCE.toQuery(query);
		Paging<ProductDto> paging = productDomainService.findAllPaging(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), productsQuery);
		return Paging.<ProductResponse>builder()
				.content(paging.getContent()
						.stream()
						.map(ProductRestMapper.INSTANCE::toResponse)
						.toList())
				.page(paging.getPage())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.sort(paging.getSort())
				.build();
	}

	@Override
	public ProductResponse findById(String id) {
		ProductDto dto = productDomainService.findById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
		return ProductRestMapper.INSTANCE.toResponse(dto);
	}

	@Override
	public ProductResponse create(CreateProductRequest request) {
		CreateProductCommand command = ProductRestMapper.INSTANCE.toCommand(request);
		ProductDto dto = productDomainService.create(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), command);
		return ProductRestMapper.INSTANCE.toResponse(dto);
	}

	@Override
	public ProductResponse update(String id, UpdateProductRequest request) {
		UpdateProductCommand command = ProductRestMapper.INSTANCE.toCommand(request);
		var dto = productDomainService.update(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null), command);
		return ProductRestMapper.INSTANCE.toResponse(dto);
	}

	@Override
	public void activate(String id) {
		productDomainService.activate(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
	}

	@Override
	public void deactivate(String id) {
		productDomainService.deactivate(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
	}

	@Override
	public void archive(String id) {
		productDomainService.archive(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
	}

	@Override
	public void delete(String id) {
		productDomainService.deleteById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
	}

	@Override
	public ListProductNutritionResponse findAllRelatedNutrition(String id) {
		List<ProductNutritionDto> productNutritionList = productNutritionService.findAllByProductId(Optional.ofNullable(
				requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null));
		return new ListProductNutritionResponse(productNutritionList.stream()
				.map(ProductRestMapper.INSTANCE::toResponse)
				.toList());
	}

	@Override
	public ListProductNutritionResponse updateRelatedNutrition(String id, List<UpdateProductNutritionRequest> request) {
		List<UpdateProductNutritionCommand> command = request.stream()
				.map(ProductRestMapper.INSTANCE::toCommand)
				.toList();
		List<ProductNutritionDto> productNutritionList = productNutritionService.updateNutrition(Optional.ofNullable(
				requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null), command);
		return new ListProductNutritionResponse(productNutritionList.stream()
				.map(ProductRestMapper.INSTANCE::toResponse)
				.toList());
	}

	@Override
	public Content<AllergenResponse> findAllRelatedAllergens(String id) {
		List<AllergenResponse> allergenResponses = productAllergenService.findAllByProductId(Optional.ofNullable(
				requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null))
				.stream()
				.map(AllergenRestMapper.INSTANCE::toResponse)
				.toList();
		return Content.<AllergenResponse>builder()
				.content(allergenResponses)
				.build();
	}

	@Override
	public Content<AllergenResponse> updateRelatedAllergens(String id, List<UpdateProductAllergenRequest> allergens) {
		List<UpdateProductAllergenCommand> allergenCommands = allergens.stream()
				.map(ProductRestMapper.INSTANCE::toCommand)
				.toList();
		List<AllergenResponse> allergenResponses = productAllergenService.updateAllergens(Optional.ofNullable(
				requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ProductId::new)
						.orElse(null), allergenCommands)
				.stream()
				.map(AllergenRestMapper.INSTANCE::toResponse)
				.toList();
		return Content.<AllergenResponse>builder()
				.content(allergenResponses)
				.build();
	}

	@Override
	public List<String> getAllProductName() {
		return productDomainService.getAllProductName(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext
				.getTenantId()));
	}
}
