/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.healthierchoice;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.catalog.service.domain.healthierchoice.HealthierChoiceService;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.CreateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.UpdateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.PaginationHealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.presenter.rest.healthierchoice.mapper.HealthierChoiceRestMapper;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.HealthierChoiceApi;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.CreateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoicesQueryRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.PaginationHealthierChoiceQueryRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.UpdateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.response.HealthierChoiceResponse;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.response.ListHealthierChoicesResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequiredArgsConstructor
public class HealthierChoiceController implements HealthierChoiceApi {
	private final HealthierChoiceService healthierChoiceService;
	private final RequestContext requestContext;

	@Override
	public ListHealthierChoicesResponse findAll(HealthierChoicesQueryRequest query) {
		HealthierChoiceQuery queryModel = HealthierChoiceRestMapper.INSTANCE.toQuery(query);
		List<HealthierChoice> healthierChoices = healthierChoiceService.findAll(Optional.ofNullable(requestContext
				.getTenantId())
				.map(TenantId::new)
				.orElse(null), queryModel);
		return new ListHealthierChoicesResponse(healthierChoices.stream()
				.map(HealthierChoiceRestMapper.INSTANCE::healthierChoiceToResponse)
				.toList());
	}

	@Override
	public Paging<HealthierChoiceResponse> findAllPaging(PaginationHealthierChoiceQueryRequest query) {
		PaginationHealthierChoiceQuery queryModel = HealthierChoiceRestMapper.INSTANCE.toQuery(query);
		Paging<HealthierChoice> paging = healthierChoiceService.findAllPaging(Optional.ofNullable(requestContext
				.getTenantId())
				.map(TenantId::new)
				.orElse(null), queryModel);
		return Paging.<HealthierChoiceResponse>builder()
				.content(paging.getContent()
						.stream()
						.map(HealthierChoiceRestMapper.INSTANCE::healthierChoiceToResponse)
						.toList())
				.page(paging.getPage())
				.totalPages(paging.getTotalPages())
				.totalElements(paging.getTotalElements())
				.sort(paging.getSort())
				.build();
	}

	@Override
	public HealthierChoiceResponse findById(String id) {
		HealthierChoice healthierChoice = healthierChoiceService.findById(Optional.ofNullable(requestContext
				.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(b -> new HealthierChoiceId(NumberUtils.createLong(id)))
						.orElse(null));
		return HealthierChoiceRestMapper.INSTANCE.healthierChoiceToResponse(healthierChoice);
	}

	@Override
	public HealthierChoiceResponse create(CreateHealthierChoiceRequest request) {
		CreateHealthierChoiceCommand command = HealthierChoiceRestMapper.INSTANCE.createHealthierChoiceRequestToCommand(
				request);
		HealthierChoice model = healthierChoiceService.create(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), command);
		return HealthierChoiceRestMapper.INSTANCE.healthierChoiceToResponse(model);
	}

	@Override
	public HealthierChoiceResponse update(String id, UpdateHealthierChoiceRequest request) {
		UpdateHealthierChoiceCommand command = HealthierChoiceRestMapper.INSTANCE.updateHealthierChoiceRequestToCommand(
				request);
		HealthierChoice model = healthierChoiceService.update(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(b -> new HealthierChoiceId(NumberUtils.createLong(id)))
						.orElse(null), command);
		return HealthierChoiceRestMapper.INSTANCE.healthierChoiceToResponse(model);
	}

	@Override
	public void delete(String id) {
		healthierChoiceService.deleteById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.of(NumberUtils.isCreatable(id))
						.filter(Boolean::booleanValue)
						.map(b -> new HealthierChoiceId(NumberUtils.createLong(id)))
						.orElse(null));
	}

}
