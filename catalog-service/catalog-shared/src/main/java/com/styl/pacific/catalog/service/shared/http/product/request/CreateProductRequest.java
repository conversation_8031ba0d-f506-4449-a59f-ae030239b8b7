/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.product.request;

import com.styl.pacific.catalog.service.shared.http.product.request.image.CreateProductImageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record CreateProductRequest(
		@Size(max = 100, message = "migrationId must not exceed 100 characters") String migrationId,
		@NotNull(message = "categoryId must not be null") @Digits(integer = 21, fraction = 0, message = "categoryId must be a valid number") String categoryId,
		@NotNull(message = "storeId must not be null") @Digits(integer = 21, fraction = 0, message = "storeId must be a valid number") String storeId,
		@Digits(integer = 21, fraction = 0, message = "healthierChoiceId must be a valid number") String healthierChoiceId,
		@Size(max = 100, message = "name must not exceed 100 characters") @NotBlank(message = "name must not be empty or null") String name,
		@Size(max = 255, message = "briefInformation must not exceed 255 characters") String briefInformation,
		@Size(max = 255, message = "description must not exceed 255 characters") String description,
		@Size(max = 500, message = "ingredients must not exceed 500 characters") String ingredients,
		String barcode,
		@Size(min = 8, max = 32, message = "sku must be between 8 and 32 characters") @NotBlank(message = "sku must not be empty or null") String sku,
		@NotNull(message = "unitPrice must not be null") @Min(value = 0, message = "unitPrice must greater than or equal 0") BigInteger unitPrice,
		@Min(value = 0, message = "listingPrice must greater than or equal 0") BigInteger listingPrice,
		@Min(value = 0, message = "preparationTime must be greater than or equal to 0") @NotNull(message = "preparationTime must not be null") Integer preparationTime,
		@Valid @Size(max = 10, message = "images must have at most 10 images") List<CreateProductImageRequest> images,
		@Size(max = 50, message = "keywords must have at most 50 keywords") List<@NotBlank(message = "keyword must not be blank") @Size(max = 20, message = "keyword must not exceed 20 characters") String> keywords) {

}
