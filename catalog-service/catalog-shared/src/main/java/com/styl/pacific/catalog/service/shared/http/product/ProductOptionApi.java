/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.product;

import com.styl.pacific.catalog.service.shared.http.product.request.option.CreateProductOptionRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.option.ProductOptionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.option.UpdateProductOptionRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.option.ListProductOptionResponse;
import com.styl.pacific.catalog.service.shared.http.product.response.option.ProductOptionResponse;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface ProductOptionApi {
	@GetMapping("/api/catalog/products/{productId}/options")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListProductOptionResponse findAll(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "productId must be a valid number") String productId,
			@Valid @SpringQueryMap @ModelAttribute ProductOptionQueryRequest request);

	@GetMapping("/api/catalog/products/{productId}/options/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ProductOptionResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "productId must be a valid number") String productId,
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/catalog/products/{productId}/options")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized
	ProductOptionResponse create(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "productId must be a valid number") String productId,
			@Valid @RequestBody CreateProductOptionRequest request);

	@PutMapping("/api/catalog/products/{productId}/options/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ProductOptionResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "productId must be a valid number") String productId,
			@PathVariable String id, @Valid @RequestBody UpdateProductOptionRequest request);

	@DeleteMapping("/api/catalog/products/{productId}/options/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized
	void deleteById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "productId must be a valid number") String productId,
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);
}
