/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.category;

import com.styl.pacific.catalog.service.shared.http.category.request.CategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.CreateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.PaginationCategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.TreeCategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.UpdateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryResponse;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryStubResponse;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface CategoryApi {
	@GetMapping(value = "/api/catalog/categories")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<CategoryStubResponse> findAll(@Valid @SpringQueryMap CategoryQueryRequest query);

	@GetMapping(value = "/api/catalog/categories/page")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<CategoryStubResponse> findAllPaging(@Valid @SpringQueryMap PaginationCategoryQueryRequest query);

	@GetMapping(value = "/api/catalog/categories/tree")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<CategoryResponse> findAllTree(@Valid @SpringQueryMap TreeCategoryQueryRequest query);

	@GetMapping(value = "/api/catalog/categories/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	CategoryResponse findById(
			@Digits(integer = 21, fraction = 0, message = "id must be valid number") @PathVariable String id);

	@PostMapping(value = "/api/catalog/categories")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_CATEGORY_ADD)
	CategoryStubResponse create(@Valid @RequestBody CreateCategoryRequest request);

	@PutMapping(value = "/api/catalog/categories/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_CATEGORY_UPDATE)
	CategoryStubResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id,
			@Valid @RequestBody UpdateCategoryRequest request);

	@DeleteMapping(value = "/api/catalog/categories/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_CATEGORY_DELETE)
	void deleteById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);
}
