/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.ouput.repository;

import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ProductRepository {
	Optional<Product> findById(TenantId tenantId, ProductId id);

	void updateStatus(TenantId tenantId, ProductId id, ProductStatus status);

	void deleteById(TenantId tenantId, ProductId id);

	Paging<ProductDto> findAllPaging(TenantId tenantId, PaginationProductQuery query);

	Optional<ProductDto> findDtoById(TenantId tenantId, ProductId id);

	ProductDto saveDto(Product product);

	ProductDto updateDto(Product product);

	boolean existById(TenantId tenantId, ProductId id);

	boolean existBySku(TenantId tenantId, String sku);

	boolean existByMigrationId(TenantId tenantId, String migrationId);

	boolean existBySkuNotId(TenantId tenantId, ProductId id, String sku);

	boolean existByQuery(TenantId tenantId, ProductFilter query);

	List<String> getAllProductName(TenantId tenantId);
}
