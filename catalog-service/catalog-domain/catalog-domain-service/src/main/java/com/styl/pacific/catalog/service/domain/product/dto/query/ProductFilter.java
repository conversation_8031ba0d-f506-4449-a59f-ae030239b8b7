/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.dto.query;

import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record ProductFilter(@Size(max = 100, message = "ids size must be less than or equal to 100") List<Long> ids,
		String migrationId,
		String name,
		@Size(max = 100, message = "healthierChoiceIds size must be less than or equal to 100") List<Long> healthierChoiceIds,
		@Size(max = 100, message = "categoryIds size must be less than or equal to 100") List<Long> categoryIds,
		Long storeId,
		String barcode,
		String sku,
		List<ProductStatus> statuses,
		BigInteger fromUnitPrice,
		BigInteger toUnitPrice) {
}
