/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.category;

import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.command.CreateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.command.UpdateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.PaginationCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.TreeCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.handler.CategoryCommandHandler;
import com.styl.pacific.catalog.service.domain.category.handler.CategoryQueryHandler;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {
	private final CategoryCommandHandler categoryCommandHandler;
	private final CategoryQueryHandler categoryTrackQueryHandler;

	@Override
	public CategoryStubDto create(TenantId tenantId, CreateCategoryCommand command) {
		return categoryCommandHandler.create(tenantId, command);
	}

	@Override
	public CategoryStubDto update(TenantId tenantId, CategoryId id, UpdateCategoryCommand command) {
		return categoryCommandHandler.update(tenantId, id, command);
	}

	@Override
	public void deleteById(TenantId tenantId, CategoryId id) {
		categoryCommandHandler.deleteById(tenantId, id);
	}

	@Override
	public Content<CategoryStubDto> findAll(TenantId tenantId, CategoryQuery query) {
		return Content.<CategoryStubDto>builder()
				.content(categoryTrackQueryHandler.findAll(tenantId, query))
				.build();
	}

	@Override
	public Content<Category> findAllTree(TenantId tenantId, TreeCategoryQuery query) {
		return Content.<Category>builder()
				.content(categoryTrackQueryHandler.findAllTree(tenantId, query))
				.build();
	}

	@Override
	public Paging<CategoryStubDto> findAllPaging(TenantId tenantId, PaginationCategoryQuery query) {
		return categoryTrackQueryHandler.findAllPaging(tenantId, query);
	}

	@Override
	public Category findById(TenantId tenantId, CategoryId id) {
		return categoryTrackQueryHandler.findById(tenantId, id);
	}
}
