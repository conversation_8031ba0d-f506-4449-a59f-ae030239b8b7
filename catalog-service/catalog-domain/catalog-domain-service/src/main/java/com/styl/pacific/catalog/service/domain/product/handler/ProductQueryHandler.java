/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.handler;

import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.exception.ProductNotFoundException;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductQueryHandler {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private final ProductRepository productRepository;

	@Transactional(readOnly = true)
	public ProductDto findById(TenantId tenantId, ProductId id) {
		return productRepository.findDtoById(tenantId, id)
				.orElseThrow(() -> {
					logger.warn("Product {} not found", id);
					return new ProductNotFoundException(String.format("Product %s not found", id.getValue()));
				});
	}

	@Transactional(readOnly = true)
	public Paging<ProductDto> findAllPaging(TenantId tenantId, PaginationProductQuery query) {
		return productRepository.findAllPaging(tenantId, query);
	}

	@Transactional(readOnly = true)
	public List<String> getAllProductName(TenantId tenantId) {
		return productRepository.getAllProductName(tenantId);
	}
}
