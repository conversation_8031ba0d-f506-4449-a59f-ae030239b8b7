/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.dto.command;

import com.styl.pacific.catalog.service.domain.product.dto.command.allergen.UpdateProductAllergenCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.CreateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.nutrition.UpdateProductNutritionCommand;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CreateProductWithDetailsCommand {
	@Size(max = 100, message = "migrationId must not exceed 100 characters")
	private final String migrationId;
	@NotNull(message = "categoryId must not be null")
	private final Long categoryId;
	@NotNull(message = "storeId must not be null")
	private final Long storeId;
	private final Long healthierChoiceId;
	@Size(max = 80, message = "name must not exceed 80 characters")
	@NotBlank(message = "name must not be empty or null")
	private final String name;
	@Size(max = 255, message = "briefInformation must not exceed 255 characters")
	private final String briefInformation;
	@Size(max = 255, message = "description must not exceed 255 characters")
	private final String description;
	@Size(max = 500, message = "ingredients must not exceed 500 characters")
	private final String ingredients;
	private final String barcode;
	@Size(min = 8, max = 32, message = "sku must be between 8 and 32 characters")
	@NotBlank(message = "sku must not be empty or null")
	private final String sku;
	@NotNull(message = "unitPrice must not be null")
	@Min(value = 0, message = "unitPrice must greater than or equal 0")
	private final BigInteger unitPrice;
	@Min(value = 0, message = "listingPrice must greater than or equal 0")
	private final BigInteger listingPrice;
	@Min(value = 0, message = "preparationTime must be greater than or equal to 0")
	@NotNull(message = "preparationTime must not be null")
	private Integer preparationTime;
	@Valid
	@Size(max = 10, message = "images must have at most 10 images")
	private List<CreateProductImageCommand> images;
	@Valid
	@NotNull
	private List<UpdateProductAllergenCommand> allergens;
	@Valid
	@NotNull
	private List<UpdateProductNutritionCommand> nutrition;
	@Size(max = 50, message = "keywords must have at most 50 keywords")
	private List<@NotBlank(message = "keyword must not be blank") @Size(max = 20, message = "keyword must not exceed 20 characters") String> keywords;

}
