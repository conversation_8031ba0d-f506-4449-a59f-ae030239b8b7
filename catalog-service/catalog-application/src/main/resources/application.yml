server:
  port: 9203
spring:
  application:
    name: catalog-service
  sql:
    init:
      platform: postgres
  jpa:
    open-in-view: false
    show-sql: true
    database-platform: org.hibernate.dialect.PostgresPlusDialect
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgresPlusDialect
        jdbc:
          time_zone: UTC
          batch_size: 50
        generate_statistics: false
        order_inserts: true
        order_updates: true
        query:
          in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: **********************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    baselineOnMigrate: true
    validateOnMigrate: true
    locations: classpath:db/migration
  cloud:
    openfeign:
      client:
        config:
          default:
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            requestInterceptors:
              - com.styl.pacific.common.feign.interceptor.PlatformFeignHeaderForwarderInterceptor

pacific:
  clients:
    tenant-service:
      url: http://tenant-svc.application.svc.cluster.local:9201
    store-service:
      url: http://store-svc.application.svc.cluster.local:9204
    order-service:
      url: http://order-svc.application.svc.cluster.local:9205
  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317
  kafka:
    catalog-service:
      consumers:
        retry-attempts: 3
        retry-interval-ms: 3000
        tenant-created-event:
          enabled: true
          group-id: catalog-service-tenant-created-event
          topic-name: tenant-service-tenant-created-event
  aws:
    s3:
      endpoint:
      region: ap-southeast-1
      accessKey:
      secretKey:
kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.UUIDDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: false
  auto-startup: true
  concurrency-level: 3
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150

logging:
  level:
    com.styl.pacific: DEBUG

management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
      metrics:
        enabled: true
  endpoints:
    web:
      exposure:
        include: "*"