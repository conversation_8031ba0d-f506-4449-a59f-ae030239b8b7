/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class HealthierChoiceEntityTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Less than 100 calories";
	private static final String SYMBOL_URL = "https://ch-api.healthhub.sg/api//content/82aa9caf456a4a869a29c14dc6594073?v=f628ae31";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline are crisps/chips where each individually packaged portion is less than or equal to 100 calories. ";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant DELETED_AT = ZonedDateTime.now()
			.toInstant();

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		HealthierChoiceEntity entity = HealthierChoiceEntity.builder()
				.id(ID)
				.name(NAME)
				.tenantId(TENANT_ID)
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
		// Assert
		assertEquals(ID, entity.getId());
		assertEquals(TENANT_ID, entity.getTenantId());
		assertEquals(NAME, entity.getName());
		assertEquals(SYMBOL_URL, entity.getSymbolPath());
		assertEquals(DESCRIPTION, entity.getDescription());
		assertEquals(CREATED_AT, entity.getCreatedAt());
		assertEquals(UPDATED_AT, entity.getUpdatedAt());
		assertEquals(DELETED_AT, entity.getDeletedAt());
	}

	@Test
	void shouldReturnProperties_whenSetter() {
		// Arrange
		HealthierChoiceEntity entityMock = HealthierChoiceEntity.builder()
				.build();
		// Act
		entityMock.setId(ID);
		entityMock.setName(NAME);
		entityMock.setTenantId(TENANT_ID);
		entityMock.setDescription(DESCRIPTION);
		entityMock.setSymbolPath(SYMBOL_URL);
		entityMock.setCreatedAt(CREATED_AT);
		entityMock.setUpdatedAt(UPDATED_AT);
		entityMock.setDeletedAt(DELETED_AT);
		// Assert
		assertEquals(ID, entityMock.getId());
		assertEquals(TENANT_ID, entityMock.getTenantId());
		assertEquals(NAME, entityMock.getName());
		assertEquals(DESCRIPTION, entityMock.getDescription());
		assertEquals(SYMBOL_URL, entityMock.getSymbolPath());
		assertEquals(CREATED_AT, entityMock.getCreatedAt());
		assertEquals(UPDATED_AT, entityMock.getUpdatedAt());
		assertEquals(DELETED_AT, entityMock.getDeletedAt());
	}

	@Test
	void shouldEquals_whenHashCode() {
		// Arrange
		HealthierChoiceEntity entity1 = getEntity();
		HealthierChoiceEntity entity2 = getEntity();
		// Act
		boolean isEquals = entity1.hashCode() == entity2.hashCode();
		// Assert
		assertTrue(isEquals);
	}

	@Test
	void shouldNotEquals_whenHashCode() {
		// Arrange
		HealthierChoiceEntity entity1 = getEntity();
		HealthierChoiceEntity entity2 = getEntity();
		entity2.setName("New name");
		// Act
		boolean isEquals = entity1.hashCode() == entity2.hashCode();
		// Assert
		assertFalse(isEquals);
	}

	@Test
	void shouldTrue_whenEquals() {
		// Arrange
		HealthierChoiceEntity entity1 = getEntity();
		HealthierChoiceEntity entity2 = getEntity();
		// Act
		boolean isEquals = entity1.equals(entity2);
		// Assert
		assertTrue(isEquals);
	}

	@Test
	void shouldFalse_whenEquals() {
		// Arrange
		HealthierChoiceEntity entity1 = getEntity();
		HealthierChoiceEntity entity2 = getEntity();
		entity2.setName("New name");
		// Act
		boolean isEquals = entity1.equals(entity2);
		// Assert
		assertFalse(isEquals);
	}

	private HealthierChoiceEntity getEntity() {
		return HealthierChoiceEntity.builder()
				.id(ID)
				.name(NAME)
				.tenantId(TENANT_ID)
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}
}
