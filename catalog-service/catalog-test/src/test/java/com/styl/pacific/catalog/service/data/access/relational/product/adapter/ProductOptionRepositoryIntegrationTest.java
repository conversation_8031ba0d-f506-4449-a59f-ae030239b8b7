/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.product.dto.query.option.ProductOptionFilter;
import com.styl.pacific.catalog.service.domain.product.dto.query.option.ProductOptionQuery;
import com.styl.pacific.catalog.service.domain.product.entity.ProductOption;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductOptionRepository;
import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductOptionId;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class ProductOptionRepositoryIntegrationTest extends BaseDataJpaTest {
	private static final Long OPTION_ID_NEW = 15L;
	private static final Long OPTION_ID = 1L;
	private static final Long PRODUCT_ID = 1L;
	private static final String OPTION_TITLE = "option 1";
	private static final Integer OPTION_MINIMUM = 0;
	private static final Integer OPTION_MAXIMUM = 100;
	private static final Instant OPTION_CREATED_AT;
	private static final Instant OPTION_UPDATED_AT;

	static {
		DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
		OPTION_CREATED_AT = OffsetDateTime.parse("2024-05-13T15:05:46.685Z", formatter)
				.toInstant();
		OPTION_UPDATED_AT = OffsetDateTime.parse("2024-05-14T09:56:00.130Z", formatter)
				.toInstant();
	}

	@MockitoBean
	private MeterRegistry meterRegistry;

	@Autowired
	private ProductOptionRepository optionRepository;

	@Order(0)
	@Test
	void shouldReturnProductOption_whenFindById() {
		// Act
		Optional<ProductOption> optionExist = optionRepository.findById(new ProductId(PRODUCT_ID), new ProductOptionId(
				OPTION_ID));
		// Assert
		assertTrue(optionExist.isPresent());
		ProductOption option = optionExist.get();

		assertEquals(OPTION_ID, option.getId()
				.getValue());
		assertEquals(OPTION_TITLE, option.getTitle());
		assertEquals(PRODUCT_ID, option.getProductId()
				.getValue());
		assertEquals(OPTION_MINIMUM, option.getMinimum());
		assertEquals(OPTION_MAXIMUM, option.getMaximum());
		assertEquals(OPTION_CREATED_AT, option.getCreatedAt());
		assertEquals(OPTION_UPDATED_AT, option.getUpdatedAt());
	}

	@Order(1)
	@Test
	void shouldReturnProductOption_whenSave() {
		// Arrange
		ProductOption productOptionMock = ProductOption.builder()
				.id(new ProductOptionId(OPTION_ID_NEW))
				.title(OPTION_TITLE)
				.productId(new ProductId(PRODUCT_ID))
				.minimum(OPTION_MINIMUM)
				.maximum(OPTION_MAXIMUM)
				.createdAt(OPTION_CREATED_AT)
				.updatedAt(OPTION_UPDATED_AT)
				.build();
		// Act
		ProductOption option = optionRepository.save(productOptionMock);
		// Assert
		Optional<ProductOption> optionFind = optionRepository.findById(new ProductId(PRODUCT_ID), new ProductOptionId(
				OPTION_ID_NEW));
		assertTrue(optionFind.isPresent());
		assertEquals(productOptionMock.getId(), option.getId());
		assertEquals(productOptionMock.getTitle(), option.getTitle());
		assertEquals(productOptionMock.getProductId(), option.getProductId());
		assertEquals(productOptionMock.getMinimum(), option.getMinimum());
		assertEquals(productOptionMock.getMaximum(), option.getMaximum());
	}

	@Order(2)
	@Test
	void shouldReturnProductOption_whenUpdate() {
		// Arrange
		String newTitle = "New title";
		Optional<ProductOption> optionFind = optionRepository.findById(new ProductId(PRODUCT_ID), new ProductOptionId(
				OPTION_ID));
		assertTrue(optionFind.isPresent());
		ProductOption option = optionFind.get();
		option.setTitle(newTitle);
		// Act
		ProductOption optionUpdate = optionRepository.update(option);
		// Assert
		assertEquals(option.getTitle(), optionUpdate.getTitle());
	}

	@Order(3)
	@Test
	void shouldReturnListProductOptions_whenFindAll() {
		// Arrange
		int sizeExpect = 1;
		ProductOptionQuery queryMock = ProductOptionQuery.builder()
				.filter(ProductOptionFilter.builder()
						.title(OPTION_TITLE)
						.build())
				.build();
		// Act
		List<ProductOption> options = optionRepository.findAll(new ProductId(PRODUCT_ID), queryMock);
		// Assert
		assertEquals(sizeExpect, options.size());
	}

	@Order(4)
	@Test
	void shouldRemove_whenDeleteById() {
		// Act
		optionRepository.deleteById(new ProductId(PRODUCT_ID), new ProductOptionId(OPTION_ID_NEW));
		// Assert
		Optional<ProductOption> optionCheckExist = optionRepository.findById(new ProductId(PRODUCT_ID),
				new ProductOptionId(OPTION_ID_NEW));
		assertTrue(optionCheckExist.isEmpty());
	}
}
