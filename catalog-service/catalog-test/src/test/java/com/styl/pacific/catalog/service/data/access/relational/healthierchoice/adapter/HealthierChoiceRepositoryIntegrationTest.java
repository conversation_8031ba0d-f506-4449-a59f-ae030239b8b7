/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.healthierchoice.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceFilter;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.domain.healthierchoice.output.repository.HealthierChoiceRepository;
import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.TenantId;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

/**
 * <AUTHOR>
 */
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
public class HealthierChoiceRepositoryIntegrationTest extends BaseDataJpaTest {
	private static final Long ID = 1L;
	private static final Long NEW_ID = 5L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "Higher in Wholegrains";
	private static final String SYMBOL_URL = "bucket:image/ch-api.healthhub.sg/api/public/content/f0e5af85f9324fe086b54ea8b1931028?v=84677d70";
	private static final String DESCRIPTION = "Products carrying HCS with this tagline contain at least 20% more wholegrains compared to similar products from the same food category.";
	private static final Instant CREATED_AT;
	private static final Instant UPDATED_AT;

	static {
		DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
		CREATED_AT = OffsetDateTime.parse("2024-05-13T15:05:46.685Z", formatter)
				.toInstant();
		UPDATED_AT = OffsetDateTime.parse("2024-05-14T09:56:00.130Z", formatter)
				.toInstant();
	}

	@MockitoBean
	private MeterRegistry meterRegistry;

	@Autowired
	private HealthierChoiceRepository healthierChoiceRepository;

	@Test
	void shouldReturnListHealthierChoices_whenFindAll() {
		// Arrange
		HealthierChoiceQuery queryMock = HealthierChoiceQuery.builder()
				.filter(HealthierChoiceFilter.builder()
						.build())
				.build();
		int sizeExpect = 4;
		// Act
		List<HealthierChoice> healthierChoices = healthierChoiceRepository.findAll(new TenantId(TENANT_ID), queryMock);
		// Assert
		assertEquals(sizeExpect, healthierChoices.size());
	}

	@Test
	void shouldReturnHealthierChoice_whenFindById() {
		// Act
		Optional<HealthierChoice> healthierChoiceFind = healthierChoiceRepository.findById(new TenantId(TENANT_ID),
				new HealthierChoiceId(ID));
		// Assert
		assertTrue(healthierChoiceFind.isPresent());

		HealthierChoice healthierChoice = healthierChoiceFind.get();
		assertEquals(ID, healthierChoice.getId()
				.getValue());
		assertEquals(TENANT_ID, healthierChoice.getTenantId()
				.getValue());
		assertEquals(NAME, healthierChoice.getName());
		assertEquals(DESCRIPTION, healthierChoice.getDescription());
		assertEquals(SYMBOL_URL, healthierChoice.getSymbolPath());
		assertEquals(CREATED_AT, healthierChoice.getCreatedAt());
		assertEquals(UPDATED_AT, healthierChoice.getUpdatedAt());
	}

	@Test
	void shouldReturnHealthierChoice_whenSave() {
		// Arrange
		HealthierChoice healthierChoiceMock = HealthierChoice.builder()
				.id(new HealthierChoiceId(NEW_ID))
				.name(NAME)
				.tenantId(new TenantId(TENANT_ID))
				.symbolPath(SYMBOL_URL)
				.description(DESCRIPTION)
				.build();
		// Act
		HealthierChoice healthierChoiceSave = healthierChoiceRepository.save(healthierChoiceMock);
		// Assert
		Optional<HealthierChoice> healthierChoiceFind = healthierChoiceRepository.findById(new TenantId(TENANT_ID),
				new HealthierChoiceId(ID));
		assertTrue(healthierChoiceFind.isPresent());

		assertEquals(healthierChoiceMock.getId(), healthierChoiceSave.getId());
		assertEquals(healthierChoiceMock.getTenantId(), healthierChoiceSave.getTenantId());
		assertEquals(healthierChoiceMock.getDescription(), healthierChoiceSave.getDescription());
		assertEquals(healthierChoiceMock.getSymbolPath(), healthierChoiceSave.getSymbolPath());
		assertEquals(healthierChoiceMock.getName(), healthierChoiceSave.getName());
	}

	@Test
	void shouldReturnHealthierChoice_whenUpdate() {
		// Arrange
		String newDes = "New des";
		String newSymbolUrl = "new Url";
		String newName = "New name";

		Optional<HealthierChoice> healthierChoiceFind = healthierChoiceRepository.findById(new TenantId(TENANT_ID),
				new HealthierChoiceId(ID));
		assertTrue(healthierChoiceFind.isPresent());
		HealthierChoice healthierChoice = healthierChoiceFind.get();
		healthierChoice.setDescription(newDes);
		healthierChoice.setSymbolPath(newSymbolUrl);
		healthierChoice.setName(newName);
		// Act
		HealthierChoice healthierChoiceUpdate = healthierChoiceRepository.update(healthierChoice);
		// Assert
		assertEquals(healthierChoice.getId(), healthierChoiceUpdate.getId());
		assertEquals(healthierChoice.getTenantId(), healthierChoiceUpdate.getTenantId());
		assertEquals(healthierChoice.getDescription(), healthierChoiceUpdate.getDescription());
		assertEquals(healthierChoice.getSymbolPath(), healthierChoiceUpdate.getSymbolPath());
		assertEquals(healthierChoice.getName(), healthierChoiceUpdate.getName());
	}

	@Test
	void shouldInvolve_whenDeleteById() {
		// Act
		healthierChoiceRepository.deleteById(new TenantId(TENANT_ID), new HealthierChoiceId(ID));
		// Assert
		Optional<HealthierChoice> healthierChoiceCheckExist = healthierChoiceRepository.findById(new TenantId(
				TENANT_ID), new HealthierChoiceId(ID));
		assertTrue(healthierChoiceCheckExist.isEmpty());
	}
}
