/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.controller.nutrition.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.catalog.service.domain.nutrition.dto.command.CreateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.UpdateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.catalog.service.presenter.rest.nutrition.mapper.NutritionRestMapper;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.CreateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.UpdateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.NutritionResponse;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class NutritionRestMapperTest {
	private static final Long ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "nutrition";
	private static final String UNIT = "unit";
	private static final Instant CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant UPDATED_AT = ZonedDateTime.now()
			.toInstant();

	@Test
	void shouldReturnNutritionResponse_whenMapFromModel() {
		// Arrange
		Nutrition nutritionMock = Nutrition.builder()
				.id(new NutritionId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.unit(UNIT)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Act
		NutritionResponse response = NutritionRestMapper.INSTANCE.toResponse(nutritionMock);
		// Assert
		assertEquals(nutritionMock.getId()
				.getValue()
				.toString(), response.id());
		assertEquals(nutritionMock.getTenantId()
				.getValue()
				.toString(), response.tenantId());
		assertEquals(nutritionMock.getName(), response.name());
		assertEquals(nutritionMock.getUnit(), response.unit());
		assertEquals(nutritionMock.getCreatedAt()
				.toEpochMilli(), response.createdAt());
		assertEquals(nutritionMock.getUpdatedAt()
				.toEpochMilli(), response.updatedAt());
	}

	@Test
	void shouldReturnCreateNutritionCommand_whenMapFromRequest() {
		// Arrange
		CreateNutritionRequest requestMock = new CreateNutritionRequest(NAME, UNIT);
		// Act
		CreateNutritionCommand command = NutritionRestMapper.INSTANCE.toCommand(requestMock);
		// Assert
		assertEquals(requestMock.name(), command.name());
		assertEquals(requestMock.unit(), command.unit());
	}

	@Test
	void shouldReturnUpdateNutritionCommand_whenMapFromRequest() {
		// Arrange
		UpdateNutritionRequest requestMock = new UpdateNutritionRequest(NAME, UNIT);
		// Act
		UpdateNutritionCommand command = NutritionRestMapper.INSTANCE.toCommand(requestMock);
		// Assert
		assertEquals(requestMock.name(), command.name());
		assertEquals(requestMock.unit(), command.unit());
	}
}
