/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.domain.product.port.ouput.repository.ProductRepository;
import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import io.micrometer.core.instrument.MeterRegistry;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class ProductRepositoryIntegrationTest extends BaseDataJpaTest {
	private static final Long PRODUCT_ID = 11L;
	private static final Long CATEGORY_ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final Long HEALTHIER_CHOICE_ID = 1L;
	private static final String NAME = "Product 11";
	private static final String BARCODE = "1100000";
	private static final String SKU = "SKU11";
	private static final String BRIEF_DESCRIPTION = "briefDescription";
	private static final String DESCRIPTION = "description";
	private static final String INGREDIENTS = "ingredients";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final Integer PREPARATION_TIME = 1;
	private static final BigInteger UNIT_PRICE = BigInteger.valueOf(100);
	private static final String CURRENCY_CODE = "SGD";
	private static final Long NUTRITION_ID = 1L;

	@MockitoBean
	private MeterRegistry meterRegistry;

	@Autowired
	private ProductRepository productRepository;

	@Test
	@Order(0)
	void shouldReturnPageProductEntity_whenFindBySpec() {
		// Arrange
		int page = 0;
		int size = 10;

		int totalNumberElementsExpect = 3;

		PaginationProductQuery query = new PaginationProductQuery(ProductFilter.builder()
				.categoryIds(List.of(CATEGORY_ID))
				.build(), size, page, "ASC", Set.of(ProductEntity.FIELD_NAME));

		// Act
		Paging<ProductDto> paging = productRepository.findAllPaging(new TenantId(TENANT_ID), query);
		// Assert
		assertEquals(totalNumberElementsExpect, paging.getContent()
				.size());
	}

	@Test
	@Order(1)
	void shouldReturnProductSave_whenSave() {
		// Arrange
		Product product = getProductArrange();
		// Act
		productRepository.saveDto(product);
		// Assert
		Optional<Product> productFind = productRepository.findById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		assertTrue(productFind.isPresent());
		Product productExist = productFind.get();
		assertEquals(product.getId()
				.getValue(), productExist.getId()
						.getValue());
		assertEquals(product.getCategoryId()
				.getValue(), productExist.getCategoryId()
						.getValue());
		assertEquals(product.getStoreId()
				.getValue(), productExist.getStoreId()
						.getValue());
		assertEquals(product.getTenantId()
				.getValue(), productExist.getTenantId()
						.getValue());
		assertEquals(product.getName(), productExist.getName());
		assertEquals(product.getUnitPrice()
				.getAmount(), productExist.getUnitPrice()
						.getAmount());
		assertEquals(product.getBriefInformation(), productExist.getBriefInformation());
		assertEquals(product.getDescription(), productExist.getDescription());
		assertEquals(product.getSku(), productExist.getSku());
		assertEquals(product.getStatus(), productExist.getStatus());
		assertEquals(product.getIngredients(), productExist.getIngredients());
		assertEquals(product.getBarcode(), productExist.getBarcode());
		Assertions.assertNotNull(productExist.getCreatedAt());
		Assertions.assertNotNull(productExist.getUpdatedAt());
	}

	@Test
	@Order(2)
	void shouldReturnProductUpdate_whenUpdate() {
		// Arrange
		long productId = 1L;
		Long newCategoryId = 2L;
		String newName = "New Product 11";
		String newSku = "SKU12";
		Long newImageId = 3L;
		String newImageUrl = "bucket:image/image.png";

		Long newNutritionId = 3L;
		Double newNutritionValue = 90.0;
		ProductStatus newStatus = ProductStatus.UNAVAILABLE;
		Optional<Product> productCheck = productRepository.findById(new TenantId(TENANT_ID), new ProductId(productId));
		assertTrue(productCheck.isPresent());
		Product product = productCheck.get();

		product.setCategoryId(new CategoryId(newCategoryId));
		product.setName(newName);
		product.setSku(newSku);
		product.setStatus(newStatus);

		ProductImage newImage = ProductImage.builder()
				.id(new ProductImageId(newImageId))
				.productId(new ProductId(productId))
				.imagePath(newImageUrl)
				.position(2)
				.build();
		product.getImages()
				.add(newImage);
		product.getImages()
				.getFirst()
				.setImagePath(newImageUrl);
		// Act
		productRepository.updateDto(product);
		// Assert
		Optional<Product> productFind = productRepository.findById(new TenantId(TENANT_ID), new ProductId(productId));
		assertTrue(productFind.isPresent());
		Product productExist = productFind.get();
		assertEquals(product.getId()
				.getValue(), productExist.getId()
						.getValue());
		assertEquals(product.getCategoryId()
				.getValue(), productExist.getCategoryId()
						.getValue());
		assertEquals(product.getName(), productExist.getName());
		assertEquals(product.getSku(), productExist.getSku());
		assertEquals(product.getStatus(), productExist.getStatus());

		assertEquals(product.getNutrition()
				.size(), productExist.getNutrition()
						.size());
		assertEquals(product.getImages()
				.size(), productExist.getImages()
						.size());
		assertEquals(newImageUrl, productExist.getImages()
				.getLast()
				.getImagePath());
		assertEquals(newImageUrl, productExist.getImages()
				.getFirst()
				.getImagePath());
	}

	@Test
	@Order(3)
	void shouldReturnProduct_whenGetById() {
		// Arrange
		long productId = 1L;
		// Act
		Optional<Product> proOptional = productRepository.findById(new TenantId(TENANT_ID), new ProductId(productId));
		// Assert
		assertTrue(proOptional.isPresent());
	}

	@Test
	@Order(4)
	void shouldRemoveProduct_whenDeleteById() {
		// Arrange
		long productId = 1L;
		// Act
		productRepository.deleteById(new TenantId(TENANT_ID), new ProductId(productId));
		// Assert
		Optional<Product> proOptional = productRepository.findById(new TenantId(TENANT_ID), new ProductId(productId));
		assertTrue(proOptional.isEmpty());
	}

	@Test
	@Order(5)
	void shouldReturnTrue_whenCheckExist() {
		// Arrange
		Long existProductId = 1L;
		// Act
		boolean isExist = productRepository.existById(new TenantId(TENANT_ID), new ProductId(existProductId));
		// Assert
		assertTrue(isExist);
	}

	@Test
	@Order(6)
	void shouldReturnFalse_whenCheckExist() {
		// Arrange
		Long notExistProductId = 11L;
		Long tenantId = 2L;
		// Act
		boolean isExist = productRepository.existById(new TenantId(tenantId), new ProductId(notExistProductId));
		// Assert
		assertFalse(isExist);
	}

	Product getProductArrange() {
		return Product.builder()
				.id(new ProductId(PRODUCT_ID))
				.tenantId(new TenantId(TENANT_ID))
				.healthierChoiceId(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
				.categoryId(new CategoryId(CATEGORY_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.sku(SKU)
				.barcode(BARCODE)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.status(STATUS)
				.unitPrice(new Money(UNIT_PRICE))
				.currencyCode(CURRENCY_CODE)
				.preparationTime(PREPARATION_TIME)
				.build();
	}
}
