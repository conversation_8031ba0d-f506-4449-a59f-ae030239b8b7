/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.CreateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.image.UpdateProductImageCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionItemCommand;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.catalog.service.features.tenant.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.FluxExchangeResult;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class ProductControllerIntegrationTest extends BaseWebClientWithDbTest {

	// Path
	private static final String PRODUCT_PATH = "/api/catalog/products";
	private static final String PRODUCT_PATH_ID = "/api/catalog/products/{productId}";

	private static final Long PRODUCT_ID = 1L;
	private static final Long CATEGORY_ID = 1L;
	private static final Long OTHER_CATEGORY_ID = 4L;
	private static final Long STORE_ID = 1L;
	private static final Long STORE_ID_2 = 2L;
	private static final Long TENANT_ID = 2L;
	private static final Long OTHER_TENANT_ID = 3L;
	private static final Long HEALTHIER_CHOICE_ID = 1L;
	private static final String NAME = "Product 1";
	private static final String BARCODE = "100000";
	private static final String SKU = "SKU1";
	private static final String BRIEF_DESCRIPTION = "Andy shoes are designed to keeping in mind 1";
	private static final String DESCRIPTION = "The automobile layout consists of a front-engine design, with transaxle-types transmissions mounted at the rear of the engine and four wheel drive 1";
	private static final String INGREDIENTS = "ingredient1,ingredient2,ingredient3";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final BigInteger UNIT_PRICE = BigInteger.valueOf(10000);
	private static final Integer PREPARATION_TIME = 1;
	private static final String IMAGE_PATH = "bucket:image/ch-api.healthhub.png";
	private static final List<String> KEYWORDS = List.of("keyword1", "keyword2");

	private static final String OPTION_TITLE = "option 1";
	private static final Integer OPTION_MINIMUM = 0;
	private static final Integer OPTION_MAXIMUM = 100;

	private static final String OPTION_ITEM_NAME = "Item 1";
	private static final BigInteger OPTION_ITEM_ADDITION_PRICE = BigInteger.valueOf(1000);

	private static final String CURRENCY_CODE = "SGD";

	private static final AtomicReference<String> productId = new AtomicReference<>();

	@Autowired
	@Qualifier("storeService")
	private WireMockServer storeServiceMock;
	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;
	@Autowired
	private ObjectMapper objectMapper;
	@MockitoBean
	private TenantCreatedEventKafkaConsumer tenantCreatedEventKafkaConsumer;

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
	}

	private void setHeadersOtherTenant(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(OTHER_TENANT_ID));
	}

	@Test
	@Order(1)
	void shouldReturn200WithListProductsResponse_whenFindProductsQuery() {
		int page = 0;
		int pageSize = 10;
		String nameQuery = "product";
		Map<String, Object> params = new HashMap<>();
		params.put("name", nameQuery);
		params.put("status", STATUS);
		params.put("page", page);
		params.put("size", pageSize);

		int totalPageExpect = 1;
		int totalElementsExpect = 5;
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH);
					params.forEach(uriBuilder::queryParam);
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isOk()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(Paging.class)
				.consumeWith(result -> {
					Paging<ProductResponse> body = result.getResponseBody();
					assert body != null;
					assertEquals(totalElementsExpect, body.getTotalElements());
					assertEquals(totalPageExpect, body.getTotalPages());
					assertEquals(page, body.getPage());
				});
	}

	@Test
	@Order(2)
	void shouldReturn200WithProductResponse_whenGetProductById() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", PRODUCT_ID);
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isOk()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ProductResponse.class)
				.consumeWith(result -> {
					ProductResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(PRODUCT_ID.toString(), body.id());
					assertEquals(TENANT_ID.toString(), body.tenantId());
					assertEquals(CATEGORY_ID.toString(), body.category()
							.id());
					assertEquals(HEALTHIER_CHOICE_ID.toString(), body.healthierChoice()
							.id());
					assertEquals(STORE_ID.toString(), body.storeId());
					assertEquals(HEALTHIER_CHOICE_ID.toString(), body.healthierChoice()
							.id());
					assertEquals(NAME, body.name());
					assertEquals(BARCODE, body.barcode());
					assertEquals(SKU, body.sku());
					assertEquals(BRIEF_DESCRIPTION, body.briefInformation());
					assertEquals(DESCRIPTION, body.description());
					assertEquals(INGREDIENTS, body.ingredients());
					assertEquals(STATUS.name(), body.status()
							.name());
					assertThat(body.unitPrice()).isEqualByComparingTo(UNIT_PRICE);
				});
	}

	@Test
	@Order(3)
	void shouldReturn400_whenGetProductById() {
		// Arrange
		Long notExistProductId = 100L;
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", notExistProductId);
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isBadRequest()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ErrorResponse.class)
				.consumeWith(result -> {
					ErrorResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(GlobalErrorCode.PRODUCT_NOT_FOUND.getValue(), body.getCode());
					assertEquals(GlobalErrorCode.PRODUCT_NOT_FOUND.getMessage(), body.getMessage());
				});
	}

	@Test
	@Order(4)
	void shouldReturn201WithProductResponse_whenCreateProduct() throws Exception {
		// Arrange
		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(STORE_ID)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));

		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(TENANT_ID)
				.settings(TenantSettingsResponse.builder()
						.currency(CurrencyResponse.builder()
								.currencyCode(CURRENCY_CODE)
								.build())
						.build())
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + TENANT_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));

		String imageUrl = "bucket:image/url.png";
		CreateProductImageCommand imageMock = CreateProductImageCommand.builder()
				.imagePath(imageUrl)
				.build();
		CreateProductOptionItemCommand commandOptionItemMock = CreateProductOptionItemCommand.builder()
				.name(OPTION_ITEM_NAME)
				.additionPrice(OPTION_ITEM_ADDITION_PRICE)
				.build();
		CreateProductOptionCommand commandOptionMock = CreateProductOptionCommand.builder()
				.title(OPTION_TITLE)
				.minimum(OPTION_MINIMUM)
				.maximum(OPTION_MAXIMUM)
				.items(List.of(commandOptionItemMock))
				.build();
		String newSku = "newSku1234567";
		String newBarcode = "123456732145";
		CreateProductCommand command = CreateProductCommand.builder()
				.migrationId("migrationId")
				.categoryId(CATEGORY_ID)
				.storeId(STORE_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(imageMock))
				.sku(newSku)
				.barcode(newBarcode)
				.preparationTime(PREPARATION_TIME)
				.keywords(KEYWORDS)
				.build();

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH);
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.bodyValue(command)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isCreated()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ProductResponse.class)
				.consumeWith(result -> {
					ProductResponse body = result.getResponseBody();
					assert body != null;
					productId.set(body.id());
					assertEquals(TENANT_ID.toString(), body.tenantId());
					assertEquals(command.getMigrationId(), body.migrationId());
					assertEquals(CATEGORY_ID.toString(), body.category()
							.id());
					assertEquals(HEALTHIER_CHOICE_ID.toString(), body.healthierChoice()
							.id());
					assertEquals(STORE_ID.toString(), body.storeId());
					assertEquals(NAME, body.name());
					assertEquals(newBarcode, body.barcode());
					assertEquals(newSku, body.sku());
					assertEquals(BRIEF_DESCRIPTION, body.briefInformation());
					assertEquals(DESCRIPTION, body.description());
					assertEquals(INGREDIENTS, body.ingredients());
					assertEquals(STATUS.name(), body.status()
							.name());
					assertThat(body.unitPrice()).isEqualByComparingTo(UNIT_PRICE);

					assertEquals(1, body.images()
							.size());
					assertEquals(imageUrl, body.images()
							.getFirst()
							.image()
							.path());
					assertEquals(KEYWORDS, body.keywords());
					assertEquals(0, body.options()
							.size());
				});
	}

	@Test
	@Order(5)
	void shouldReturn200WithProductResponse_whenUpdateProduct() throws Exception {
		// Arrange
		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(STORE_ID)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));

		String newSku = "newSku23423";
		String newBarcode = "123456732145";
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.sku(newSku)
				.images(List.of(UpdateProductImageCommand.builder()
						.imagePath(IMAGE_PATH)
						.build()))
				.barcode(newBarcode)
				.preparationTime(PREPARATION_TIME)
				.keywords(new ArrayList<>(KEYWORDS) {
					{
						add("keyword3");
					}
				})
				.build();
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", productId.get());
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.put()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.bodyValue(command)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isOk()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ProductResponse.class)
				.consumeWith(result -> {
					ProductResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(TENANT_ID.toString(), body.tenantId());
					assertEquals(CATEGORY_ID.toString(), body.category()
							.id());
					assertEquals(HEALTHIER_CHOICE_ID.toString(), body.healthierChoice()
							.id());
					assertEquals(STORE_ID.toString(), body.storeId());
					assertEquals(NAME, body.name());
					assertEquals(newBarcode, body.barcode());
					assertEquals(newSku, body.sku());
					assertEquals(BRIEF_DESCRIPTION, body.briefInformation());
					assertEquals(DESCRIPTION, body.description());
					assertEquals(INGREDIENTS, body.ingredients());
					assertEquals(STATUS.name(), body.status()
							.name());
					assertEquals(command.getKeywords(), body.keywords());
					assertThat(body.unitPrice()).isEqualByComparingTo(UNIT_PRICE);
				});
	}

	@Test
	@Order(6)
	void shouldReturn400WithErrorResponseProductNotFound_whenUpdateProduct() throws Exception {
		// Arrange
		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(STORE_ID)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));

		Long notExistProductId = 100L;
		String exceptionMessage = String.format("Product %s not found", notExistProductId);

		String newSku = "newSku32345";
		String newBarcode = "123456732145";
		UpdateProductCommand command = UpdateProductCommand.builder()
				.categoryId(CATEGORY_ID)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.name(NAME)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.unitPrice(UNIT_PRICE)
				.images(List.of(UpdateProductImageCommand.builder()
						.imagePath("bucket:image/url.png")
						.build()))
				.sku(newSku)
				.barcode(newBarcode)
				.preparationTime(PREPARATION_TIME)
				.build();
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", notExistProductId);
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.put()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.bodyValue(command)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isBadRequest()

				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)

				.expectBody(ErrorResponse.class)
				.consumeWith(result -> {
					ErrorResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(GlobalErrorCode.PRODUCT_NOT_FOUND.getValue(), body.getCode());
					assertEquals(GlobalErrorCode.PRODUCT_NOT_FOUND.getMessage(), body.getMessage());

					assertEquals(exceptionMessage, body.getDetails()
							.getFirst());
				});
	}

	@Test
	@Order(7)
	void shouldReturn204_whenActivateProduct() {
		// Arrange

		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", productId.get());

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.patch()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID)
							.pathSegment("activate");
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isNoContent();
	}

	@Test
	@Order(7)
	void shouldReturn204_whenDeactivateProduct() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", productId.get());

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.patch()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID)
							.pathSegment("deactivate");
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isNoContent();
	}

	@Test
	@Order(8)
	void shouldReturn204_whenDeleteProduct() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("productId", productId.get());

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.delete()
				.uri(uriBuilder -> {
					uriBuilder.path(PRODUCT_PATH_ID);
					return uriBuilder.build(uriVariables);
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec

				.expectStatus()
				.isNoContent();

	}

	@Test
	@Order(9)
	void shouldReturn200WithListName_whenGetAllProductName() throws JsonProcessingException {
		// Arrange

		// Mock other tenant
		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(OTHER_TENANT_ID)
				.settings(TenantSettingsResponse.builder()
						.currency(CurrencyResponse.builder()
								.currencyCode(CURRENCY_CODE)
								.build())
						.build())
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + OTHER_TENANT_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));

		// Mock store1
		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(STORE_ID)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));

		// Mock store2
		StoreResponse storeResponse2 = StoreResponse.builder()
				.storeId(STORE_ID_2)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID_2))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse2))));

		// Create product for store1
		CreateProductCommand command = CreateProductCommand.builder()
				.categoryId(OTHER_CATEGORY_ID)
				.storeId(STORE_ID)
				.name(NAME)
				.sku("skuskusku")
				.unitPrice(UNIT_PRICE)
				.preparationTime(PREPARATION_TIME)
				.build();

		webClient.post()
				.uri(PRODUCT_PATH)
				.bodyValue(command)
				.headers(this::setHeadersOtherTenant)
				.exchange()
				.expectStatus()
				.isCreated();

		// Create product for store2
		CreateProductCommand command2 = CreateProductCommand.builder()
				.categoryId(OTHER_CATEGORY_ID)
				.storeId(STORE_ID_2)
				.name(NAME)
				.sku("skuskusku2")
				.unitPrice(UNIT_PRICE)
				.preparationTime(PREPARATION_TIME)
				.build();

		webClient.post()
				.uri(PRODUCT_PATH)
				.bodyValue(command2)
				.headers(this::setHeadersOtherTenant)
				.exchange()
				.expectStatus()
				.isCreated();

		// Act
		FluxExchangeResult<List<String>> result = webClient.get()
				.uri(PRODUCT_PATH + "/name")
				.headers(this::setHeadersOtherTenant)
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(new ParameterizedTypeReference<>() {
				});
		List<String> list = result.getResponseBody()
				.blockFirst();

		// Assert
		assertNotNull(list);
		assertEquals(1, list.size());
		assertEquals(NAME, list.getFirst());
	}
}
