/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.features.tenant;

import com.styl.pacific.catalog.service.domain.nutrition.NutritionTenantService;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.kafka.tenant.avro.model.TenantCreatedEventAvroModel;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.catalog-service.consumers.tenant-created-event.enabled", havingValue = "true")
public class TenantCreatedEventKafkaConsumer implements KafkaConsumer<UUID, TenantCreatedEventAvroModel> {
	private static final Logger logger = LoggerFactory.getLogger(TenantCreatedEventKafkaConsumer.class);
	private final NutritionTenantService nutritionDomainService;

	@PostConstruct
	public void init() {
		logger.info("Constructed TenantCreatedEventKafkaConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.catalog-service.consumers.retry-interval-ms}}"), attempts = "${pacific.kafka.catalog-service.consumers.retry-attempts}", autoCreateTopics = "false")
	@KafkaListener(id = "${pacific.kafka.catalog-service.consumers.tenant-created-event.group-id}", topics = "${pacific.kafka.catalog-service.consumers.tenant-created-event.topic-name}")
	public void receive(TenantCreatedEventAvroModel event, UUID key, Integer partion, Long offset) {
		logger.info("Received messageId: {}, tenantId: {}, name: {}, realmId {}, partion {}, offset {}", event.getId(),
				event.getTenantId(), event.getName(), event.getRealmId(), partion, offset);

		// Initialize default nutrition list
		nutritionDomainService.initDefaultNutrition(new TenantId(event.getTenantId()));
	}
}
