<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.gateway</groupId>
        <artifactId>gateway</artifactId>
        <version>1.2.5</version>
    </parent>

    <artifactId>gateway-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-tracing</artifactId>
        </dependency>
        <!-- Catalog Service -->
        <dependency>
            <groupId>com.styl.pacific.catalog.service</groupId>
            <artifactId>catalog-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.common</groupId>
            <artifactId>common-constant</artifactId>
        </dependency>
        <!-- Notification Service -->
        <dependency>
            <groupId>com.styl.pacific.notification.service</groupId>
            <artifactId>notification-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Order Service -->
        <dependency>
            <groupId>com.styl.pacific.order.service</groupId>
            <artifactId>order-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Payment Service -->
        <dependency>
            <groupId>com.styl.pacific.payment.service</groupId>
            <artifactId>payment-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Store Service -->
        <dependency>
            <groupId>com.styl.pacific.store.service</groupId>
            <artifactId>store-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Tenant Service -->
        <dependency>
            <groupId>com.styl.pacific.tenant.service</groupId>
            <artifactId>tenant-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- User Service -->
        <dependency>
            <groupId>com.styl.pacific.user.service</groupId>
            <artifactId>user-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Utility Service -->
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Wallet Service -->
        <dependency>
            <groupId>com.styl.pacific.wallet.service</groupId>
            <artifactId>wallet-shared</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-feign</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.styl.pacific</groupId>
                    <artifactId>common-rest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-gateway-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-circuitbreaker-reactor-resilience4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>
    </dependencies>

</project>
