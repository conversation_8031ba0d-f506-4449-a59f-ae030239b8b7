/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.core.features.scanners;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.gateway.core.features.apiregistry.entities.ScannedApiInfo;
import com.styl.pacific.gateway.core.utils.HttpRequestMatcherUtils;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.http.HttpMethod;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;

public interface PacificApiInfoScanner {
	Set<ScannedApiInfo> scan(List<String> basePackages);

	GatewayProperties getGatewayProperties();

	default List<ScannedApiInfo> buildApiInfo(Method method) {
		final List<ScannedApiInfo> apiInfoList = Arrays.stream(method.getDeclaredAnnotations())
				.map(annotation -> {
					if (annotation instanceof GetMapping get) {
						final var paths = handleRequestMappingPaths(get.path(), get.name(), get.value());

						return paths.stream()
								.map(path -> ScannedApiInfo.builder()
										.path(path)
										.serviceId(getServiceId(path))
										.method(HttpMethod.GET)
										.build())
								.toList();
					}
					if (annotation instanceof PutMapping put) {
						final var paths = handleRequestMappingPaths(put.path(), put.name(), put.value());

						return paths.stream()
								.map(path -> ScannedApiInfo.builder()
										.path(path)
										.serviceId(getServiceId(path))
										.method(HttpMethod.PUT)
										.build())
								.toList();
					}
					if (annotation instanceof PostMapping post) {
						final var paths = handleRequestMappingPaths(post.path(), post.name(), post.value());
						return paths.stream()
								.map(path -> ScannedApiInfo.builder()
										.path(path)
										.serviceId(getServiceId(path))
										.method(HttpMethod.POST)
										.build())
								.toList();
					}
					if (annotation instanceof DeleteMapping delete) {
						final var paths = handleRequestMappingPaths(delete.path(), delete.name(), delete.value());

						return paths.stream()
								.map(path -> ScannedApiInfo.builder()
										.path(path)
										.serviceId(getServiceId(path))
										.method(HttpMethod.DELETE)
										.build())
								.toList();
					}
					if (annotation instanceof PatchMapping patch) {
						final var paths = handleRequestMappingPaths(patch.path(), patch.name(), patch.value());

						return paths.stream()
								.map(path -> ScannedApiInfo.builder()
										.path(path)
										.serviceId(getServiceId(path))
										.method(HttpMethod.PATCH)
										.build())
								.toList();
					}
					if (annotation instanceof RequestMapping requestMapping) {
						final var paths = handleRequestMappingPaths(requestMapping.path(), requestMapping.name(),
								requestMapping.value());

						return paths.stream()
								.map(path -> Arrays.stream(requestMapping.method())
										.map(httpMethod -> ScannedApiInfo.builder()
												.path(path)
												.serviceId(getServiceId(path))
												.method(httpMethod.asHttpMethod())
												.build())
										.toList())
								.flatMap(List::stream)
								.toList();
					}
					return List.<ScannedApiInfo>of();
				})
				.flatMap(List::stream)
				.toList();

		final var pacificApiAuthorizedAnnotation = Arrays.stream(method.getDeclaredAnnotations())
				.filter(anno -> anno instanceof PacificApiAuthorized)
				.map(anno -> (PacificApiAuthorized) anno)
				.findFirst()
				.orElse(null);

		if (pacificApiAuthorizedAnnotation == null) {
			return List.of();
		}
		return apiInfoList.stream()
				.map(apiInfo -> apiInfo.withSecurityLevel(pacificApiAuthorizedAnnotation.security())
						.withAllowedCustomerAccess(pacificApiAuthorizedAnnotation.isAllowedCustomerAccess())
						.withPermissionKeys(Arrays.stream(pacificApiAuthorizedAnnotation.permissions())
								.collect(Collectors.toSet())))
				.toList();
	}

	default List<String> handleRequestMappingPaths(String[] path, String name, String[] value) {
		final var streamPaths = Stream.concat(Arrays.stream(path), Arrays.stream(value));
		return Stream.concat(streamPaths, Optional.of(name)
				.stream())
				.filter(StringUtils::hasText)
				.toList();
	}

	default String getServiceId(String path) {
		return getGatewayProperties().getRoutes()
				.stream()
				.filter(route -> route.getPredicates()
						.stream()
						.anyMatch(routePredicate -> routePredicate.getArgs()
								.values()
								.stream()
								.anyMatch(routePath -> HttpRequestMatcherUtils.isMatchPathRequest(routePath, path))))
				.map(RouteDefinition::getId)
				.findFirst()
				.orElse("NoServiceId");

	}
}
