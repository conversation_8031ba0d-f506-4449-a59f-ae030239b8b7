/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.core.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.PathContainer;
import org.springframework.web.util.pattern.PathPatternParser;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class HttpRequestMatcherUtils {
	public static boolean isMatchPathRequest(String pattern, String path) {
		if (StringUtils.isBlank(pattern) || StringUtils.isBlank(path)) {
			throw new IllegalArgumentException(String.format("pattern: [%s]  or path: [%s] is empty", pattern, path));
		}
		final var pathPattern = new PathPatternParser().parse(pattern);

		if (log.isDebugEnabled()) {
			log.debug("Matching path: pattern: {} - path: {}", pattern, path);
		}
		return pathPattern.matches(PathContainer.parsePath(path));
	}
}
