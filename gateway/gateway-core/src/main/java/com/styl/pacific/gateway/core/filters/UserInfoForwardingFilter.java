/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.core.filters;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.tokenclaims.UserTokenClaimLite;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class UserInfoForwardingFilter implements WebFilter {

	private final ObjectMapper objectMapper;

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
		return ReactiveSecurityContextHolder.getContext()
				.map(SecurityContext::getAuthentication)
				.filter(Authentication::isAuthenticated)
				.map(authentication -> {
					if (authentication.getPrincipal() instanceof Jwt jwt) {
						return exchange.mutate()
								.request(exchange.getRequest()
										.mutate()
										.header(PacificRestConstants.PlatformHeader.HEADER_X_USER_ID, objectMapper
												.convertValue(jwt.getClaims(), UserTokenClaimLite.class)
												.getUserId())
										.build())
								.build();
					}
					return exchange;
				})
				.defaultIfEmpty(exchange)
				.flatMap(chain::filter);
	}
}
