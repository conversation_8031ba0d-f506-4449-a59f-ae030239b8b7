/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.order;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderPaymentStatus;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderResponse;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.CategoryMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.CreateOrderOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.HealthierChoiceMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineDetailRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemOptionItemRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemOptionRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflinePaymentRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.ProductMetadataOfflineRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class OrderOfflineControllerIntegrationTest extends BaseWebClientWithDbTest {

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	private static final AtomicReference<String> orderId = new AtomicReference<>();

	@BeforeEach
	public void setup() throws JsonProcessingException {
		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(2L)
				.settings(TenantSettingsResponse.builder()
						.currency(CurrencyResponse.builder()
								.displayName("Vietnamese Dong")
								.currencyCode("VND")
								.symbol("₫")
								.fractionDigits(0)
								.numericCode(704)
								.build())
						.build())
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + 2))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));
	}

	@Test
	@Order(0)
	public void shouldReturnOrderOffline_whenCreate() {
		// Arrange
		CreateOrderOfflineRequest request = CreateOrderOfflineRequest.builder()
				.order(OrderOfflineDetailRequest.builder()
						.idempotencyKey("123456789")
						.systemSource("System Source")
						.storeId("2")
						.orderNumber("123456789")
						.staffCode("Staff Code")
						.staffName("Staff Name")
						.status(OrderStatus.PAID)
						.lineItems(List.of(

								OrderOfflineLineItemRequest.builder()
										.menuId("1")
										.menuItemId("1")
										.productId("1")
										.productName("Product 1")
										.quantity(2L)
										.note("Test 123")
										.metadata(ProductMetadataOfflineRequest.builder()
												.name("Product 1")
												.barcode("123456789")
												.sku("123456789")
												.category(CategoryMetadataOfflineRequest.builder()
														.name("Category 1")
														.build())
												.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
														.name("Healthier Choice 1")
														.build())
												.unitPrice(BigInteger.valueOf(2000))
												.build())
										.options(List.of(OrderOfflineLineItemOptionRequest.builder()
												.optionId("1")
												.title("Option 1")
												.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
														.optionItemId("2")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(2000))
										.totalDiscount(BigInteger.valueOf(2000))
										.totalAmount(BigInteger.valueOf(4000))
										.build(),

								OrderOfflineLineItemRequest.builder()
										.menuId("1")
										.menuItemId("2")
										.productId("2")
										.productName("Product 2")
										.quantity(3L)
										.note("Test 456")
										.metadata(ProductMetadataOfflineRequest.builder()
												.name("Product 2")
												.barcode("987654321")
												.sku("987654321")
												.category(CategoryMetadataOfflineRequest.builder()
														.name("Category 2")
														.build())
												.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
														.name("Healthier Choice 2")
														.build())
												.unitPrice(BigInteger.valueOf(1500))
												.build())
										.options(List.of(OrderOfflineLineItemOptionRequest.builder()
												.optionId("2")
												.title("Option 2")
												.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
														.optionItemId("5")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(1500))
										.totalDiscount(BigInteger.valueOf(3000))
										.totalAmount(BigInteger.valueOf(4500))
										.build()))
						.subtotalAmount(BigInteger.valueOf(8500))
						.note("Test Note")
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(935))
						.discountAmount(BigInteger.ZERO)
						.serviceChargeAmount(BigInteger.ZERO)
						.totalAmount(BigInteger.valueOf(9435))
						.currencyCode("VND")
						.createdAt(System.currentTimeMillis())
						.build())
				.payment(OrderOfflinePaymentRequest.builder()
						.paymentMethodId("1")
						.paymentMethodName("Payment Method 1")
						.paymentStatus(OrderPaymentStatus.PAID)
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/v2/orders/offline")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.is2xxSuccessful()
				.expectBody(OrderResponse.class)
				.value(orderResponse -> {
					assertNotNull(orderResponse.id());
					assertNotNull(orderResponse.paymentRef());
					assertNotNull(orderResponse.createdAt());
					orderId.set(orderResponse.id());
					assertTrue(orderResponse.isOffline());
				});
	}

	@Test
	@Order(1)
	public void shouldReturnSameOrder_whenCreateWithSaneIdempotencyKey() {
		// Arrange
		CreateOrderOfflineRequest request = CreateOrderOfflineRequest.builder()
				.order(OrderOfflineDetailRequest.builder()
						.idempotencyKey("123456789")
						.systemSource("System Source")
						.storeId("2")
						.orderNumber("123456789")
						.staffCode("Staff Code")
						.staffName("Staff Name")
						.status(OrderStatus.PAID)
						.lineItems(List.of(

								OrderOfflineLineItemRequest.builder()
										.menuId("1")
										.menuItemId("1")
										.productId("1")
										.productName("Product 1")
										.quantity(2L)
										.note("Test 123")
										.metadata(ProductMetadataOfflineRequest.builder()
												.name("Product 1")
												.barcode("123456789")
												.sku("123456789")
												.category(CategoryMetadataOfflineRequest.builder()
														.name("Category 1")
														.build())
												.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
														.name("Healthier Choice 1")
														.build())
												.unitPrice(BigInteger.valueOf(2000))
												.build())
										.options(List.of(OrderOfflineLineItemOptionRequest.builder()
												.optionId("1")
												.title("Option 1")
												.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
														.optionItemId("2")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(2000))
										.totalDiscount(BigInteger.valueOf(2000))
										.totalAmount(BigInteger.valueOf(4000))
										.build(),

								OrderOfflineLineItemRequest.builder()
										.menuId("1")
										.menuItemId("2")
										.productId("2")
										.productName("Product 2")
										.quantity(3L)
										.note("Test 456")
										.metadata(ProductMetadataOfflineRequest.builder()
												.name("Product 2")
												.barcode("987654321")
												.sku("987654321")
												.category(CategoryMetadataOfflineRequest.builder()
														.name("Category 2")
														.build())
												.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
														.name("Healthier Choice 2")
														.build())
												.unitPrice(BigInteger.valueOf(1500))
												.build())
										.options(List.of(OrderOfflineLineItemOptionRequest.builder()
												.optionId("2")
												.title("Option 2")
												.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
														.optionItemId("5")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(1500))
										.totalDiscount(BigInteger.valueOf(3000))
										.totalAmount(BigInteger.valueOf(4500))
										.build()))
						.subtotalAmount(BigInteger.valueOf(8500))
						.note("Test Note")
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(935))
						.discountAmount(BigInteger.ZERO)
						.serviceChargeAmount(BigInteger.ZERO)
						.totalAmount(BigInteger.valueOf(9435))
						.currencyCode("VND")
						.createdAt(System.currentTimeMillis())
						.build())
				.payment(OrderOfflinePaymentRequest.builder()
						.paymentMethodId("1")
						.paymentMethodName("Payment Method 1")
						.paymentStatus(OrderPaymentStatus.PAID)
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/v2/orders/offline")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.is2xxSuccessful()
				.expectBody(OrderResponse.class)
				.value(orderResponse -> {
					assertEquals(orderId.get(), orderResponse.id());
					assertTrue(orderResponse.isOffline());
				});
	}

	@Test
	@Order(2)
	public void shouldReturnOrderOfflineWithOrderedAt_whenCreate() {
		final var oneHourAgo = Instant.now()
				.minus(1, ChronoUnit.HOURS);
		// Arrange
		final var request = CreateOrderOfflineRequest.builder()
				.order(OrderOfflineDetailRequest.builder()
						.idempotencyKey("123456789-002")
						.systemSource("System Source")
						.storeId("2")
						.orderNumber("123456789")
						.staffCode("Staff Code")
						.staffName("Staff Name")
						.status(OrderStatus.PAID)
						.lineItems(List.of(OrderOfflineLineItemRequest.builder()
								.menuId("1")
								.menuItemId("1")
								.productId("1")
								.productName("Product 1")
								.quantity(2L)
								.note("Test 123")
								.metadata(ProductMetadataOfflineRequest.builder()
										.name("Product 1")
										.barcode("123456789")
										.sku("123456789")
										.category(CategoryMetadataOfflineRequest.builder()
												.name("Category 1")
												.build())
										.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
												.name("Healthier Choice 1")
												.build())
										.unitPrice(BigInteger.valueOf(2000))
										.build())
								.options(List.of(OrderOfflineLineItemOptionRequest.builder()
										.optionId("1")
										.title("Option 1")
										.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
												.optionItemId("2")
												.name("item 2")
												.additionalPrice(BigInteger.valueOf(1000))
												.build()))
										.build()))
								.unitPrice(BigInteger.valueOf(2000))
								.totalDiscount(BigInteger.valueOf(2000))
								.totalAmount(BigInteger.valueOf(4000))
								.build(),

								OrderOfflineLineItemRequest.builder()
										.menuId("1")
										.menuItemId("2")
										.productId("2")
										.productName("Product 2")
										.quantity(3L)
										.note("Test 456")
										.metadata(ProductMetadataOfflineRequest.builder()
												.name("Product 2")
												.barcode("987654321")
												.sku("987654321")
												.category(CategoryMetadataOfflineRequest.builder()
														.name("Category 2")
														.build())
												.healthierChoice(HealthierChoiceMetadataOfflineRequest.builder()
														.name("Healthier Choice 2")
														.build())
												.unitPrice(BigInteger.valueOf(1500))
												.build())
										.options(List.of(OrderOfflineLineItemOptionRequest.builder()
												.optionId("2")
												.title("Option 2")
												.items(List.of(OrderOfflineLineItemOptionItemRequest.builder()
														.optionItemId("5")
														.name("item 2")
														.additionalPrice(BigInteger.valueOf(1000))
														.build()))
												.build()))
										.unitPrice(BigInteger.valueOf(1500))
										.totalDiscount(BigInteger.valueOf(3000))
										.totalAmount(BigInteger.valueOf(4500))
										.build()))
						.subtotalAmount(BigInteger.valueOf(8500))
						.note("Test Note")
						.taxName("TAX")
						.taxRate(new BigDecimal("0.1100"))
						.taxInclude(false)
						.taxAmount(BigInteger.valueOf(935))
						.discountAmount(BigInteger.ZERO)
						.serviceChargeAmount(BigInteger.ZERO)
						.totalAmount(BigInteger.valueOf(9435))
						.currencyCode("VND")
						.createdAt(System.currentTimeMillis())
						.orderedAt(oneHourAgo.toEpochMilli())
						.build())
				.payment(OrderOfflinePaymentRequest.builder()
						.paymentMethodId("1")
						.paymentMethodName("Payment Method 1")
						.paymentStatus(OrderPaymentStatus.PAID)
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path("/api/order/v2/orders/offline")
							.pathSegment("place");
					return uriBuilder.build();
				})
				.bodyValue(request)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.is2xxSuccessful()
				.expectBody(OrderResponse.class)
				.value(orderResponse -> {
					assertNotNull(orderResponse.id());
					assertNotNull(orderResponse.paymentRef());
					assertNotNull(orderResponse.createdAt());
					orderId.set(orderResponse.id());
					assertTrue(orderResponse.isOffline());
					assertEquals(oneHourAgo.toEpochMilli(), orderResponse.orderedAt());
				});
	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(2));
	}
}
