/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.menu;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.order.service.config.MvcTestConfiguration;
import com.styl.pacific.order.service.domain.features.menu.dto.command.CreateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.command.UpdateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.query.MenuQuery;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuType;
import com.styl.pacific.order.service.domain.features.menu.ports.input.service.MenuService;
import com.styl.pacific.order.service.presenter.exception.handler.OrderExceptionHandler;
import com.styl.pacific.order.service.presenter.features.menu.MenuController;
import com.styl.pacific.order.service.shared.http.menu.request.CreateMenuRequest;
import com.styl.pacific.order.service.shared.http.menu.request.UpdateMenuRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
@WebMvcTest(controllers = MenuController.class)
@Import({ MenuController.class, OrderExceptionHandler.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class MenuControllerTest {
	private static final String MENU_PATH = "/api/order/menus";

	private static final Long ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final com.styl.pacific.order.service.shared.http.menu.enums.MenuType TYPE_REST = com.styl.pacific.order.service.shared.http.menu.enums.MenuType.APP;
	private static final com.styl.pacific.order.service.shared.http.menu.enums.MenuStatus STATUS_REST = com.styl.pacific.order.service.shared.http.menu.enums.MenuStatus.ACTIVE;
	private static final MenuType TYPE = MenuType.APP;
	private static final MenuStatus STATUS = MenuStatus.ACTIVE;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private MenuService menuService;

	@Test
	void shouldReturn200ListMenuResponse_whenGetListQuery() throws Exception {
		// Arrange
		Menu responseStubMock = getMenuStubResponse();
		List<Menu> resMenuStubs = List.of(responseStubMock);
		when(menuService.findAll(any(), any())).thenReturn(Content.<Menu>builder()
				.content(resMenuStubs)
				.build());
		String uri = UriComponentsBuilder.fromPath(MENU_PATH)
				.pathSegment("list")
				.queryParam("filter.type", TYPE)
				.queryParam("filter.statuses", STATUS)
				.toUriString();

		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.content").isArray())
				.andExpect(jsonPath("$.content.length()").value(resMenuStubs.size()));

		ArgumentCaptor<MenuQuery> captor = ArgumentCaptor.forClass(MenuQuery.class);
		verify(menuService, times(1)).findAll(any(), captor.capture());
		MenuQuery queryCaptor = captor.getValue();
		assertEquals(MenuType.APP, queryCaptor.filter()
				.type());
	}

	@Test
	void shouldReturn200MenuResponse_whenGetByIdQuery() throws Exception {
		// Arrange
		Menu response = getMenuStubResponse();
		when(menuService.findById(any(), any())).thenReturn(response);
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(MENU_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(menuService, times(1)).findById(any(), any());
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(response.getId()
						.getValue()
						.toString()))
				.andExpect(jsonPath("$.name").value(response.getName()))
				.andExpect(jsonPath("$.description").value(response.getDescription()))
				.andExpect(jsonPath("$.storeId").value(response.getStoreId()
						.getValue()
						.toString()))
				.andExpect(jsonPath("$.type").value(response.getType()
						.toString()))
				.andExpect(jsonPath("$.status").value(response.getStatus()
						.toString()));
	}

	@Test
	void shouldReturn201MenuResponse_whenCreate() throws Exception {
		// Arrange
		CreateMenuRequest requestMock = new CreateMenuRequest(STORE_ID.toString(), NAME, STATUS_REST, TYPE_REST,
				DESCRIPTION);
		Menu response = getMenuStubResponse();
		when(menuService.create(any(), any())).thenReturn(response);
		String uri = UriComponentsBuilder.fromPath(MENU_PATH)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(post(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(requestMock)));
		// Assert
		ArgumentCaptor<CreateMenuCommand> captor = ArgumentCaptor.forClass(CreateMenuCommand.class);
		verify(menuService, times(1)).create(any(), captor.capture());
		CreateMenuCommand commandInput = captor.getValue();
		assertEquals(requestMock.name(), commandInput.name());
		assertEquals(requestMock.description(), commandInput.description());
		assertEquals(requestMock.storeId(), commandInput.storeId()
				.toString());
		assertEquals(requestMock.type()
				.name(), commandInput.type()
						.name());
		resultActions.andExpect(status().isCreated())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(response.getId()
						.getValue()))
				.andExpect(jsonPath("$.name").value(response.getName()))
				.andExpect(jsonPath("$.description").value(response.getDescription()))
				.andExpect(jsonPath("$.storeId").value(response.getStoreId()
						.getValue()))
				.andExpect(jsonPath("$.type").value(response.getType()
						.toString()))
				.andExpect(jsonPath("$.status").value(response.getStatus()
						.toString()));
	}

	@Test
	void shouldReturn200MenuResponse_whenUpdate() throws Exception {
		// Arrange
		UpdateMenuRequest requestMock = new UpdateMenuRequest(NAME, STATUS_REST, TYPE_REST, DESCRIPTION);
		Menu response = getMenuStubResponse();
		when(menuService.update(any(), any(), any())).thenReturn(response);
		Map<String, Object> uriVariable = new HashMap<>();
		uriVariable.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(MENU_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriVariable)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(put(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(requestMock)));
		// Assert
		ArgumentCaptor<UpdateMenuCommand> captor = ArgumentCaptor.forClass(UpdateMenuCommand.class);
		verify(menuService, times(1)).update(any(), any(), captor.capture());
		UpdateMenuCommand commandInput = captor.getValue();
		assertEquals(requestMock.name(), commandInput.name());
		assertEquals(requestMock.description(), commandInput.description());
		assertEquals(requestMock.type()
				.name(), commandInput.type()
						.name());
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(response.getId()
						.getValue()
						.toString()))
				.andExpect(jsonPath("$.name").value(response.getName()))
				.andExpect(jsonPath("$.description").value(response.getDescription()))
				.andExpect(jsonPath("$.storeId").value(response.getStoreId()
						.getValue()
						.toString()))
				.andExpect(jsonPath("$.type").value(response.getType()
						.toString()))
				.andExpect(jsonPath("$.status").value(response.getStatus()
						.toString()));
	}

	@Test
	void shouldReturn_whenDelete() throws Exception {
		// Arrange
		Map<String, Object> uriVariable = new HashMap<>();
		uriVariable.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(MENU_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriVariable)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(delete(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(menuService, times(1)).deleteById(any(), any());
		resultActions.andExpect(status().isNoContent());
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}

	private Menu getMenuStubResponse() {
		return Menu.builder()
				.id(new MenuId(ID))
				.name(NAME)
				.description(DESCRIPTION)
				.storeId(new StoreId(STORE_ID))
				.type(TYPE)
				.status(STATUS)
				.build();
	}
}
