/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.preorder.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemChainId;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.product.dto.ProductStubDto;
import java.time.Instant;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class PreOrderMenuItemDtoTest {
	private static final Long ID = 1L;
	private static final Long CHAIN_ID = 2L;
	private static final Long PRE_ORDER_MENU_ID = 3L;
	private static final Long MEAL_TIME_ID = 4L;
	private static final Long PRODUCT_ID = 5L;
	private static final LocalDate DATE = LocalDate.now();
	private static final Integer CAPACITY = 10;
	private static final Integer ORDERED = 5;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldCreated_whenBuilder() {
		// Act
		PreOrderMenuItemDto dto = PreOrderMenuItemDto.builder()
				.id(new PreOrderMenuItemId(ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.product(ProductStubDto.builder()
						.id(PRODUCT_ID)
						.build())
				.date(DATE)
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.chainId(new PreOrderMenuItemChainId(CHAIN_ID))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
		// Assert
		assertEquals(ID, dto.id()
				.getValue());
		assertEquals(PRE_ORDER_MENU_ID, dto.preOrderMenuId()
				.getValue());
		assertEquals(MEAL_TIME_ID, dto.mealTimeId()
				.getValue());
		assertEquals(PRODUCT_ID, dto.product()
				.id());
		assertEquals(DATE, dto.date());
		assertEquals(ORDERED, dto.ordered());
		assertEquals(CHAIN_ID, dto.chainId()
				.getValue());
		assertEquals(CAPACITY, dto.capacity());
		assertEquals(CREATED_AT, dto.createdAt());
		assertEquals(UPDATED_AT, dto.updatedAt());
	}
}
