/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.menu;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.DynamoDbContainerTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.shared.http.common.enums.DateOfWeek;
import com.styl.pacific.order.service.shared.http.menu.request.arrangement.ArrangementRequest;
import com.styl.pacific.order.service.shared.http.menu.request.item.CreateMenuItemRequest;
import com.styl.pacific.order.service.shared.http.menu.request.item.UpdateMenuItemRequest;
import com.styl.pacific.order.service.shared.http.menu.response.MenuItemResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class MenuItemControllerIntegrationTest extends BaseWebClientWithDbTest implements DynamoDbContainerTest,
		KafkaContainerTest {
	private static final String BASE_URL = "/api/order/menus/{id}/items";

	private static final Long MENU_ID = 1L;

	private static final Long PRODUCT_ID = 3L;

	private static final Long TENANT_ID = 2L;
	private static final Long STORE_ID = 1L;
	private static final Instant START_DATE = Instant.parse("2021-08-01T00:00:00Z");
	private static final Instant END_DATE = Instant.parse("2021-08-31T00:00:00Z");
	private static final LocalTime START_TIME = LocalTime.parse("00:00:00");
	private static final LocalTime END_TIME = LocalTime.parse("23:59:59");
	private static final List<DateOfWeek> AVAILABLE_ON = List.of(DateOfWeek.MONDAY);

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	@Autowired
	@Qualifier("storeService")
	private WireMockServer storeServiceMock;

	private static final AtomicReference<Long> idValue = new AtomicReference<>();

	@BeforeEach
	public void setup() throws JsonProcessingException {

		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(TENANT_ID)
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + TENANT_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));

		StoreResponse storeResponse = StoreResponse.builder()
				.storeId(STORE_ID)
				.build();
		storeServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/store/stores/" + STORE_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(storeResponse))));
	}

	@Order(0)
	@Test
	public void shouldReturnMenuItem_whenCreate() {
		// Arrange
		CreateMenuItemRequest request = CreateMenuItemRequest.builder()
				.productId(PRODUCT_ID.toString())
				.arrangement(ArrangementRequest.builder()
						.availableOn(AVAILABLE_ON)
						.startDate(START_DATE.toEpochMilli())
						.endDate(END_DATE.toEpochMilli())
						.startTime(START_TIME.toString())
						.endTime(END_TIME.toString())
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec response = webClient.post()
				.uri(BASE_URL, MENU_ID)
				.headers(this::setHeaders)
				.bodyValue(request)
				.exchange();
		// Assert
		response.expectStatus()
				.isCreated()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON_VALUE)
				.expectBody(MenuItemResponse.class)
				.value(menuItemResponse -> {
					assertNotNull(menuItemResponse);
					assertNotNull(menuItemResponse.id());
					idValue.set(Long.parseLong(menuItemResponse.id()));
					assertNotNull(menuItemResponse.product());
					assertEquals(PRODUCT_ID.toString(), menuItemResponse.product()
							.id());
					assertNotNull(menuItemResponse.arrangement());
					assertEquals(AVAILABLE_ON, menuItemResponse.arrangement()
							.availableOn());
					assertEquals(START_DATE.toEpochMilli(), menuItemResponse.arrangement()
							.startDate());
					assertEquals(END_DATE.toEpochMilli(), menuItemResponse.arrangement()
							.endDate());
					assertEquals(START_TIME.format(DateTimeFormatter.ISO_LOCAL_TIME), menuItemResponse.arrangement()
							.startTime());
					assertEquals(END_TIME.format(DateTimeFormatter.ISO_LOCAL_TIME), menuItemResponse.arrangement()
							.endTime());
					assertNotNull(menuItemResponse.createdAt());
					assertNotNull(menuItemResponse.updatedAt());
				});
	}

	@Order(1)
	@Test
	public void shouldReturnInvalid_whenCreate() {
		// Arrange
		CreateMenuItemRequest request = CreateMenuItemRequest.builder()
				.productId(PRODUCT_ID.toString())
				.arrangement(ArrangementRequest.builder()
						.availableOn(AVAILABLE_ON)
						.startDate(END_DATE.toEpochMilli())
						.endDate(START_DATE.toEpochMilli())
						.startTime(START_TIME.toString())
						.endTime(START_TIME.toString())
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec response = webClient.post()
				.uri(BASE_URL, MENU_ID)
				.headers(this::setHeaders)
				.bodyValue(request)
				.exchange();
		// Assert
		response.expectStatus()
				.isBadRequest()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON_VALUE)
				.expectBody()
				.jsonPath("$.code")
				.isEqualTo(GlobalErrorCode.VALIDATION_ERROR.getValue());
	}

	@Order(2)
	@Test
	public void shouldReturnProductAlreadyAssign_whenCreate() {
		// Arrange
		CreateMenuItemRequest request = CreateMenuItemRequest.builder()
				.productId(PRODUCT_ID.toString())
				.arrangement(ArrangementRequest.builder()
						.availableOn(AVAILABLE_ON)
						.startDate(START_DATE.toEpochMilli())
						.endDate(END_DATE.toEpochMilli())
						.startTime(START_TIME.toString())
						.endTime(END_TIME.toString())
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec response = webClient.post()
				.uri(BASE_URL, MENU_ID)
				.headers(this::setHeaders)
				.bodyValue(request)
				.exchange();
		// Assert
		response.expectStatus()
				.isBadRequest()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON_VALUE)
				.expectBody()
				.jsonPath("$.code")
				.isEqualTo(GlobalErrorCode.MENU_ITEM_PRODUCT_ALREADY_ASSIGN.getValue());

	}

	@Order(3)
	@Test
	public void shouldReturnNewMenuItem_whenUpdate() {
		// Arrange
		Instant newStartDate = Instant.parse("2021-09-01T00:00:00Z");
		Instant newEndDate = Instant.parse("2021-09-30T00:00:00Z");
		LocalTime newStartTime = LocalTime.parse("01:00:00");
		LocalTime newEndTime = LocalTime.parse("22:00:00");
		List<DateOfWeek> newAvailableOn = List.of(DateOfWeek.TUESDAY, DateOfWeek.WEDNESDAY);
		UpdateMenuItemRequest request = UpdateMenuItemRequest.builder()
				.arrangement(ArrangementRequest.builder()
						.availableOn(newAvailableOn)
						.startDate(newStartDate.toEpochMilli())
						.endDate(newEndDate.toEpochMilli())
						.startTime(newStartTime.toString())
						.endTime(newEndTime.toString())
						.build())
				.build();
		// Act
		WebTestClient.ResponseSpec response = webClient.put()
				.uri(uriBuilder -> uriBuilder.path(BASE_URL)
						.pathSegment(idValue.get()
								.toString())
						.build(MENU_ID))
				.headers(this::setHeaders)
				.bodyValue(request)
				.exchange();
		// Assert
		response.expectStatus()
				.isOk()
				.expectBody(MenuItemResponse.class)
				.value(menuItemResponse -> {
					assertNotNull(menuItemResponse);
					assertEquals(idValue.get()
							.toString(), menuItemResponse.id());
					assertNotNull(menuItemResponse.product());
					assertNotNull(menuItemResponse.arrangement());
					assertEquals(newAvailableOn, menuItemResponse.arrangement()
							.availableOn());
					assertEquals(newStartDate.toEpochMilli(), menuItemResponse.arrangement()
							.startDate());
					assertEquals(newEndDate.toEpochMilli(), menuItemResponse.arrangement()
							.endDate());
					assertEquals(newStartTime.format(DateTimeFormatter.ISO_LOCAL_TIME), menuItemResponse.arrangement()
							.startTime());
					assertEquals(newEndTime.format(DateTimeFormatter.ISO_LOCAL_TIME), menuItemResponse.arrangement()
							.endTime());
					assertNotNull(menuItemResponse.createdAt());
					assertNotNull(menuItemResponse.updatedAt());
				});
	}

	@Order(4)
	@Test
	public void shouldRemove_whenDelete() {
		// Act
		WebTestClient.ResponseSpec response = webClient.delete()
				.uri(uriBuilder -> uriBuilder.path(BASE_URL + "/{itemId}")
						.build(MENU_ID, idValue.get()))
				.headers(this::setHeaders)
				.exchange();
		// Assert
		response.expectStatus()
				.isNoContent();
	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
	}

}
