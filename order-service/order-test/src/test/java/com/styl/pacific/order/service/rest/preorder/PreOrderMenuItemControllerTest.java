/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.preorder;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.order.service.config.MvcTestConfiguration;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.input.service.PreOrderMenuItemService;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.product.dto.ProductStubDto;
import com.styl.pacific.order.service.presenter.exception.handler.OrderExceptionHandler;
import com.styl.pacific.order.service.presenter.features.preorder.menu.PreOrderMenuItemController;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */
@WebMvcTest(controllers = PreOrderMenuItemController.class)
@Import({ PreOrderMenuItemController.class, OrderExceptionHandler.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class PreOrderMenuItemControllerTest {
	private static final String PRE_ORDER_MENU_ITEM_PATH = "/api/order/pre-orders/menus/{menuId}/items";

	private static final Long ID = 1L;
	private static final Long PRE_ORDER_MENU_ID = 1L;
	private static final Long MEAL_TIME_ID = 2L;
	private static final Long PRODUCT_ID = 3L;
	private static final LocalDate START_DATE = LocalDate.now();
	private static final LocalDate END_DATE = LocalDate.now();
	private static final Integer ORDERED = 0;
	private static final Integer CAPACITY = 10;
	private static final boolean REPEATED = true;
	private static final List<DateOfWeek> AVAILABLE_ON = List.of(DateOfWeek.FRIDAY, DateOfWeek.MONDAY);
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	private static final Long TENANT_ID = 1L;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockBean
	private PreOrderMenuItemService preOrderMenuItemService;

	@Test
	void shouldReturn200_whenGetById() throws Exception {
		// Arrange
		PreOrderMenuItemDto dto = getPreOrderMenuItemDto();
		when(preOrderMenuItemService.findById(any(), any(), any())).thenReturn(dto);
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("menuId", PRE_ORDER_MENU_ID);
		uriVariables.put("id", ID);
		String uri = UriComponentsBuilder.fromPath(PRE_ORDER_MENU_ITEM_PATH)
				.pathSegment("{id}")
				.buildAndExpand(uriVariables)
				.toUriString();
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(preOrderMenuItemService, times(1)).findById(any(), any(), any());
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.id").value(dto.id()
						.getValue()))
				.andExpect(jsonPath("$.preOrderMenuId").value(dto.preOrderMenuId()
						.getValue()))
				.andExpect(jsonPath("$.mealTimeId").value(dto.mealTimeId()
						.getValue()))
				.andExpect(jsonPath("$.product.id").value(dto.product()
						.id()))
				.andExpect(jsonPath("$.capacity").value(dto.capacity()))
				.andExpect(jsonPath("$.createdAt").value(dto.createdAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.updatedAt").value(dto.updatedAt()
						.toEpochMilli()));
	}

	@Test
	void shouldReturn200WithList_whenGetAll() throws Exception {
		// Arrange
		PreOrderMenuItemDto dto = getPreOrderMenuItemDto();
		List<PreOrderMenuItemDto> dtoList = List.of(dto);
		int pageMock = 1;
		List<String> sortMock = List.of("id:DESC");

		Paging<PreOrderMenuItemDto> pagingMock = Paging.<PreOrderMenuItemDto>builder()
				.content(dtoList)
				.totalElements(dtoList.size())
				.totalPages(1)
				.page(pageMock)
				.sort(sortMock)
				.build();
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("menuId", PRE_ORDER_MENU_ID);
		String uri = UriComponentsBuilder.fromPath(PRE_ORDER_MENU_ITEM_PATH)
				.buildAndExpand(uriVariables)
				.toUriString();
		when(preOrderMenuItemService.findAllPaging(any(), any(), any())).thenReturn(pagingMock);
		// Act
		ResultActions resultActions = mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()));
		// Assert
		verify(preOrderMenuItemService, times(1)).findAllPaging(any(), any(), any());
		resultActions.andExpect(status().isOk())
				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
				.andExpect(jsonPath("$.totalElements").value(pagingMock.getTotalElements()))
				.andExpect(jsonPath("$.totalPages").value(pagingMock.getTotalPages()))
				.andExpect(jsonPath("$.page").value(pagingMock.getPage()))
				.andExpect(jsonPath("$.content[0].id").value(dto.id()
						.getValue()))
				.andExpect(jsonPath("$.content[0].preOrderMenuId").value(dto.preOrderMenuId()
						.getValue()))
				.andExpect(jsonPath("$.content[0].mealTimeId").value(dto.mealTimeId()
						.getValue()))
				.andExpect(jsonPath("$.content[0].product.id").value(dto.product()
						.id()))
				.andExpect(jsonPath("$.content[0].capacity").value(dto.capacity()))
				.andExpect(jsonPath("$.content[0].createdAt").value(dto.createdAt()
						.toEpochMilli()))
				.andExpect(jsonPath("$.content[0].updatedAt").value(dto.updatedAt()
						.toEpochMilli()));

	}

	//	@Test
	//	void shouldReturn201_whenCreate() throws Exception {
	//		// Arrange
	//		CreatePreOrderMenuItemRequest requestMock = CreatePreOrderMenuItemRequest.builder()
	//				.mealTimeId(MEAL_TIME_ID.toString())
	//				.productId(PRODUCT_ID.toString())
	//				.startDate(DateTimeFormatter.ISO_LOCAL_DATE.format(START_DATE))
	//				.endDate(DateTimeFormatter.ISO_LOCAL_DATE.format(END_DATE))
	//				.availableOn(AVAILABLE_ON.stream()
	//						.map(DateOfWeek::name)
	//						.map(com.styl.pacific.order.service.shared.http.common.enums.DateOfWeek::valueOf)
	//						.toList())
	//				.capacity(CAPACITY)
	//				.repeated(REPEATED)
	//				.build();
	//		PreOrderMenuItemDto dtoMock = getPreOrderMenuItemDto();
	//		Map<String, Object> uriVariables = new HashMap<>();
	//		uriVariables.put("menuId", PRE_ORDER_MENU_ID);
	//		String uri = UriComponentsBuilder.fromPath(PRE_ORDER_MENU_ITEM_PATH)
	//				.buildAndExpand(uriVariables)
	//				.toUriString();
	//		when(preOrderMenuItemService.create(anyLong(), anyLong(), any())).thenReturn(dtoMock);
	//		// Act
	//		ResultActions resultActions = mockMvc.perform(post(uri).contentType(MediaType.APPLICATION_JSON)
	//				.headers(getHttpHeaders())
	//				.content(objectMapper.writeValueAsString(requestMock)));
	//		// Assert
	//		ArgumentCaptor<CreatePreOrderMenuItemCommand> captor = ArgumentCaptor.forClass(
	//				CreatePreOrderMenuItemCommand.class);
	//		verify(preOrderMenuItemService, times(1)).create(anyLong(), anyLong(), captor.capture());
	//		CreatePreOrderMenuItemCommand commandInput = captor.getValue();
	//		assertEquals(requestMock.mealTimeId(), commandInput.mealTimeId()
	//				.toString());
	//		assertEquals(requestMock.productId(), commandInput.productId()
	//				.toString());
	//		assertEquals(requestMock.startDate(), commandInput.startDate());
	//		assertEquals(requestMock.endDate(), commandInput.endDate());
	//		assertEquals(requestMock.capacity(), commandInput.capacity());
	//		assertEquals(requestMock.repeated(), commandInput.repeated());
	//		assertEquals(requestMock.availableOn()
	//				.stream()
	//				.map(Enum::name)
	//				.map(DateOfWeek::valueOf)
	//				.toList(), commandInput.availableOn());
	//
	//		resultActions.andExpect(statuses().isCreated())
	//				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
	//				.andExpect(jsonPath("$.id").value(dtoMock.id()))
	//				.andExpect(jsonPath("$.preOrderMenuId").value(dtoMock.preOrderMenuId()))
	//				.andExpect(jsonPath("$.mealTimeId").value(dtoMock.mealTimeId()))
	//				.andExpect(jsonPath("$.product.id").value(dtoMock.product()
	//						.id()))
	//				.andExpect(jsonPath("$.capacity").value(dtoMock.capacity()))
	//				.andExpect(jsonPath("$.createdAt").value(dtoMock.createdAt()
	//						.toEpochMilli()))
	//				.andExpect(jsonPath("$.updatedAt").value(dtoMock.updatedAt()
	//						.toEpochMilli()));
	//
	//	}

	//	@Test
	//	void shouldReturn200_whenUpdate() throws Exception {
	//		// Arrange
	//		UpdatePreOrderMenuItemRequest requestMock = UpdatePreOrderMenuItemRequest.builder()
	//				.mealTimeId(MEAL_TIME_ID.toString())
	//				.startDate(DateTimeFormatter.ISO_LOCAL_DATE.format(START_DATE))
	//				.endDate(DateTimeFormatter.ISO_LOCAL_DATE.format(END_DATE))
	//				.availableOn(AVAILABLE_ON.stream()
	//						.map(DateOfWeek::name)
	//						.map(com.styl.pacific.order.service.shared.http.common.enums.DateOfWeek::valueOf)
	//						.toList())
	//				.capacity(CAPACITY)
	//				.repeated(REPEATED)
	//				.build();
	//		PreOrderMenuItemDto dtoMock = getPreOrderMenuItemDto();
	//		Map<String, Object> uriVariables = new HashMap<>();
	//		uriVariables.put("menuId", PRE_ORDER_MENU_ID);
	//		uriVariables.put("id", ID);
	//		String uri = UriComponentsBuilder.fromPath(PRE_ORDER_MENU_ITEM_PATH)
	//				.pathSegment("{id}")
	//				.buildAndExpand(uriVariables)
	//				.toUriString();
	//		when(preOrderMenuItemService.update(anyLong(), anyLong(), anyLong(), any())).thenReturn(dtoMock);
	//		// Act
	//		ResultActions resultActions = mockMvc.perform(put(uri).contentType(MediaType.APPLICATION_JSON)
	//				.headers(getHttpHeaders())
	//				.content(objectMapper.writeValueAsString(requestMock)));
	//		// Assert
	//		ArgumentCaptor<UpdatePreOrderMenuItemCommand> captor = ArgumentCaptor.forClass(
	//				UpdatePreOrderMenuItemCommand.class);
	//		verify(preOrderMenuItemService, times(1)).update(anyLong(), anyLong(), anyLong(), captor.capture());
	//		UpdatePreOrderMenuItemCommand commandInput = captor.getValue();
	//		assertEquals(requestMock.mealTimeId(), commandInput.mealTimeId()
	//				.toString());
	//		assertEquals(requestMock.startDate(), commandInput.startDate());
	//		assertEquals(requestMock.endDate(), commandInput.endDate());
	//		assertEquals(requestMock.capacity(), commandInput.capacity());
	//		assertEquals(requestMock.repeated(), commandInput.repeated());
	//		assertEquals(requestMock.availableOn()
	//				.stream()
	//				.map(Enum::name)
	//				.map(DateOfWeek::valueOf)
	//				.toList(), commandInput.availableOn());
	//
	//		resultActions.andExpect(statuses().isOk())
	//				.andExpect(content().contentType(MediaType.APPLICATION_JSON))
	//				.andExpect(jsonPath("$.id").value(dtoMock.id()))
	//				.andExpect(jsonPath("$.preOrderMenuId").value(dtoMock.preOrderMenuId()))
	//				.andExpect(jsonPath("$.mealTimeId").value(dtoMock.mealTimeId()))
	//				.andExpect(jsonPath("$.product.id").value(dtoMock.product()
	//						.id()))
	//				.andExpect(jsonPath("$.capacity").value(dtoMock.capacity()))
	//				.andExpect(jsonPath("$.createdAt").value(dtoMock.createdAt()
	//						.toEpochMilli()))
	//				.andExpect(jsonPath("$.updatedAt").value(dtoMock.updatedAt()
	//						.toEpochMilli()));
	//
	//	}

	//	@Test
	//	void shouldReturn204_whenDelete() throws Exception {
	//		// Arrange
	//		Map<String, Object> uriVariables = new HashMap<>();
	//		uriVariables.put("menuId", PRE_ORDER_MENU_ID);
	//		uriVariables.put("id", ID);
	//		String uri = UriComponentsBuilder.fromPath(PRE_ORDER_MENU_ITEM_PATH)
	//				.pathSegment("{id}")
	//				.buildAndExpand(uriVariables)
	//				.toUriString();
	//		// Act
	//		ResultActions resultActions = mockMvc.perform(delete(uri).contentType(MediaType.APPLICATION_JSON)
	//				.headers(getHttpHeaders()));
	//		// Assert
	//		verify(preOrderMenuItemService, times(1)).delete(anyLong(), anyLong(), eq(ID));
	//		resultActions.andExpect(statuses().isNoContent());
	//	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}

	private PreOrderMenuItemDto getPreOrderMenuItemDto() {
		return PreOrderMenuItemDto.builder()
				.id(new PreOrderMenuItemId(ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.product(ProductStubDto.builder()
						.id(PRODUCT_ID)
						.build())
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

}
