/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.menuboard.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.menuboard.entity.MenuBoardSessionEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class MenuBoardSessionSpecifications {

	public static Specification<MenuBoardSessionEntity> withTenantIdAndId(Long tenantId, UUID id) {
		List<Specification<MenuBoardSessionEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuBoardSessionEntity> withTenantIdAndMenuBoardId(Long tenantId, Long menuBoardId) {
		List<Specification<MenuBoardSessionEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(menuBoardId)) {
			specifications.add(withMenuBoardId(menuBoardId));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuBoardSessionEntity> withMultipleCriteria(Long tenantId, List<Long> menuBoardIds,
			String name) {
		List<Specification<MenuBoardSessionEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(menuBoardIds) && !menuBoardIds.isEmpty()) {
			specifications.add(withInMenuBoardIds(menuBoardIds));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.add(withLikeName(name));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MenuBoardSessionEntity> withId(UUID id) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuBoardSessionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuBoardSessionEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<MenuBoardSessionEntity> withMenuBoardId(Long menuBoardId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuBoardSessionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuBoardSessionEntity.FIELD_MENU_BOARD_ID), menuBoardId);
			}
		};
	}

	public static Specification<MenuBoardSessionEntity> withInMenuBoardIds(List<Long> menuBoardIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuBoardSessionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MenuBoardSessionEntity.FIELD_MENU_BOARD_ID)
						.in(menuBoardIds);
			}
		};
	}

	public static Specification<MenuBoardSessionEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuBoardSessionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MenuBoardSessionEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<MenuBoardSessionEntity> withLikeName(String name) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MenuBoardSessionEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(MenuBoardSessionEntity.FIELD_NAME), name);
			}
		};
	}
}
