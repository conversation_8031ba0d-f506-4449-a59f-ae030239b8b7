/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.data.access.jpa.converter.JpaLocalDateConverter;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuItemStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "tb_pre_order_menu_item")
@AllArgsConstructor
@NoArgsConstructor
public class PreOrderMenuItemEntity extends AuditableEntity {
	public static final String FIELD_ID = "id";
	public static final String FIELD_CHAIN_ID = "chainId";
	public static final String FIELD_ORDERED = "ordered";
	public static final String FIELD_MENU_ID = "preOrderMenuId";
	public static final String FIELD_PRODUCT = "product";
	public static final String FIELD_PRODUCT_ID = "productId";
	public static final String FIELD_CAPACITY = "capacity";
	public static final String FIELD_STATUS = "status";
	public static final String FIELD_DATE = "date";
	public static final String FIELD_DATE_TIME = "dateTime";
	public static final String FIELD_MENU = "menu";
	public static final String FIELD_MEAL_TIME = "mealTime";
	public static final String FIELD_MEAL_TIME_ID = "mealTimeId";

	public static final Set<String> SORTABLE_FIELDS = Set.of(String.join("_", List.of(FIELD_PRODUCT,
			ProductEntity.FIELD_ID)), String.join("_", List.of(FIELD_PRODUCT, ProductEntity.FIELD_NAME)), String.join(
					"_", List.of(FIELD_PRODUCT, ProductEntity.FIELD_CATEGORY_ID)), FIELD_CAPACITY);

	@Id
	private Long id;

	@Column(name = "pre_order_menu_id", nullable = false)
	private Long preOrderMenuId;

	@Column(nullable = false)
	private Long chainId;

	@Version
	private Integer version;

	@Column(name = "meal_time_id", nullable = false)
	private Long mealTimeId;

	@ManyToOne
	@JoinColumn(name = "meal_time_id", insertable = false, updatable = false)
	private MealTimeEntity mealTime;

	@Column(name = "product_id", nullable = false)
	private Long productId;

	@ManyToOne
	@JoinColumn(name = "product_id", insertable = false, updatable = false)
	private ProductEntity product;

	@Enumerated(EnumType.STRING)
	@Column(columnDefinition = "varchar(255) default 'ACTIVE'")
	private PreOrderMenuItemStatus status;

	private Integer capacity;

	@Column(nullable = false)
	private Integer ordered;

	@Convert(converter = JpaLocalDateConverter.class)
	@Column(nullable = false)
	private LocalDate date;

	@Column(nullable = false)
	private Instant dateTime;

	@ManyToOne
	@JoinColumn(name = "pre_order_menu_id", insertable = false, updatable = false)
	private PreOrderMenuEntity menu;

	private PreOrderMenuItemEntity(Builder builder) {
		setDeletedAt(builder.deletedAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
		setId(builder.id);
		setPreOrderMenuId(builder.preOrderMenuId);
		setChainId(builder.chainId);
		setVersion(builder.version);
		setMealTimeId(builder.mealTimeId);
		setMealTime(builder.mealTime);
		setProductId(builder.productId);
		setProduct(builder.product);
		setStatus(builder.status);
		setCapacity(builder.capacity);
		setOrdered(builder.ordered);
		setDate(builder.date);
		setDateTime(builder.dateTime);
		setMenu(builder.menu);
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private Instant deletedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Long id;
		private Long preOrderMenuId;
		private Long chainId;
		private Integer version;
		private Long mealTimeId;
		private MealTimeEntity mealTime;
		private Long productId;
		private ProductEntity product;
		private PreOrderMenuItemStatus status;
		private Integer capacity;
		private Integer ordered;
		private LocalDate date;
		private Instant dateTime;
		private PreOrderMenuEntity menu;

		private Builder() {
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder preOrderMenuId(Long preOrderMenuId) {
			this.preOrderMenuId = preOrderMenuId;
			return this;
		}

		public Builder chainId(Long chainId) {
			this.chainId = chainId;
			return this;
		}

		public Builder version(Integer version) {
			this.version = version;
			return this;
		}

		public Builder mealTimeId(Long mealTimeId) {
			this.mealTimeId = mealTimeId;
			return this;
		}

		public Builder mealTime(MealTimeEntity mealTime) {
			this.mealTime = mealTime;
			return this;
		}

		public Builder productId(Long productId) {
			this.productId = productId;
			return this;
		}

		public Builder product(ProductEntity product) {
			this.product = product;
			return this;
		}

		public Builder status(PreOrderMenuItemStatus status) {
			this.status = status;
			return this;
		}

		public Builder capacity(Integer capacity) {
			this.capacity = capacity;
			return this;
		}

		public Builder ordered(Integer ordered) {
			this.ordered = ordered;
			return this;
		}

		public Builder date(LocalDate date) {
			this.date = date;
			return this;
		}

		public Builder dateTime(Instant dateTime) {
			this.dateTime = dateTime;
			return this;
		}

		public Builder menu(PreOrderMenuEntity menu) {
			this.menu = menu;
			return this;
		}

		public PreOrderMenuItemEntity build() {
			return new PreOrderMenuItemEntity(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof PreOrderMenuItemEntity that))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, that.id)
				.append(preOrderMenuId, that.preOrderMenuId)
				.append(chainId, that.chainId)
				.append(version, that.version)
				.append(mealTimeId, that.mealTimeId)
				.append(mealTime, that.mealTime)
				.append(productId, that.productId)
				.append(product, that.product)
				.append(status, that.status)
				.append(capacity, that.capacity)
				.append(ordered, that.ordered)
				.append(date, that.date)
				.append(dateTime, that.dateTime)
				.append(menu, that.menu)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(preOrderMenuId)
				.append(chainId)
				.append(version)
				.append(mealTimeId)
				.append(mealTime)
				.append(productId)
				.append(product)
				.append(status)
				.append(capacity)
				.append(ordered)
				.append(date)
				.append(dateTime)
				.append(menu)
				.toHashCode();
	}
}
