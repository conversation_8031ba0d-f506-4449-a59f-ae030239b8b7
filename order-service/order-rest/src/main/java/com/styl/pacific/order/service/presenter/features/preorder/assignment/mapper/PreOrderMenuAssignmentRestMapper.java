/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.preorder.assignment.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.PreOrderMenuAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.command.PreOrderMenuAssignmentCommand;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.DeletePreOrderMenuAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuAssignmentFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuAssignmentPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuAssignment;
import com.styl.pacific.order.service.presenter.features.preorder.menu.mapper.PreOrderMenuRestMapper;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.PreOrderMenuAssignmentRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.DeletePreOrderMenuAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PaginationPreOrderMenuAssignmentRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuAssignmentFilterRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemAssignmentFilterRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemSummaryFilterRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemSummaryQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuAssignmentResponse;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuAssignmentStubResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuItemLightResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { PreOrderMenuRestMapper.class, CommonDataMapper.class,
		MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface PreOrderMenuAssignmentRestMapper {
	PreOrderMenuAssignmentRestMapper INSTANCE = Mappers.getMapper(PreOrderMenuAssignmentRestMapper.class);

	PreOrderMenuAssignmentCommand toCommand(PreOrderMenuAssignmentRequest request);

	PreOrderMenuAssignmentFilter toFilter(PreOrderMenuAssignmentFilterRequest request);

	PreOrderMenuAssignmentPagingQuery toPagingQuery(PaginationPreOrderMenuAssignmentRequest request);

	DeletePreOrderMenuAssignmentQuery toQuery(DeletePreOrderMenuAssignmentQueryRequest request);

	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "userGroupId", qualifiedByName = "baseLongIdToLong")
	PreOrderMenuAssignmentResponse toResponse(PreOrderMenuAssignmentDto dto);

	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "userGroupId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", qualifiedByName = "baseLongIdToLong")
	PreOrderMenuAssignmentStubResponse toStubResponse(PreOrderMenuAssignment dto);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "chainId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "dateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", qualifiedByName = "instantToLong")
	PreOrderMenuItemLightResponse toResponse(PreOrderMenuItemLightDto dto);

	PreOrderMenuItemAssignmentFilter toMenuItemFilter(PreOrderMenuItemAssignmentFilterRequest filter);

	PreOrderMenuItemAssignmentQuery toMenuItemQuery(PreOrderMenuItemAssignmentQueryRequest request);

	PreOrderMenuAssignmentQuery toQuery(PreOrderMenuAssignmentQueryRequest request);

	PreOrderMenuItemSummaryFilter toFilter(PreOrderMenuItemSummaryFilterRequest request);

	PreOrderMenuItemSummaryQuery toQuery(PreOrderMenuItemSummaryQueryRequest request);
}
