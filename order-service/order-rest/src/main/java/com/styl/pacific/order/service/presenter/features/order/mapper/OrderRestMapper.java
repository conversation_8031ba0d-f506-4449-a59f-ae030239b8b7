/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.order.mapper;

import com.styl.pacific.aws.s3.mapper.Mapstruct3SMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLineItemDto;
import com.styl.pacific.order.service.domain.features.order.dto.command.cancel.CancelOrderCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.collect.CollectOrderCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.labels.GenerateOrderLabelsCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.CategoryMetadataOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.CreateOrderOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.HealthierChoiceMetadataOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineDetailCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemOptionCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemOptionItemCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflinePaymentCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.ProductImageMetadataOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.ProductMetadataOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.CategoryMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.CreateOrderCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.CreateOrderLineItemCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.CreateOrderPaymentCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.HealthierChoiceMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.OrderDetailCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.ProductImageMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.ProductMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.ProductOptionItemMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.ProductOptionMetadataCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.CreateOrderV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderDetailV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderLineItemOptionItemV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderLineItemOptionV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderLineItemV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderPaymentV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.OrderServiceChargeV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.common.PlaceOrderDto;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import com.styl.pacific.order.service.domain.features.order.valueobjects.CategoryMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.HealthierChoiceMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductImageMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductOptionItemMetadata;
import com.styl.pacific.order.service.presenter.features.mealtime.mapper.MealTimeRestMapper;
import com.styl.pacific.order.service.shared.http.order.v1.request.GenerateOrderLabelsRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.OrderFilterRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.PaginationOrderQueryRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.cancel.CancelOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.collect.CollectOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.CategoryMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.CreateOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.HealthierChoiceMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.OrderDetailRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.OrderLineItemRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.OrderPaymentRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.ProductImageMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.ProductMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.ProductOptionItemMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.ProductOptionMetadataRequest;
import com.styl.pacific.order.service.shared.http.order.v1.response.PlaceOrderResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.CategoryMetadataResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.HealthierChoiceMetadataResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderDtoResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderLightDtoResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderLineItemDtoResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderLineItemResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderServiceChargeResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.ProductImageMetadataResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.ProductMetadataResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.ProductOptionItemMetadataResponse;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.CategoryMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.CreateOrderOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.HealthierChoiceMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineDetailRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemOptionItemRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemOptionRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineLineItemRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflinePaymentRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.OrderOfflineServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.ProductImageMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.offline.place.ProductMetadataOfflineRequest;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.CreateOrderV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderDetailV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderLineItemOptionItemV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderLineItemOptionV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderLineItemV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderPaymentV2Request;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderServiceChargeV2Request;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MealTimeRestMapper.class, CommonDataMapper.class,
		Mapstruct3SMapper.class, MapstructCommonDomainMapper.class, MapstructCommonMapper.class })
public interface OrderRestMapper {
	OrderRestMapper INSTANCE = Mappers.getMapper(OrderRestMapper.class);

	HealthierChoiceMetadataCommand healthierChoiceMetadataRequestToCommand(HealthierChoiceMetadataRequest request);

	CategoryMetadataCommand categoryMetadataRequestToCommand(CategoryMetadataRequest request);

	ProductMetadataCommand productMetadataRequestToCommand(ProductMetadataRequest request);

	ProductOptionItemMetadataCommand productOptionItemMetadataRequestToCommand(
			ProductOptionItemMetadataRequest request);

	ProductOptionMetadataCommand productOptionMetadataRequestToCommand(ProductOptionMetadataRequest request);

	ProductImageMetadataCommand productImageMetadataRequestToCommand(ProductImageMetadataRequest request);

	@Mapping(target = "note", source = "note", qualifiedByName = { "stringToClearance" })
	CreateOrderLineItemCommand createOrderLineItemRequestToCommand(OrderLineItemRequest request);

	CreateOrderPaymentCommand createOrderPaymentRequestToCommand(OrderPaymentRequest request);

	@Mapping(target = "customerName", source = "customerName", qualifiedByName = { "stringToClearance" })
	@Mapping(target = "note", source = "note", qualifiedByName = { "stringToClearance" })
	OrderDetailCommand createOrderRequestToCommand(OrderDetailRequest request);

	CreateOrderCommand createOrderManifestRequestToCommand(CreateOrderRequest request);

	@Mapping(target = "orderId", qualifiedByName = "baseLongIdToLong")
	PlaceOrderResponse placeOrderDtoToResponse(PlaceOrderDto dto);

	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "status", source = "status")
	@Mapping(target = "paymentStatus", source = "paymentStatus")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "collectedAt", source = "collectedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "collectionDateTime", source = "collectionDateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "cancellationDueAt", source = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", source = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", source = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "orderedAt", source = "orderedAt", qualifiedByName = "instantToLong")
	OrderResponse orderToResponse(Order order);

	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuItemId", source = "menuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuItemId", source = "preOrderMenuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "collectionDateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	OrderLineItemResponse orderLineItemToResponse(OrderLineItem orderLineItem);

	@Mapping(target = "cancellationType", source = "cancellationType")
	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "status", source = "status")
	@Mapping(target = "paymentStatus", source = "paymentStatus")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "collectedAt", source = "collectedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "collectionDateTime", source = "collectionDateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "cancellationDueAt", source = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", source = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", source = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "orderedAt", source = "orderedAt", qualifiedByName = "instantToLong")
	OrderDtoResponse orderToResponse(OrderDto order);

	@Mapping(target = "cancellationType", source = "cancellationType")
	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "status", source = "status")
	@Mapping(target = "paymentStatus", source = "paymentStatus")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "collectedAt", source = "collectedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "collectionDateTime", source = "collectionDateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "cancellationDueAt", source = "cancellationDueAt", qualifiedByName = "instantToLong")
	@Mapping(target = "canceledAt", source = "canceledAt", qualifiedByName = "instantToLong")
	@Mapping(target = "expiredAt", source = "expiredAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "orderedAt", source = "orderedAt", qualifiedByName = "instantToLong")
	OrderLightDtoResponse toResponse(OrderLightDto order);

	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuItemId", source = "menuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuItemId", source = "preOrderMenuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "collectionDateTime", qualifiedByName = "instantToLong")
	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	OrderLineItemDtoResponse orderLineItemToResponse(OrderLineItemDto orderLineItem);

	@Mapping(target = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "serviceChargeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "chargeFixedAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "appliedAt", qualifiedByName = "instantToLong")
	OrderServiceChargeResponse toResponse(OrderServiceCharge orderServiceCharge);

	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "listingPrice", source = "listingPrice", qualifiedByName = "moneyToBigInteger")
	ProductMetadataResponse productMetadataCommandToResponse(ProductMetadata productMetadata);

	@Mapping(target = "symbol", source = "symbolPath", qualifiedByName = "pathToFileResponse")
	HealthierChoiceMetadataResponse healthierChoiceMetadataToResponse(HealthierChoiceMetadata healthierChoiceMetadata);

	@Mapping(target = "icon", source = "iconPath", qualifiedByName = "pathToFileResponse")
	CategoryMetadataResponse categoryMetadataToResponse(CategoryMetadata categoryMetadata);

	@Mapping(target = "additionalPrice", source = "additionalPrice", qualifiedByName = "moneyToBigInteger")
	ProductOptionItemMetadataResponse productOptionMetadataToResponse(ProductOptionItemMetadata productOptionMetadata);

	@Mapping(target = "image", source = "imagePath", qualifiedByName = "pathToFileResponse")
	ProductImageMetadataResponse productImageMetadataToResponse(ProductImageMetadata productImageMetadata);

	OrderFilter orderFilterRequestToQuery(OrderFilterRequest request);

	OrderPagingQuery getOrdersQueryRequestToQuery(PaginationOrderQueryRequest request);

	CancelOrderCommand cancelOrderRequestToCommand(CancelOrderRequest request);

	CollectOrderCommand collectOrderRequestToCommand(CollectOrderRequest request);

	GenerateOrderLabelsCommand toCommand(GenerateOrderLabelsRequest request);

	CreateOrderV2Command toCommand(CreateOrderV2Request request);

	OrderDetailV2Command toCommand(OrderDetailV2Request request);

	OrderPaymentV2Command toCommand(OrderPaymentV2Request request);

	OrderLineItemV2Command toCommand(OrderLineItemV2Request request);

	OrderLineItemOptionV2Command toCommand(OrderLineItemOptionV2Request request);

	OrderLineItemOptionItemV2Command toCommand(OrderLineItemOptionItemV2Request request);

	OrderServiceChargeV2Command toCommand(OrderServiceChargeV2Request request);

	CreateOrderOfflineCommand toCommand(CreateOrderOfflineRequest request);

	@Mapping(target = "canceledAt", qualifiedByName = "longToInstant")
	@Mapping(target = "cancellationDueAt", qualifiedByName = "longToInstant")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "longToInstant")
	@Mapping(target = "orderedAt", source = "orderedAt", qualifiedByName = "longToInstant")
	OrderOfflineDetailCommand toCommand(OrderOfflineDetailRequest request);

	OrderOfflinePaymentCommand toCommand(OrderOfflinePaymentRequest request);

	OrderOfflineLineItemCommand toCommand(OrderOfflineLineItemRequest request);

	OrderOfflineLineItemOptionCommand toCommand(OrderOfflineLineItemOptionRequest request);

	OrderOfflineLineItemOptionItemCommand toCommand(OrderOfflineLineItemOptionItemRequest request);

	OrderOfflineServiceChargeCommand toCommand(OrderOfflineServiceChargeRequest request);

	ProductMetadataOfflineCommand toCommand(ProductMetadataOfflineRequest request);

	CategoryMetadataOfflineCommand toCommand(CategoryMetadataOfflineRequest request);

	HealthierChoiceMetadataOfflineCommand toCommand(HealthierChoiceMetadataOfflineRequest request);

	ProductImageMetadataOfflineCommand toCommand(ProductImageMetadataOfflineRequest request);

}
