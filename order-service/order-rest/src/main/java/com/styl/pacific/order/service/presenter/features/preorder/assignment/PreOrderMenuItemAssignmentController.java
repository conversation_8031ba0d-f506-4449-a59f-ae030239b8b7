/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.preorder.assignment;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.input.service.PreOrderMenuItemAssignmentService;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.presenter.features.preorder.assignment.mapper.PreOrderMenuAssignmentRestMapper;
import com.styl.pacific.order.service.presenter.features.preorder.menu.mapper.PreOrderMenuRestMapper;
import com.styl.pacific.order.service.shared.http.preorder.assignment.PreOrderMenuItemAssignmentApi;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemSummaryQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuItemAssignmentResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuItemLightResponse;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@RequiredArgsConstructor
public class PreOrderMenuItemAssignmentController implements PreOrderMenuItemAssignmentApi {
	private final PreOrderMenuItemAssignmentService preOrderMenuItemAssignmentService;
	private final RequestContext context;

	@Override
	public Content<PreOrderMenuItemAssignmentResponse> findAll(PreOrderMenuItemAssignmentQueryRequest request) {
		PreOrderMenuItemAssignmentQuery query = PreOrderMenuAssignmentRestMapper.INSTANCE.toMenuItemQuery(request);
		Content<PreOrderMenuItemAssignmentDto> items = preOrderMenuItemAssignmentService.findAll(Optional.ofNullable(
				context.getTenantId())
				.map(TenantId::new)
				.orElse(null), context.getTokenClaim(), query);
		return items.map(PreOrderMenuRestMapper.INSTANCE::preOrderMenuItemAssignmentDtoToResponse);
	}

	@Override
	public Paging<PreOrderMenuItemLightResponse> findSummaryAllPaging(PreOrderMenuItemSummaryQueryRequest request) {
		PreOrderMenuItemSummaryQuery query = PreOrderMenuAssignmentRestMapper.INSTANCE.toQuery(request);
		Paging<PreOrderMenuItemLightDto> paging = preOrderMenuItemAssignmentService.findSummaryAllPaging(new TenantId(
				context.getTenantId()), context.getTokenClaim(), query);
		return paging.map(PreOrderMenuAssignmentRestMapper.INSTANCE::toResponse);
	}
}
