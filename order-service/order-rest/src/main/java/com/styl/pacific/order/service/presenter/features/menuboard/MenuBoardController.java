/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.menuboard;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.CreateMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.UpdateMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.PaginationMenuBoardQuery;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoard;
import com.styl.pacific.order.service.domain.features.menuboard.ports.input.service.MenuBoardService;
import com.styl.pacific.order.service.presenter.features.menuboard.mapper.MenuBoardRestMapper;
import com.styl.pacific.order.service.shared.http.menuboard.MenuBoardApi;
import com.styl.pacific.order.service.shared.http.menuboard.request.CreateMenuBoardRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.UpdateMenuBoardRequest;
import com.styl.pacific.order.service.shared.http.menuboard.request.query.PaginationMenuBoardQueryRequest;
import com.styl.pacific.order.service.shared.http.menuboard.response.MenuBoardResponse;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@RequiredArgsConstructor
public class MenuBoardController implements MenuBoardApi {
	private final MenuBoardService menuBoardService;
	private final RequestContext requestContext;

	@Override
	public MenuBoardResponse findById(String id) {
		MenuBoard menuBoard = menuBoardService.findById(new TenantId(requestContext.getTenantId()), Optional.ofNullable(
				id)
				.filter(NumberUtils::isCreatable)
				.map(s -> new MenuBoardId(NumberUtils.createLong(s)))
				.orElse(null));
		return MenuBoardRestMapper.INSTANCE.toResponse(menuBoard);
	}

	@Override
	public Paging<MenuBoardResponse> findAllPaging(PaginationMenuBoardQueryRequest request) {
		PaginationMenuBoardQuery query = MenuBoardRestMapper.INSTANCE.toQuery(request);
		Paging<MenuBoard> paging = menuBoardService.findAll(new TenantId(requestContext.getTenantId()), query);
		return Paging.<MenuBoardResponse>builder()
				.content(paging.getContent()
						.stream()
						.map(MenuBoardRestMapper.INSTANCE::toResponse)
						.toList())
				.page(paging.getPage())
				.sort(paging.getSort())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.build();
	}

	@Override
	public MenuBoardResponse create(CreateMenuBoardRequest request) {
		CreateMenuBoardCommand command = MenuBoardRestMapper.INSTANCE.toCommand(request);
		MenuBoard menuBoard = menuBoardService.save(new TenantId(requestContext.getTenantId()), command);
		return MenuBoardRestMapper.INSTANCE.toResponse(menuBoard);
	}

	@Override
	public MenuBoardResponse update(String id, UpdateMenuBoardRequest request) {
		UpdateMenuBoardCommand command = MenuBoardRestMapper.INSTANCE.toCommand(request);
		MenuBoardId menuBoardId = Optional.ofNullable(id)
				.filter(NumberUtils::isCreatable)
				.map(s -> new MenuBoardId(NumberUtils.createLong(s)))
				.orElse(null);
		MenuBoard menuBoard = menuBoardService.update(new TenantId(requestContext.getTenantId()), menuBoardId, command);
		return MenuBoardRestMapper.INSTANCE.toResponse(menuBoard);
	}

	@Override
	public void deleteById(String id) {
		MenuBoardId menuBoardId = Optional.ofNullable(id)
				.filter(NumberUtils::isCreatable)
				.map(s -> new MenuBoardId(NumberUtils.createLong(s)))
				.orElse(null);
		menuBoardService.deleteById(new TenantId(requestContext.getTenantId()), menuBoardId);
	}
}
