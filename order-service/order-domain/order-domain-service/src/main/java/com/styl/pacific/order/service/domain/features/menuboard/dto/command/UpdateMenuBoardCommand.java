/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.dto.command;

import com.styl.pacific.order.service.domain.features.menuboard.enums.MenuBoardDisplayMode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record UpdateMenuBoardCommand(
		@NotBlank(message = "name must not be blank") @Size(max = 255, message = "name must not be greater than 255 characters") String name,
		@Valid @Size(min = 1, message = "images must not be empty") List<UpdateMenuBoardImageCommand> images,
		@NotNull(message = "displayMode must not be null") MenuBoardDisplayMode displayMode,
		@Min(value = 1, message = "delayBetweenImages must be greater than 0 seconds") Integer delayBetweenImages) {
}
