/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.servicecharge.ports.output.repository;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.model.ServiceChargeDetailDTO;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeQuery;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.enums.PlatformOrderType;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ServiceChargeRepository {

	List<ServiceChargeDetailDTO> findDetailsAll(TenantId tenantId, ServiceChargeQuery query);

	List<ServiceCharge> findAll(TenantId tenantId, ServiceChargeQuery query);

	Optional<ServiceCharge> findById(TenantId tenantId, ServiceChargeId id);

	Optional<ServiceChargeDetailDTO> findDetailById(TenantId tenantId, ServiceChargeId id);

	ServiceCharge save(ServiceCharge serviceCharge);

	ServiceCharge update(ServiceCharge serviceCharge);

	void delete(TenantId tenantId, ServiceChargeId id);

	boolean existsById(TenantId tenantId, ServiceChargeId id);

	boolean existsByName(TenantId tenantId, String name);

	boolean existsByNameAndNotId(TenantId tenantId, ServiceChargeId id, String name);

	List<ServiceCharge> findAllByPlatform(TenantId tenantId, PlatformOrderType platformOrderType, Boolean isActive);
}
