/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.ports.input.service;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface PreOrderMenuItemAssignmentService {
	Content<PreOrderMenuItemAssignmentDto> findAll(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			TokenClaim tokenClaim, @Valid PreOrderMenuItemAssignmentQuery query);

	Paging<PreOrderMenuItemLightDto> findSummaryAllPaging(
			@NotNull(message = "tenantId must not be null") TenantId tenantId, TokenClaim tokenClaim,
			@Valid PreOrderMenuItemSummaryQuery query);
}
