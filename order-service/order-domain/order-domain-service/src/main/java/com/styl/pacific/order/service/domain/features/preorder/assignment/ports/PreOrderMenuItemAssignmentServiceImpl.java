/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.ports;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderException;
import com.styl.pacific.order.service.domain.features.order.exception.UserAccessDomainForbiddenException;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.mapper.PreOrderMenuAssignmentDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.input.service.PreOrderMenuItemAssignmentService;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.output.repository.PreOrderMenuItemAssignmentRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuFilter;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuRepository;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import com.styl.pacific.order.service.domain.features.user.dto.UserDto;
import com.styl.pacific.order.service.domain.features.user.dto.UserGroupDto;
import com.styl.pacific.order.service.domain.features.user.dto.UserSubAccountDto;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserRepository;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserSubAccountRepository;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class PreOrderMenuItemAssignmentServiceImpl implements PreOrderMenuItemAssignmentService {
	private static final Logger log = LoggerFactory.getLogger(PreOrderMenuItemAssignmentServiceImpl.class);
	private static final int MAX_RANGE = 2;

	private final UserRepository userRepository;
	private final TenantRepository tenantRepository;
	private final PreOrderMenuItemAssignmentRepository preOrderMenuItemAssignmentRepository;
	private final PreOrderMenuRepository preOrderMenuRepository;
	private final UserSubAccountRepository userSubAccountRepository;

	@Override
	@Transactional(readOnly = true)
	public Content<PreOrderMenuItemAssignmentDto> findAll(TenantId tenantId, TokenClaim tokenClaim,
			PreOrderMenuItemAssignmentQuery query) {
		validateTenant(tenantId);
		query = validateAndNormalizeQuery(query);
		List<UserGroupId> groupIds = resolveUserGroups(tokenClaim, query);

		return buildMenuAssignments(tenantId, groupIds, query, !isCustomerToken(tokenClaim));
	}

	private void validateTenant(TenantId tenantId) {
		tenantRepository.findById(tenantId.getValue())
				.orElseThrow(() -> new OrderDomainException("Tenant " + tenantId.getValue()
						+ " not found"));
	}

	@SuppressWarnings("unchecked")
	private <T> T validateAndNormalizeQuery(T query) {
		if (query instanceof PreOrderMenuItemAssignmentQuery) {
			return (T) validateAssignmentQuery((PreOrderMenuItemAssignmentQuery) query);
		} else if (query instanceof PreOrderMenuItemSummaryQuery) {
			return (T) validateSummaryQuery((PreOrderMenuItemSummaryQuery) query);
		}
		return query;
	}

	private PreOrderMenuItemAssignmentQuery validateAssignmentQuery(PreOrderMenuItemAssignmentQuery query) {
		if (query == null || query.getFilter() == null) {
			return query;
		}

		validateDateRange(parseDate(query.getFilter()
				.fromDate()), parseDate(query.getFilter()
						.toDate()));

		return query.withFilter(query.getFilter()
				.withToDate(getEffectiveToDate(query.getFilter()
						.fromDate(), query.getFilter()
								.toDate())));
	}

	private PreOrderMenuItemSummaryQuery validateSummaryQuery(PreOrderMenuItemSummaryQuery query) {
		if (query == null || query.getFilter() == null) {
			return query;
		}

		validateDateRange(parseDate(query.getFilter()
				.fromDate()), parseDate(query.getFilter()
						.toDate()));

		PreOrderMenuItemSummaryFilter filter = Optional.ofNullable(query.getFilter())
				.map(f -> f.withToDate(getEffectiveToDate(f.fromDate(), f.toDate())))
				.orElse(PreOrderMenuItemSummaryFilter.builder()
						.build());

		return new PreOrderMenuItemSummaryQuery(filter, query.getSize(), query.getPage(), query.getSortDirection(),
				query.getSortFields());
	}

	private String getEffectiveToDate(String fromDate, String toDate) {
		return Optional.ofNullable(toDate)
				.orElse(DateTimeFormatter.ISO_LOCAL_DATE.format(LocalDate.parse(fromDate)
						.plusMonths(MAX_RANGE)));
	}

	private Content<PreOrderMenuItemAssignmentDto> buildMenuAssignments(TenantId tenantId, List<UserGroupId> groupIds,
			PreOrderMenuItemAssignmentQuery query, boolean isAdmin) {
		List<PreOrderMenuItemDto> items = preOrderMenuItemAssignmentRepository.findAll(tenantId, groupIds, query,
				isAdmin);

		Map<PreOrderMenuId, List<PreOrderMenuItemDto>> itemsByMenu = groupItemsByMenu(items);
		Map<PreOrderMenuId, PreOrderMenuDto> menuMap = fetchMenuDetails(tenantId, itemsByMenu.keySet());

		return createAssignmentDTOs(itemsByMenu, menuMap);
	}

	private Map<PreOrderMenuId, List<PreOrderMenuItemDto>> groupItemsByMenu(List<PreOrderMenuItemDto> items) {
		return items.stream()
				.collect(Collectors.groupingBy(PreOrderMenuItemDto::preOrderMenuId));
	}

	private Map<PreOrderMenuId, PreOrderMenuDto> fetchMenuDetails(TenantId tenantId, Set<PreOrderMenuId> menuIds) {
		Map<PreOrderMenuId, PreOrderMenuDto> menuMap = new HashMap<>();
		PreOrderMenuFilter menuFilter = createMenuFilter(menuIds);
		int page = 0;

		while (true) {
			PreOrderMenuPagingQuery query = new PreOrderMenuPagingQuery(menuFilter, 20, page, null, null);
			List<PreOrderMenuDto> menus = preOrderMenuRepository.findDtoAllWithPaging(tenantId, query)
					.getContent();

			if (menus.isEmpty())
				break;

			menus.forEach(menu -> menuMap.put(menu.id(), menu));
			page++;
		}

		return menuMap;
	}

	private PreOrderMenuFilter createMenuFilter(Set<PreOrderMenuId> menuIds) {
		return PreOrderMenuFilter.builder()
				.ids(menuIds.stream()
						.map(PreOrderMenuId::getValue)
						.collect(Collectors.toList()))
				.build();
	}

	private Content<PreOrderMenuItemAssignmentDto> createAssignmentDTOs(
			Map<PreOrderMenuId, List<PreOrderMenuItemDto>> itemsByMenu, Map<PreOrderMenuId, PreOrderMenuDto> menuMap) {
		List<PreOrderMenuItemAssignmentDto> assignments = new ArrayList<>();

		itemsByMenu.forEach((menuId, menuItems) -> {
			PreOrderMenuDto menu = menuMap.get(menuId);
			menuItems.forEach(item -> assignments.add(PreOrderMenuAssignmentDataMapper.INSTANCE
					.toPreOrderMenuItemAssignmentDto(item, menu)));
		});

		return Content.<PreOrderMenuItemAssignmentDto>builder()
				.content(assignments)
				.build();
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<PreOrderMenuItemLightDto> findSummaryAllPaging(TenantId tenantId, TokenClaim tokenClaim,
			PreOrderMenuItemSummaryQuery query) {
		validateTenant(tenantId);
		query = validateAndNormalizeQuery(query);
		List<UserGroupId> groupIds = resolveUserGroups(tokenClaim, query);
		Paging<PreOrderMenuItem> paging = preOrderMenuItemAssignmentRepository.findAllPaging(tenantId, groupIds, query,
				!isCustomerToken(tokenClaim));

		Map<PreOrderMenuId, List<PreOrderMenuItem>> itemsByMenu = paging.getContent()
				.stream()
				.collect(Collectors.groupingBy(PreOrderMenuItem::getPreOrderMenuId));
		Map<PreOrderMenuId, PreOrderMenuDto> menuMap = fetchMenuDetails(tenantId, itemsByMenu.keySet());

		return Paging.<PreOrderMenuItemLightDto>builder()
				.content(mapToItemLightDtos(menuMap, itemsByMenu))
				.page(paging.getPage())
				.totalPages(paging.getTotalPages())
				.totalElements(paging.getTotalElements())
				.sort(paging.getSort())
				.build();
	}

	private List<PreOrderMenuItemLightDto> mapToItemLightDtos(Map<PreOrderMenuId, PreOrderMenuDto> menuMap,
			Map<PreOrderMenuId, List<PreOrderMenuItem>> itemsByMenu) {
		List<PreOrderMenuItemLightDto> assignments = new ArrayList<>();

		itemsByMenu.forEach((menuId, menuItems) -> {
			PreOrderMenuDto menu = menuMap.get(menuId);
			menuItems.forEach(item -> assignments.add(PreOrderMenuAssignmentDataMapper.INSTANCE
					.toPreOrderMenuItemLightDto(menu.cutOffTime(), item)));
		});

		return assignments;
	}

	private List<UserGroupId> resolveUserGroups(TokenClaim tokenClaim, Object query) {
		if (!isCustomerToken(tokenClaim)) {
			return new ArrayList<>();
		}

		UserId userId = new UserId(Long.parseLong(tokenClaim.getUserId()));
		UserId subAccountId = extractSubAccountId(query, userId);
		UserDto userDto = fetchUserDetails(userId, subAccountId);

		return extractUserGroupIds(userDto);
	}

	private UserId extractSubAccountId(Object query, UserId defaultUserId) {
		if (query instanceof PreOrderMenuItemAssignmentQuery) {
			return Optional.ofNullable(((PreOrderMenuItemAssignmentQuery) query).getFilter())
					.map(PreOrderMenuItemAssignmentFilter::userId)
					.map(UserId::new)
					.orElse(defaultUserId);
		} else if (query instanceof PreOrderMenuItemSummaryQuery) {
			return Optional.ofNullable(((PreOrderMenuItemSummaryQuery) query).getFilter())
					.map(PreOrderMenuItemSummaryFilter::userId)
					.map(UserId::new)
					.orElse(defaultUserId);
		}
		return defaultUserId;
	}

	private UserDto fetchUserDetails(UserId userId, UserId subAccountId) {
		if (userId.equals(subAccountId)) {
			return userRepository.findById(userId)
					.orElseThrow(() -> new OrderDomainException("User " + userId.getValue()
							+ " not found"));
		}

		return userSubAccountRepository.findSubAccountByParentUserIdAndSubAccountIds(userId, List.of(subAccountId))
				.getContent()
				.stream()
				.findFirst()
				.map(UserSubAccountDto::subUser)
				.orElseThrow(() -> new UserAccessDomainForbiddenException(String.format(
						"User %s does not have permission to query sub account %s", userId.getValue(), subAccountId
								.getValue())));
	}

	private List<UserGroupId> extractUserGroupIds(UserDto userDto) {
		return Optional.of(userDto)
				.map(UserDto::userGroup)
				.map(UserGroupDto::getPath)
				.map(path -> path.split("\\."))
				.map(Arrays::stream)
				.map(stream -> stream.map(Long::parseLong)
						.map(UserGroupId::new)
						.collect(Collectors.toList()))
				.orElse(new ArrayList<>());
	}

	private LocalDate parseDate(String date) {
		return Optional.ofNullable(date)
				.map(LocalDate::parse)
				.orElse(null);
	}

	private void validateDateRange(LocalDate fromDate, LocalDate toDate) {
		if (fromDate != null && toDate != null) {
			long daysBetween = Period.between(fromDate, toDate)
					.toTotalMonths();

			if (daysBetween > MAX_RANGE) {
				throw new InvalidOrderException(String.format("Range time is over %d months between %s and %s",
						MAX_RANGE, fromDate, toDate));
			}

			if (fromDate.isAfter(toDate)) {
				throw new InvalidOrderException("fromDate is after toDate");
			}
		}
	}

	private boolean isCustomerToken(TokenClaim tokenClaim) {
		return Objects.nonNull(tokenClaim) && UserType.CUSTOMER.equals(tokenClaim.getUserType());
	}
}
