/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.handler.query;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.exception.OrderNotFoundException;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderQueryRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderQueryHandler {
	private static final Logger log = LoggerFactory.getLogger(OrderQueryHandler.class);
	private final OrderQueryRepository orderQueryRepository;

	@Transactional(readOnly = true)
	public Order findById(TenantId tenantId, OrderId orderId) {
		return orderQueryRepository.findById(tenantId, orderId)
				.orElseThrow(() -> {
					log.info("Order {} not found", orderId);
					return new OrderNotFoundException(String.format("Order %s not found", orderId.getValue()));
				});
	}

	@Transactional(readOnly = true)
	public OrderDto findDtoById(TenantId tenantId, OrderId orderId) {
		return orderQueryRepository.findDtoById(tenantId, orderId)
				.orElseThrow(() -> {
					log.info("Order {} not found", orderId);
					return new OrderNotFoundException(String.format("Order %s not found", orderId.getValue()));
				});
	}

	@Transactional(readOnly = true)
	public Paging<OrderDto> findAllDtoPageable(TenantId tenantId, OrderPagingQuery query) {
		return orderQueryRepository.findAllDtoPaging(tenantId, query);
	}

	@Transactional(readOnly = true)
	public Paging<Order> findAllPageable(TenantId tenantId, OrderPagingQuery query) {
		return orderQueryRepository.findAllPageable(tenantId, query);
	}

	@Transactional(readOnly = true)
	public Paging<OrderLight> findAllLightPageable(TenantId tenantId, OrderPagingQuery query) {
		return orderQueryRepository.findAllLightPageable(tenantId, query);
	}

	@Transactional(readOnly = true)
	public Paging<OrderLightDto> findAllLightDtoPageable(TenantId tenantId, OrderPagingQuery query) {
		return orderQueryRepository.findAllLightDtoPageable(tenantId, query);
	}
}
