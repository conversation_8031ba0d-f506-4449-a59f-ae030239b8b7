/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.ports;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.inventory.event.InventoryReservedEvent;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.producer.InventoryReservedEventProducer;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.CreateOrderOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.place.v2.CreateOrderV2Command;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.event.OrderCreatedEvent;
import com.styl.pacific.order.service.domain.features.order.handler.command.OrderCommandV2Handler;
import com.styl.pacific.order.service.domain.features.order.handler.query.OrderQueryHandler;
import com.styl.pacific.order.service.domain.features.order.helpers.OrderOfflineFlowValidationHelper;
import com.styl.pacific.order.service.domain.features.order.helpers.OrderOfflineProcessDataHelper;
import com.styl.pacific.order.service.domain.features.order.helpers.OrderOnlineFlowValidationV12Helper;
import com.styl.pacific.order.service.domain.features.order.helpers.OrderOnlineProcessDataHelper;
import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderServiceV2;
import com.styl.pacific.order.service.domain.features.order.ports.output.producer.OrderCreatedEventProducer;
import com.styl.pacific.order.service.domain.utils.TokenClaimHelper;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class OrderServiceV2Impl implements OrderServiceV2 {

	private static final Logger log = LoggerFactory.getLogger(OrderServiceV2Impl.class);
	private final OrderOnlineFlowValidationV12Helper orderOnlineFlowValidationHelper;
	private final OrderOnlineProcessDataHelper orderOnlineProcessDataHelper;
	private final OrderOfflineFlowValidationHelper orderOfflineFlowValidationHelper;
	private final OrderOfflineProcessDataHelper orderOfflineProcessDataHelper;
	private final InventoryReservedEventProducer inventoryReservedEventProducer;
	private final OrderCreatedEventProducer orderCreatedEventProducer;
	private final OrderCommandV2Handler orderCommandHandler;
	private final OrderQueryHandler orderQueryHandler;

	@Override
	public Order create(TenantId tenantId, TokenClaim tokenClaim, CreateOrderV2Command command) {
		orderOnlineFlowValidationHelper.validateInputOrderProcess(tenantId, tokenClaim, command);
		Order order = orderOnlineProcessDataHelper.processOrder(tenantId, tokenClaim, command);
		reserveInventory(tenantId, order);
		order = orderCommandHandler.create(order);
		commitOrder(tenantId, order);
		return order;
	}

	@Override
	public Order createOffline(TenantId tenantId, TokenClaim tokenClaim, CreateOrderOfflineCommand command) {
		orderOfflineFlowValidationHelper.validateInputOrderOfflineProcess(tenantId, tokenClaim, command);
		Optional<Order> optional = orderCommandHandler.findByIdempotentKey(tenantId, command.getOrder()
				.getIdempotencyKey());
		if (optional.isPresent()) {
			return optional.get();
		}
		Order order = orderOfflineProcessDataHelper.processOrderOffline(tenantId, command);
		order = orderCommandHandler.create(order);
		return order;
	}

	protected void reserveInventory(TenantId tenantId, Order order) {
		try {
			List<InventoryReservedEvent.ProductQuantity> reservations = order.getLineItems()
					.stream()
					.filter(orderLineItem -> Boolean.TRUE.equals(orderLineItem.getReversible()))
					.map(orderLineItem -> new InventoryReservedEvent.ProductQuantity(orderLineItem.getProductId(),
							orderLineItem.getQuantity()))
					.toList();
			InventoryReservedEvent event = new InventoryReservedEvent(tenantId, order.getId(), order.getStoreId(),
					reservations);
			inventoryReservedEventProducer.publish(event);
		} catch (Exception ignored) {
		}
	}

	protected void commitOrder(TenantId tenantId, Order order) {
		try {
			OrderCreatedEvent event = new OrderCreatedEvent(order);
			orderCreatedEventProducer.publish(event);
		} catch (Exception ignored) {
		}
	}

	@Override
	public Paging<OrderLightDto> findAllPaging(TenantId tenantId, TokenClaim tokenClaim, OrderPagingQuery query) {
		if (TokenClaimHelper.isCustomerToken(tokenClaim)) {
			UserId userId = new UserId(Long.parseLong(tokenClaim.getUserId()));

			Set<UserId> customerIds = Optional.ofNullable(query.getFilter())
					.flatMap(filter -> Optional.ofNullable(filter.customerIds())
							.map(longs -> longs.stream()
									.map(UserId::new)))
					.orElse(Stream.of(userId))
					.collect(Collectors.toSet());

			query = updatePagingQueryWithUserId(query, customerIds.stream()
					.map(UserId::getValue)
					.collect(Collectors.toSet()));
		}
		return orderQueryHandler.findAllLightDtoPageable(tenantId, query);
	}

	@Override
	public Paging<OrderDto> findAllDetailPaging(TenantId tenantId, TokenClaim tokenClaim, OrderPagingQuery query) {
		if (TokenClaimHelper.isCustomerToken(tokenClaim)) {
			UserId userId = new UserId(Long.parseLong(tokenClaim.getUserId()));

			Set<UserId> customerIds = Optional.ofNullable(query.getFilter())
					.flatMap(filter -> Optional.ofNullable(filter.customerIds())
							.map(longs -> longs.stream()
									.map(UserId::new)))
					.orElse(Stream.of(userId))
					.collect(Collectors.toSet());

			query = updatePagingQueryWithUserId(query, customerIds.stream()
					.map(UserId::getValue)
					.collect(Collectors.toSet()));
		}
		return orderQueryHandler.findAllDtoPageable(tenantId, query);
	}

	private OrderPagingQuery updatePagingQueryWithUserId(OrderPagingQuery query, Set<Long> customerIds) {
		OrderFilter filter = Optional.ofNullable(query.getFilter())
				.map(f -> f.withCustomerIds(customerIds))
				.orElseGet(() -> {
					final var newFilter = OrderFilter.builder();
					if (!customerIds.isEmpty()) {
						newFilter.customerIds(customerIds);
					}
					return newFilter.build();
				});

		return new OrderPagingQuery(filter, query.getSize(), query.getPage(), query.getSortDirection(), query
				.getSortFields());
	}
}
