/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.menu.helpers;

import static java.time.temporal.ChronoUnit.DAYS;

import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderCommonValidateException;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.mealtime.exception.MealTimeNotFoundException;
import com.styl.pacific.order.service.domain.features.mealtime.ports.output.repository.MealTimeRepository;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.product.dto.ProductBriefDto;
import com.styl.pacific.order.service.domain.features.product.exception.ProductNotFoundException;
import com.styl.pacific.order.service.domain.features.product.ports.output.repository.ProductRepository;
import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderMenuItemValidationHelper {
	private static final Logger log = LoggerFactory.getLogger(PreOrderMenuItemValidationHelper.class);

	private final MealTimeRepository mealTimeRepository;
	private final ProductRepository productRepository;

	@Transactional
	public void propertiesCheck(TenantId tenantId, StoreId storeId, List<MealTimeId> mealTimeIds, ProductId productId,
			LocalDate startDate, LocalDate endDate, Boolean repeated, List<DateOfWeek> availableOn) {
		if (mealTimeIds != null) {
			mealTimeIds.forEach(mealTimeId -> mealTimeExistsCheck(tenantId, mealTimeId));
		}
		if (productId != null) {
			productExistsCheck(tenantId, storeId, productId);
		}
		if (Boolean.TRUE.equals(repeated)) {
			if (endDate == null) {
				throw new OrderCommonValidateException("endDate must not be null when repeated is true");
			}
			if (availableOn == null) {
				throw new OrderCommonValidateException("availableOn must not be null when repeated is true");
			}
		}
		if (endDate != null) {
			if (startDate.isAfter(endDate)) {
				log.info("startDate {} must be before endDate {}", startDate, endDate);
				throw new OrderCommonValidateException("startDate must be before endDate");
			}
			if (Duration.ofDays(DAYS.between(startDate, endDate))
					.toDays() > 180) {
				log.info("The difference between startDate {} and endDate {} must not exceed 180 days", startDate,
						endDate);
				throw new OrderCommonValidateException(
						"The difference between startDate and endDate must not exceed 180 days");
			}

		}

		if (availableOn != null && availableOn.isEmpty()) {
			throw new OrderCommonValidateException("availableOn must not be empty");
		}
	}

	private void productExistsCheck(TenantId tenantId, StoreId storeId, ProductId productId) {
		Optional<ProductBriefDto> briefDtoOptional = productRepository.findBriefById(tenantId, productId);
		if (briefDtoOptional.isEmpty()) {
			log.info("Product {} not found", productId.getValue());
			throw new ProductNotFoundException(String.format("Product %s not found", productId.getValue()));
		}
		if (Boolean.FALSE.equals(briefDtoOptional.get()
				.storeId()
				.equals(storeId))) {
			throw new OrderDomainException(String.format("Product %s not belong to store %s", productId.getValue(),
					storeId.getValue()));
		}
	}

	private void mealTimeExistsCheck(TenantId tenantId, MealTimeId mealTimeId) {
		boolean isExist = mealTimeRepository.existsById(tenantId, mealTimeId);
		if (!isExist) {
			log.info("Meal Time {} not found", mealTimeId.getValue());
			throw new MealTimeNotFoundException(String.format("Meal Time %s not found", mealTimeId.getValue()));
		}
	}
}
