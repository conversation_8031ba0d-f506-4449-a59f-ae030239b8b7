/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.MenuItemId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderCommonValidateException;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.menu.dto.command.arrangement.ArrangementCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.command.item.CreateMenuItemCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.command.item.UpdateMenuItemCommand;
import com.styl.pacific.order.service.domain.features.menu.dto.common.MenuItemDto;
import com.styl.pacific.order.service.domain.features.menu.dto.query.item.MenuItemFilter;
import com.styl.pacific.order.service.domain.features.menu.dto.query.item.PaginationMenuItemQuery;
import com.styl.pacific.order.service.domain.features.menu.entity.Menu;
import com.styl.pacific.order.service.domain.features.menu.entity.MenuItem;
import com.styl.pacific.order.service.domain.features.menu.exception.MenuItemNotFoundException;
import com.styl.pacific.order.service.domain.features.menu.exception.MenuItemProductAlreadyAssignException;
import com.styl.pacific.order.service.domain.features.menu.exception.MenuNotFoundException;
import com.styl.pacific.order.service.domain.features.menu.id.generator.MenuItemIdGenerator;
import com.styl.pacific.order.service.domain.features.menu.mapper.MenuItemDataMapper;
import com.styl.pacific.order.service.domain.features.menu.ports.input.service.MenuItemService;
import com.styl.pacific.order.service.domain.features.menu.ports.output.repository.MenuItemRepository;
import com.styl.pacific.order.service.domain.features.menu.ports.output.repository.MenuRepository;
import com.styl.pacific.order.service.domain.features.product.dto.ProductBriefDto;
import com.styl.pacific.order.service.domain.features.product.exception.ProductNotFoundException;
import com.styl.pacific.order.service.domain.features.product.ports.output.repository.ProductRepository;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import com.styl.pacific.utils.position.PositionUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class MenuItemServiceImpl implements MenuItemService {
	private static final Logger log = LoggerFactory.getLogger(MenuItemServiceImpl.class);
	private final MenuItemRepository menuItemRepository;
	private final MenuRepository menuRepository;
	private final ProductRepository productRepository;
	private final TenantRepository tenantRepository;

	private final MenuItemIdGenerator menuItemIdGenerator;

	private final TenantCheckHelper tenantCheckHelper;

	private static final String MENU_ITEM_NOT_FOUND_EXCEPTION = "Menu Item {} not found";
	private static final String MENU_ITEM_NOT_FOUND_EXCEPTION_2 = "Menu Item %s not found";

	private static final String MENU_NOT_FOUND_EXCEPTION = "Menu {} not found";
	private static final String MENU_NOT_FOUND_EXCEPTION_2 = "Menu %s not found";

	@Override
	@Transactional(readOnly = true)
	public Paging<MenuItemDto> findAllPaging(TenantId tenantId, MenuId menuId, PaginationMenuItemQuery query) {
		menuCheckExists(tenantId, menuId);
		TenantDto tenantDto = tenantCheckHelper.tenantCheck(tenantId);
		ZoneId zoneId = TenantHelper.getZoneId(tenantDto);
		PaginationMenuItemQuery queryProcess = new PaginationMenuItemQuery(Optional.ofNullable(query)
				.map(PaginationMenuItemQuery::getFilter)
				.map(menuItemFilter -> {
					MenuItemFilter filter = menuItemFilter;
					Instant fromTimeLine = filter.fromTimeLine();
					if (Objects.nonNull(fromTimeLine)) {
						LocalDate fromDate = fromTimeLine.atZone(zoneId)
								.toLocalDate();
						LocalTime fromTime = fromTimeLine.atZone(zoneId)
								.toLocalTime();
						filter = filter.withFromDate(fromDate)
								.withFromTime(fromTime);
					}
					Instant toTimeLine = filter.toTimeLine();
					if (Objects.nonNull(toTimeLine)) {
						LocalDate toDate = toTimeLine.atZone(zoneId)
								.toLocalDate();
						LocalTime toTime = toTimeLine.atZone(zoneId)
								.toLocalTime();
						filter = filter.withToDate(toDate)
								.withToTime(toTime);
					}
					return filter;
				})
				.orElse(null), Optional.ofNullable(query)
						.map(PaginationQuery::getSize)
						.orElse(null), Optional.ofNullable(query)
								.map(PaginationQuery::getPage)
								.orElse(null), Optional.ofNullable(query)
										.map(PaginationQuery::getSortDirection)
										.orElse(null), Optional.ofNullable(query)
												.map(PaginationQuery::getSortFields)
												.orElse(null));
		Paging<MenuItemDto> paging = menuItemRepository.findDtoAll(menuId, queryProcess);
		return Paging.<MenuItemDto>builder()
				.content(paging.getContent())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.page(paging.getPage())
				.sort(paging.getSort())
				.build();
	}

	@Override
	@Transactional(readOnly = true)
	public MenuItemDto findById(TenantId tenantId, MenuId menuId, MenuItemId id) {
		menuCheckExists(tenantId, menuId);
		Optional<MenuItemDto> menuItemResponseOptional = menuItemRepository.findDtoById(menuId, id);
		if (menuItemResponseOptional.isEmpty()) {
			log.warn(MENU_ITEM_NOT_FOUND_EXCEPTION, id);
			throw new MenuItemNotFoundException(String.format(MENU_ITEM_NOT_FOUND_EXCEPTION_2, id.getValue()));
		}
		return menuItemResponseOptional.get();
	}

	@Override
	@Transactional
	public MenuItemDto create(TenantId tenantId, MenuId menuId, CreateMenuItemCommand command) {
		createMenuItemCommandCheck(tenantId, menuId, command);

		MenuItem menuItem = MenuItemDataMapper.INSTANCE.createMenuItemRequestToMenuItem(command);
		menuItem.setMenuId(menuId);
		menuItem.setId(menuItemIdGenerator.nextId());
		processMenuItemProperties(menuItem);

		return menuItemRepository.save(menuItem);
	}

	private void createMenuItemCommandCheck(TenantId tenantId, MenuId menuId, CreateMenuItemCommand command) {
		checkValidArrangement(command.arrangement());
		ProductId productIdObject = new ProductId(command.productId());
		Menu menu = menuCheck(tenantId, menuId);
		productExistsCheck(tenantId, menu.getStoreId(), productIdObject);
		if (menuItemRepository.existsByMenuIdAndProductId(menuId, productIdObject)) {
			log.warn("Menu Item of menu {} with product {} already exists", menuId.getValue(), productIdObject
					.getValue());
			throw new MenuItemProductAlreadyAssignException(String.format(
					"Menu Item of menu %s with product %s already exists", menuId.getValue(), productIdObject
							.getValue()));
		}
	}

	private void checkValidArrangement(ArrangementCommand arrangement) {
		if (!Objects.isNull(arrangement)) {
			if (Objects.nonNull(arrangement.startDate()) && Objects.nonNull(arrangement.endDate())) {
				Instant startDate = arrangement.startDate();
				Instant endDate = arrangement.endDate();
				if (startDate.isAfter(endDate)) {
					log.info("startDate {} should before endDate {}", startDate, endDate);
					throw new OrderCommonValidateException(String.format("startDate %s should before endDate %s",
							startDate, endDate));
				}
			}
			if (Objects.nonNull(arrangement.startTime()) && Objects.nonNull(arrangement.endTime())) {
				LocalTime startTime = LocalTime.parse(arrangement.startTime());
				LocalTime endTime = LocalTime.parse(arrangement.endTime());
				if (startTime.isAfter(endTime)) {
					log.info("fromTime {} should before toTime {}", startTime, endTime);
					throw new OrderCommonValidateException(String.format("fromTime %s should before toTime %s",
							startTime, endTime));
				}
			}
		}
	}

	@Override
	@Transactional
	public MenuItemDto update(TenantId tenantId, MenuId menuId, MenuItemId id, UpdateMenuItemCommand command) {
		;
		updateMenuItemCheck(id, command);
		menuCheckExists(tenantId, menuId);

		MenuItem menuItem = menuItemCheck(menuId, id);
		MenuItemDataMapper.INSTANCE.updateMenuItemRequestToMenuItem(menuItem, command);
		processMenuItemProperties(menuItem);

		return menuItemRepository.update(menuItem);
	}

	@Override
	@Transactional
	public void deleteById(TenantId tenantId, MenuId menuId, MenuItemId id) {
		menuCheckExists(tenantId, menuId);
		menuItemCheckExists(menuId, id);

		menuItemRepository.deleteById(menuId, id);
	}

	private void updateMenuItemCheck(MenuItemId menuItemId, UpdateMenuItemCommand command) {
		checkValidArrangement(command.arrangement());
	}

	private void processMenuItemProperties(MenuItem menuItem) {
		if (Objects.isNull(menuItem.getPosition())) {
			menuItem.setPosition(PositionUtils.calculatePosition(menuItemRepository.getMaxPosition(menuItem
					.getMenuId())));
		}
	}

	private MenuItem menuItemCheck(MenuId menuId, MenuItemId id) {
		return menuItemRepository.findById(menuId, id)
				.orElseThrow(() -> {
					log.warn(MENU_ITEM_NOT_FOUND_EXCEPTION, id.getValue());
					return new MenuItemNotFoundException(String.format(MENU_ITEM_NOT_FOUND_EXCEPTION_2, id.getValue()));
				});
	}

	private void menuItemCheckExists(MenuId menuId, MenuItemId id) {
		if (!menuItemRepository.existsById(menuId, id)) {
			log.warn(MENU_ITEM_NOT_FOUND_EXCEPTION, id.getValue());
			throw new MenuItemNotFoundException(String.format(MENU_ITEM_NOT_FOUND_EXCEPTION_2, id.getValue()));
		}
	}

	private Menu menuCheck(TenantId tenantId, MenuId menuId) {
		return menuRepository.findById(tenantId, menuId)
				.orElseThrow(() -> {
					log.warn(MENU_NOT_FOUND_EXCEPTION, menuId.getValue());
					return new MenuNotFoundException(String.format(MENU_NOT_FOUND_EXCEPTION_2, menuId.getValue()));
				});
	}

	private void menuCheckExists(TenantId tenantId, MenuId menuId) {
		if (!menuRepository.existsById(tenantId, menuId)) {
			log.warn(MENU_NOT_FOUND_EXCEPTION, menuId.getValue());
			throw new MenuNotFoundException(String.format(MENU_NOT_FOUND_EXCEPTION_2, menuId.getValue()));
		}
	}

	private void productExistsCheck(TenantId tenantId, StoreId storeId, ProductId productId) {
		Optional<ProductBriefDto> briefDtoOptional = productRepository.findBriefById(tenantId, productId);
		if (briefDtoOptional.isEmpty()) {
			log.info("Product {} not found", productId.getValue());
			throw new ProductNotFoundException(String.format("Product %s not found", productId.getValue()));
		}
		if (Boolean.FALSE.equals(briefDtoOptional.get()
				.storeId()
				.equals(storeId))) {
			throw new OrderDomainException(String.format("Product %s not belong to store %s", productId.getValue(),
					storeId.getValue()));
		}
	}

}
