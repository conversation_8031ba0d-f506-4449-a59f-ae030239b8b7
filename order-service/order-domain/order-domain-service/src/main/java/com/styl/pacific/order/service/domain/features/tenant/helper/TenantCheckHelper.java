/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.tenant.helper;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.tenant.dto.TaxDto;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TaxRepository;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TenantCheckHelper {
	private static final Logger log = LoggerFactory.getLogger(TenantCheckHelper.class);
	private final TenantRepository tenantRepository;
	private final TaxRepository taxRepository;

	public TenantDto tenantCheck(TenantId tenantId) {
		return tenantRepository.findById(tenantId.getValue())
				.orElseThrow(() -> {
					log.warn("Tenant {} not found", tenantId);
					return new OrderDomainException(String.format("Tenant %s not found", tenantId));
				});
	}

	public TaxDto getCurrentTax() {
		return taxRepository.getCurrentTax()
				.orElseThrow(() -> {
					log.warn("Tax not found");
					return new OrderDomainException("Tax not found");
				});
	}

	public TaxDto getCurrentTaxOrNull() {
		return taxRepository.getCurrentTax()
				.orElse(null);
	}
}
