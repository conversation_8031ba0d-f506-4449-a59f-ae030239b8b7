/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuInfoAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemAssignmentDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemLightDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import java.time.LocalTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface PreOrderMenuAssignmentDataMapper {
	PreOrderMenuAssignmentDataMapper INSTANCE = Mappers.getMapper(PreOrderMenuAssignmentDataMapper.class);

	PreOrderMenuItemLightDto toPreOrderMenuItemLightDto(LocalTime cutOffTime, PreOrderMenuItem model);

	PreOrderMenuInfoAssignmentDto toPreOrderMenuInfoAssignmentDto(PreOrderMenuDto model);

	@Mapping(target = "id", source = "model.id")
	@Mapping(target = "preOrderMenuId", source = "model.preOrderMenuId")
	@Mapping(target = "chainId", source = "model.chainId")
	@Mapping(target = "version", source = "model.version")
	@Mapping(target = "mealTimeId", source = "model.mealTimeId")
	@Mapping(target = "mealTime", source = "model.mealTime")
	@Mapping(target = "product", source = "model.product")
	@Mapping(target = "date", source = "model.date")
	@Mapping(target = "menu", source = "menu")
	@Mapping(target = "dateTime", source = "model.dateTime")
	@Mapping(target = "status", source = "model.status")
	@Mapping(target = "capacity", source = "model.capacity")
	@Mapping(target = "ordered", source = "model.ordered")
	@Mapping(target = "createdAt", source = "model.createdAt")
	@Mapping(target = "updatedAt", source = "model.updatedAt")
	PreOrderMenuItemAssignmentDto toPreOrderMenuItemAssignmentDto(PreOrderMenuItemDto model, PreOrderMenuDto menu);
}
