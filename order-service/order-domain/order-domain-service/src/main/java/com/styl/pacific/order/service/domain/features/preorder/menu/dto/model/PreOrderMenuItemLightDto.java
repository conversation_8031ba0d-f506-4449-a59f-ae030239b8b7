/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.menu.dto.model;

import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemChainId;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import java.time.Instant;
import java.time.LocalDate;
import lombok.Builder;
import lombok.With;

/**
 * <AUTHOR>
 */
@Builder
@With
public record PreOrderMenuItemLightDto(PreOrderMenuItemId id,
		PreOrderMenuId preOrderMenuId,
		PreOrderMenuItemChainId chainId,
		Integer version,
		MealTimeId mealTimeId,
		LocalDate date,
		Instant dateTime,
		PreOrderMenuItemStatus status,
		Integer capacity,
		Integer ordered,
		Instant createdAt,
		Instant updatedAt) {
}
