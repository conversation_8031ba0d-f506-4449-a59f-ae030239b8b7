/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.helpers;

import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.MenuId;
import com.styl.pacific.domain.valueobject.MenuItemId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.CreateOrderOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineDetailCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemOptionCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineLineItemOptionItemCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflinePaymentCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.OrderOfflineServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.order.dto.command.offline.place.ProductMetadataOfflineCommand;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderNumberAuditSequence;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import com.styl.pacific.order.service.domain.features.order.processor.OrderOnsiteNumberProcessor;
import com.styl.pacific.order.service.domain.features.order.processor.OrderPaymentProcessor;
import com.styl.pacific.order.service.domain.features.order.valueobjects.CategoryMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.HealthierChoiceMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.OrderIdGenerator;
import com.styl.pacific.order.service.domain.features.order.valueobjects.OrderLineItemIdGenerator;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductImageMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductOptionItemMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductOptionMetadata;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.helper.TenantCheckHelper;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import java.math.BigInteger;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderOfflineProcessDataHelper {
	private final TenantCheckHelper tenantCheckHelper;

	private final OrderIdGenerator orderIdGenerator;
	private final OrderLineItemIdGenerator orderLineItemIdGenerator;

	private final OrderPaymentProcessor orderPaymentProcessor;
	private final OrderNumberHelper orderNumberHelper;
	private final OrderOnsiteNumberProcessor orderNumberProcessor;

	@Transactional
	public Order processOrderOffline(TenantId tenantId, CreateOrderOfflineCommand command) {
		TenantDto tenant = Optional.of(tenantId)
				.map(tenantCheckHelper::tenantCheck)
				.orElseThrow(() -> new OrderDomainException("Tenant not found"));
		Order order = mappingOrder(tenant, command);
		processOrderOfflineNumber(tenant, order);
		processOrderOfflinePayment(order);
		return order;
	}

	private Order mappingOrder(TenantDto tenant, CreateOrderOfflineCommand command) {

		OrderOfflineDetailCommand detail = Optional.of(command.getOrder())
				.orElse(OrderOfflineDetailCommand.builder()
						.build());
		OrderOfflinePaymentCommand payment = Optional.of(command)
				.map(CreateOrderOfflineCommand::getPayment)
				.orElse(OrderOfflinePaymentCommand.builder()
						.paymentStatus(OrderPaymentStatus.PENDING)
						.build());

		var order = Order.builder()
				.id(orderIdGenerator.nextId())
				.idempotencyKey(detail.getIdempotencyKey())
				.systemSource(detail.getSystemSource())
				.orderNumber(detail.getOrderNumber())
				.tenantId(new TenantId(tenant.getTenantId()))
				.storeId(new StoreId(detail.getStoreId()))
				.type(OrderType.ONSITE_ORDER)
				.isOffline(true)
				.status(detail.getStatus())
				.staffCode(detail.getStaffCode())
				.staffName(detail.getStaffName())
				.customerId(Optional.ofNullable(detail.getCustomerId())
						.map(UserId::new)
						.orElse(null))
				.customerName(detail.getCustomerName())
				.customerEmail(detail.getCustomerEmail())
				.note(detail.getNote())
				.taxName(detail.getTaxName())
				.taxRate(detail.getTaxRate())
				.taxInclude(detail.getTaxInclude())
				.taxAmount(Optional.ofNullable(detail.getTaxAmount())
						.map(Money::new)
						.orElse(null))
				.subtotalAmount(Optional.ofNullable(detail.getSubtotalAmount())
						.map(Money::new)
						.orElse(null))
				.serviceChargeAmount(Optional.ofNullable(detail.getServiceChargeAmount())
						.map(Money::new)
						.orElse(null))
				.discountAmount(Optional.ofNullable(detail.getDiscountAmount())
						.map(Money::new)
						.orElse(null))
				.totalAmount(Optional.ofNullable(detail.getTotalAmount())
						.map(Money::new)
						.orElse(null))
				.paymentMethodId(Optional.of(payment)
						.map(OrderOfflinePaymentCommand::getPaymentMethodId)
						.map(PaymentMethodId::new)
						.orElse(null))
				.paymentMethodName(Optional.of(payment)
						.map(OrderOfflinePaymentCommand::getPaymentMethodName)
						.orElse(null))
				.paymentStatus(Optional.of(payment)
						.map(OrderOfflinePaymentCommand::getPaymentStatus)
						.orElse(null))
				.canceledAt(detail.getCanceledAt())
				.cancellationDueAt(detail.getCancellationDueAt())
				.cancellationType(detail.getCancellationType())
				.cancelReason(detail.getCancelReason())
				.currencyCode(tenant.getSettings()
						.getCurrency()
						.getCurrencyCode())
				.createdAt(detail.getCreatedAt())
				.orderedAt(detail.getOrderedAt())
				.build();
		order.setLineItems(mappingOrderLineItem(order, Optional.ofNullable(detail.getLineItems())
				.orElse(Collections.emptyList())));
		order.setServiceCharges(mappingServiceCharges(order, Optional.ofNullable(detail.getServiceCharges())
				.orElse(Collections.emptyList())));
		return order;
	}

	private List<OrderServiceCharge> mappingServiceCharges(Order order,
			List<OrderOfflineServiceChargeCommand> orderOfflineServiceChargeCommands) {
		return Optional.of(orderOfflineServiceChargeCommands)
				.orElse(List.of())
				.stream()
				.map(serviceCharge -> {
					return OrderServiceCharge.builder()
							.orderId(order.getId())
							.serviceChargeId(new ServiceChargeId(serviceCharge.getServiceChargeId()))
							.name(serviceCharge.getName())
							.description(serviceCharge.getDescription())
							.chargeFixedAmount(Optional.ofNullable(serviceCharge.getChargeFixedAmount())
									.map(Money::new)
									.orElse(null))
							.chargeRate(serviceCharge.getChargeRate())
							.amount(new Money(serviceCharge.getAmount()))
							.appliedAt(order.getCreatedAt())
							.currency(Currency.getInstance(order.getCurrencyCode()))
							.build();
				})
				.toList();
	}

	private List<OrderLineItem> mappingOrderLineItem(Order order, List<OrderOfflineLineItemCommand> commands) {
		return Optional.of(commands)
				.orElse(List.of())
				.stream()
				.map(lineItem -> OrderLineItem.builder()
						.id(orderLineItemIdGenerator.nextId())
						.orderId(order.getId())
						.tenantId(order.getTenantId())
						.menuId(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getMenuId)
								.map(MenuId::new)
								.orElse(null))
						.menuItemId(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getMenuItemId)
								.map(MenuItemId::new)
								.orElse(null))
						.productId(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getProductId)
								.map(ProductId::new)
								.orElse(null))
						.productName(lineItem.getProductName())
						.metadata(mapMetadata(lineItem.getMetadata(), lineItem.getOptions()))
						.option(Optional.ofNullable(lineItem.getOptions())
								.map(options -> options.stream()
										.map(option -> {
											String title = option.getTitle();
											return title + ": "
													+ option.getItems()
															.stream()
															.map(OrderOfflineLineItemOptionItemCommand::getName)
															.collect(Collectors.joining(", "));
										})
										.collect(Collectors.joining(", ")))
								.orElse(null))
						.quantity(lineItem.getQuantity())
						.note(lineItem.getNote())
						.quantity(lineItem.getQuantity())
						.optionPrice(Optional.ofNullable(lineItem.getOptions())
								.filter(options -> !options.isEmpty())
								.map(options -> options.stream()
										.map(option -> option.getItems()
												.stream()
												.map(OrderOfflineLineItemOptionItemCommand::getAdditionalPrice)
												.reduce(BigInteger.ZERO, BigInteger::add))
										.reduce(BigInteger.ZERO, BigInteger::add))
								.map(Money::new)
								.orElse(new Money(BigInteger.ZERO)))
						.reversible(false)
						.unitPrice(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getUnitPrice)
								.map(Money::new)
								.orElse(null))
						.totalDiscount(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getTotalDiscount)
								.map(Money::new)
								.orElse(null))
						.totalAmount(Optional.of(lineItem)
								.map(OrderOfflineLineItemCommand::getTotalAmount)
								.map(Money::new)
								.orElse(null))
						.build())
				.toList();
	}

	private ProductMetadata mapMetadata(ProductMetadataOfflineCommand productMetadata,
			List<OrderOfflineLineItemOptionCommand> options) {
		return ProductMetadata.builder()
				.name(productMetadata.getName())
				.barcode(productMetadata.getBarcode())
				.sku(productMetadata.getSku())
				.ingredients(productMetadata.getIngredients())
				.unitPrice(Optional.ofNullable(productMetadata.getUnitPrice())
						.map(Money::new)
						.orElse(null))
				.listingPrice(Optional.ofNullable(productMetadata.getListingPrice())
						.map(Money::new)
						.orElse(null))
				.images(Optional.ofNullable(productMetadata.getImages())
						.orElse(Collections.emptyList())
						.stream()
						.map(image -> ProductImageMetadata.builder()
								.imagePath(image.getImagePath())
								.build())
						.toList())
				.category(Optional.ofNullable(productMetadata.getCategory())
						.map(category -> CategoryMetadata.builder()
								.name(category.getName())
								.iconPath(category.getIconPath())
								.build())
						.orElse(null))
				.healthierChoice(Optional.ofNullable(productMetadata.getHealthierChoice())
						.map(healthierChoice -> HealthierChoiceMetadata.builder()
								.name(healthierChoice.getName())
								.symbolPath(healthierChoice.getSymbolPath())
								.build())
						.orElse(null))
				.options(Optional.ofNullable(options)
						.map(opts -> opts.stream()
								.map(opt -> ProductOptionMetadata.builder()
										.title(opt.getTitle())
										.items(Optional.ofNullable(opt.getItems())
												.map(items -> items.stream()
														.map(item -> ProductOptionItemMetadata.builder()
																.name(item.getName())
																.additionalPrice(Optional.ofNullable(item
																		.getAdditionalPrice())
																		.map(Money::new)
																		.orElse(null))
																.build())
														.collect(Collectors.toList()))
												.orElse(null))
										.build())
								.collect(Collectors.toList()))
						.orElse(null))
				.build();
	}

	private void processOrderOfflinePayment(Order order) {
		order.setNonce(UUID.randomUUID());
		order.setPaymentRef(orderPaymentProcessor.generatePaymentReference(order.getId()
				.getValue(), OrderType.ONSITE_ORDER, order.getCurrencyCode(), order.getTotalAmount(), order
						.getNonce()));
	}

	private void processOrderOfflineNumber(TenantDto tenant, Order order) {
		if (StringUtils.isNotBlank(order.getOrderNumber())) {
			return;
		}
		ZoneId zoneId = TenantHelper.getZoneId(tenant);
		OrderNumberAuditSequence auditSequence = orderNumberHelper.currentNumberAudit(order.getTenantId(), order
				.getStoreId(), zoneId);
		order.setOrderNumber(orderNumberProcessor.generateOrderNumber(auditSequence));
	}
}
