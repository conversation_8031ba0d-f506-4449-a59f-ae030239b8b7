/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu.dto.command.arrangement;

import com.styl.pacific.common.validator.timeformat.Time;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record ArrangementCommand(Instant startDate,
		Instant endDate,
		@Time(message = "startTime must be valid time format") String startTime,
		@Time(message = "endTime must be valid time format") String endTime,
		@NotNull(message = "availableOn must not be null") @Size(min = 1, message = "availableOn must have at least 1") List<DateOfWeek> availableOn) {

}
