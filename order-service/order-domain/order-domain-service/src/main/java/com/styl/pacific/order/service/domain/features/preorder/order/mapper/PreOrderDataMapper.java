/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.entity.PreOrder;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface PreOrderDataMapper {
	PreOrderDataMapper INSTANCE = Mappers.getMapper(PreOrderDataMapper.class);

	PreOrderLightDto toDto(PreOrder model);

	default OrderStatus toOrderStatus(PreOrderStatus status) {
		return switch (status) {
		case PENDING -> OrderStatus.PENDING;
		case PAID -> OrderStatus.PAID;
		case CANCELLED -> OrderStatus.CANCELLED;
		case COMPLETED -> OrderStatus.COMPLETED;
		};
	}
}
