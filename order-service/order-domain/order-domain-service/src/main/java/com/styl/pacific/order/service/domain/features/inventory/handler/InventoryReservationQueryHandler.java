/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.inventory.handler;

import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.order.service.domain.features.inventory.entity.InventoryReservation;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.repository.InventoryReservationRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class InventoryReservationQueryHandler {
	private final InventoryReservationRepository inventoryReservationRepository;

	@Transactional(readOnly = true)
	public List<InventoryReservation> findAllReservationsByOrderIds(List<OrderId> orderIds) {
		return inventoryReservationRepository.findAllByOrderIds(orderIds);
	}
}
