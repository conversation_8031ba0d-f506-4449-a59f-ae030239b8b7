/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.handlers;

import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.output.repository.PreOrderMenuAssignmentRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenu;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuAssignment;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderMenuAssignmentCommandHandler {
	private final PreOrderMenuAssignmentRepository preOrderMenuAssignmentRepository;

	@Transactional
	public List<PreOrderMenuAssignment> assignMenuToPreOrder(TenantId tenantId, List<UserGroupId> groupIds,
			PreOrderMenu menu) {
		// Map command into assignment
		List<PreOrderMenuAssignment> assignments = new ArrayList<>();
		for (UserGroupId userGroupId : Set.copyOf(groupIds)) {
			PreOrderMenuAssignment assignment = PreOrderMenuAssignment.builder()
					.tenantId(tenantId)
					.storeId(menu.getStoreId())
					.preOrderMenuId(menu.getId())
					.userGroupId(userGroupId)
					.build();
			assignments.add(assignment);
		}
		if (assignments.isEmpty()) {
			preOrderMenuAssignmentRepository.deleteAllByMenuIds(tenantId, List.of(menu.getId()));
		} else {
			preOrderMenuAssignmentRepository.deleteAllNotInMenuAssignments(assignments);
		}

		return preOrderMenuAssignmentRepository.saveDtoAll(assignments);
	}

	@Transactional
	public void deleteByMenuId(TenantId tenantId, PreOrderMenuId id) {
		preOrderMenuAssignmentRepository.deleteAllByMenuIds(tenantId, List.of(id));
	}

	@Transactional
	public void deleteByUserGroupIdAndId(TenantId tenantId, PreOrderMenuId preOrderMenuId, UserGroupId userGroupId) {
		preOrderMenuAssignmentRepository.deleteByUserGroupIdAndId(tenantId, preOrderMenuId, userGroupId);
	}
}
