/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.event;

import com.styl.pacific.order.service.domain.features.common.event.OrderEvent;
import com.styl.pacific.order.service.domain.features.order.entity.Order;

/**
 * <AUTHOR>
 */
public class OrderCancelledEvent extends OrderEvent {
	private final Order order;
	private final String reason;
	private final boolean refundable;

	public OrderCancelledEvent(Order order, String reason, boolean refundable) {
		this.order = order;
		this.reason = reason;
		this.refundable = refundable;
	}

	public Order getOrder() {
		return order;
	}

	public String getReason() {
		return reason;
	}

	public boolean isRefundable() {
		return refundable;
	}
}
