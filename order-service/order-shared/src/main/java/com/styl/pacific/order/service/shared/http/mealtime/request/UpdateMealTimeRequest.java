/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.mealtime.request;

import com.styl.pacific.common.validator.timeformat.Time;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record UpdateMealTimeRequest(
		@Size(max = 80, message = "name must be less than or equal to 80 characters") @NotBlank(message = "name must not be null or empty") String name,
		@NotNull(message = "startTime must not be null") @Time(message = "startTime must be a valid time format") String startTime,
		@NotNull(message = "endTime must not be null") @Time(message = "endTime must be a valid time format") String endTime,
		@Pattern(regexp = "^#([A-Fa-f0-9]{8}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "color is invalid") String color) {
}
