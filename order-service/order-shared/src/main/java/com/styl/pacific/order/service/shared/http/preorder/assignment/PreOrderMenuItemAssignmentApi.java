/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.assignment;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemAssignmentQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.request.query.PreOrderMenuItemSummaryQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.assignment.response.PreOrderMenuItemAssignmentResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuItemLightResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;

/**
 * <AUTHOR>
 */
public interface PreOrderMenuItemAssignmentApi {
	@GetMapping("/api/order/assignments/menu-items")
	@PacificApiAuthorized
	Content<PreOrderMenuItemAssignmentResponse> findAll(
			@Valid @SpringQueryMap @ModelAttribute PreOrderMenuItemAssignmentQueryRequest request);

	@GetMapping("/api/order/assignments/menu-items/summary")
	@PacificApiAuthorized
	Paging<PreOrderMenuItemLightResponse> findSummaryAllPaging(
			@Valid @SpringQueryMap @ModelAttribute PreOrderMenuItemSummaryQueryRequest request);
}
