/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v1.request;

import com.styl.pacific.common.validator.dateformat.Date;
import com.styl.pacific.common.validator.instant.ValidUnixTimestamp;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderPaymentStatus;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderType;
import jakarta.validation.constraints.Digits;
import java.util.List;

public record OrderFilterRequest(
		@Digits(integer = 20, fraction = 0, message = "matchesId must be a number") String matchesId,
		List<@Digits(integer = 20, fraction = 0, message = "orderId must be a number") String> orderIds,
		List<@Digits(integer = 20, fraction = 0, message = "preOrderIds must be a number") String> preOrderIds,
		@Digits(integer = 20, fraction = 0, message = "storeId must be a number") String storeId,
		String orderNumber,
		List<OrderStatus> statuses,
		List<OrderCancellationType> cancellationTypes,
		List<OrderPaymentStatus> paymentStatuses,
		List<OrderType> types,
		List<@Digits(integer = 20, fraction = 0, message = "customerId must be a number") String> customerIds,
		String customerName,
		String customerEmail,
		String paymentMethodId,
		String staffName,
		List<@Digits(integer = 20, fraction = 0, message = "mealTimeId must be a number") String> mealTimeIds,
		List<@Digits(integer = 20, fraction = 0, message = "categoryId must be a number") String> categoryIds,
		Boolean refundable,
		Boolean expired,
		Boolean isOffline,
		Long paymentTransactionId,
		@Date(message = "fromCollectionDate must be a valid format") String fromCollectionDate,
		@Date(message = "toCollectionDate must be a valid format") String toCollectionDate,
		@ValidUnixTimestamp(message = "fromOrderedTime must be a valid unix timestamp") Long fromOrderedTime,
		@ValidUnixTimestamp(message = "toOrderedTime must be a valid unix timestamp") Long toOrderedTime,
		@ValidUnixTimestamp(message = "fromTime must be a valid unix timestamp") Long fromTime,
		@ValidUnixTimestamp(message = "toTime must be a valid unix timestamp") Long toTime) {
}
