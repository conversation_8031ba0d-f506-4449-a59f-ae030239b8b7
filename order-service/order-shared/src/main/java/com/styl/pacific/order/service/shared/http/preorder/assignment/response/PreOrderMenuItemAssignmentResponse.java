/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.assignment.response;

import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.shared.http.product.response.ProductStubResponse;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record PreOrderMenuItemAssignmentResponse(String id,
		String preOrderMenuId,
		String chainId,
		String mealTimeId,
		MealTimeResponse mealTime,
		ProductStubResponse product,
		String cutOffTime,
		PreOrderMenuInfoAssignmentResponse menu,
		String date,
		Long dateTime,
		PreOrderMenuItemStatus status,
		Integer capacity,
		Integer ordered,
		Long createdAt,
		Long updatedAt) {
}
