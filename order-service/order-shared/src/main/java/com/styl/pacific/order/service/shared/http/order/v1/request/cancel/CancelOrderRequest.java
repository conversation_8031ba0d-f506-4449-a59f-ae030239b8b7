/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v1.request.cancel;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record CancelOrderRequest(
		@NotNull(message = "ids must not be null") @Size(min = 1, max = 50, message = "ids must be at least 1 and at most 50 items") @Size(min = 1, message = "ids must not be empty") List<@Digits(integer = 21, fraction = 0, message = "id must be valid number") String> ids,
		@NotBlank(message = "reason must not be blank") @Size(max = 255, message = "reason must not exceed 255 characters") String reason,
		Boolean refundable) {
}
