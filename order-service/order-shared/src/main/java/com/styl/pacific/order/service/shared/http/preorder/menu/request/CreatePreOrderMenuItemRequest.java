/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.menu.request;

import com.styl.pacific.common.validator.dateformat.Date;
import com.styl.pacific.order.service.shared.http.common.enums.DateOfWeek;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record CreatePreOrderMenuItemRequest(
		@NotNull(message = "mealTimeIds must not be null") @Size(min = 1, message = "mealTimeIds must not be empty") List<@Digits(integer = 21, fraction = 0, message = "mealTimeId must be valid number") String> mealTimeIds,
		@NotNull(message = "productId must not be null") @Digits(integer = 21, fraction = 0, message = "productId must be valid number") String productId,
		@NotNull(message = "startDate must not be null") @Date(message = "startDate must be valid date format") String startDate,
		boolean repeated,
		@Date(message = "endDate must be valid date format") String endDate,
		List<DateOfWeek> availableOn,
		@Min(value = 1, message = "capacity must be greater than or equal to 1") Integer capacity) {
}
