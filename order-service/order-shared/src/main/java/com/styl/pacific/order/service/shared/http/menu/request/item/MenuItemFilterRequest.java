/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.menu.request.item;

import com.styl.pacific.order.service.shared.http.product.enums.ProductStatus;
import jakarta.validation.constraints.Digits;
import java.util.List;
import lombok.Builder;
import lombok.With;
import org.springdoc.core.annotations.ParameterObject;

/**
 * <AUTHOR>
 */
@Builder
@With
@ParameterObject
public record MenuItemFilterRequest(
		List<@Digits(integer = 21, fraction = 0, message = "id must be valid number") String> ids,
		List<@Digits(integer = 21, fraction = 0, message = "productId must be valid number") String> productIds,
		String sku,
		String name,
		@Digits(integer = 21, fraction = 0, message = "categoryId must be a valid number") String categoryId,
		List<ProductStatus> productStatuses,
		@Digits(integer = 21, fraction = 0, message = "fromTime must be valid timestamp") Long fromTime,
		@Digits(integer = 21, fraction = 0, message = "toTime must be valid timestamp") Long toTime) {
}
