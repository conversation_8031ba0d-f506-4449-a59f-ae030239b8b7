/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v1.response.detail;

import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderPaymentStatus;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderStatus;
import com.styl.pacific.order.service.shared.http.order.v1.enums.OrderType;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */

@Builder
public record OrderLightDtoResponse(Long version,
		String id,
		String tenantId,
		String idempotencyKey,
		String systemSource,
		String storeId,
		String orderNumber,

		String staffCode,
		String staffName,
		String issuerId,
		String customerId,
		String customerEmail,
		String customerName,

		OrderStatus status,
		OrderType type,
		Boolean isOffline,

		String preOrderId,
		PreOrderMenuType preOrderMenuType,
		String mealTimeId,
		MealTimeResponse mealTime,
		String collectionDate,
		Long collectionDateTime,
		Long collectedAt,
		String collectorStaffCode,
		String collectorStaffName,
		String collectorName,

		String note,

		List<OrderServiceChargeResponse> serviceCharges,
		String taxName,
		BigDecimal taxRate,
		Boolean taxInclude,
		BigInteger taxAmount,

		BigInteger subtotalAmount,
		BigInteger serviceChargeAmount,
		BigInteger discountAmount,
		BigInteger totalAmount,
		CurrencyResponse currency,

		String paymentMethodId,
		String paymentMethodName,
		String paymentTransactionId,
		OrderPaymentStatus paymentStatus,
		String transactionRef,
		String terminalId,
		String paymentRef,
		Boolean refundable,

		Long cancellationDueAt,
		OrderCancellationType cancellationType,
		String cancelReason,
		Long canceledAt,

		Long expiredAt,
		Long orderedAt,
		Long createdAt,
		Long updatedAt) {
}
