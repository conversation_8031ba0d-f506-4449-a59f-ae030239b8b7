/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.order.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderLineItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderNumberAuditSequenceEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderServiceChargeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.CategorySchemaDto;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.HealthierChoiceSchemaDto;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.ProductImageSchemaDto;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.ProductOptionItemSchemaDto;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.ProductOptionSchemaDto;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.dto.ProductSchemaDto;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.data.access.relations.features.mealtime.mapper.MealTimeDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLineItemDto;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderNumberAuditSequence;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import com.styl.pacific.order.service.domain.features.order.valueobjects.CategoryMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.HealthierChoiceMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductImageMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductOptionItemMetadata;
import com.styl.pacific.order.service.domain.features.order.valueobjects.ProductOptionMetadata;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MealTimeDataAccessMapper.class, CommonDataAccessMapper.class,
		CommonDataMapper.class, MapstructCommonDomainMapper.class, MapstructCommonMapper.class })
public interface OrderDataAccessMapper {

	OrderDataAccessMapper INSTANCE = Mappers.getMapper(OrderDataAccessMapper.class);

	CategoryMetadata toMetadata(CategorySchemaDto metadata);

	HealthierChoiceMetadata toMetadata(HealthierChoiceSchemaDto metadata);

	ProductOptionMetadata toMetadata(ProductOptionSchemaDto metadata);

	@Mapping(target = "additionalPrice", qualifiedByName = "bigIntegerToMoney")
	ProductOptionItemMetadata toMetadata(ProductOptionItemSchemaDto metadata);

	@Mapping(target = "listingPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	ProductMetadata toMetadata(ProductSchemaDto metadata);

	@Mapping(target = "id", qualifiedByName = "longToOrderId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "preOrderId", qualifiedByName = "longToPreOrderId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "issuerId", qualifiedByName = "longToUserId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	Order toModel(OrderEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToOrderId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "preOrderId", qualifiedByName = "longToPreOrderId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "issuerId", qualifiedByName = "longToUserId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	OrderLight toLightModel(OrderEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToOrderId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "preOrderId", qualifiedByName = "longToPreOrderId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "issuerId", qualifiedByName = "longToUserId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	OrderLightDto toLightDtoModel(OrderEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToOrderLineItemId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "orderId", qualifiedByName = "longToOrderId")
	@Mapping(target = "productId", qualifiedByName = "longToProductId")
	@Mapping(target = "menuId", qualifiedByName = "longToMenuId")
	@Mapping(target = "menuItemId", qualifiedByName = "longToMenuItemId")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "longToPreOrderMenuItemId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "optionPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalDiscount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	OrderLineItem toModel(OrderLineItemEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToOrderId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "storeId", qualifiedByName = "longToStoreId")
	@Mapping(target = "preOrderId", qualifiedByName = "longToPreOrderId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "issuerId", qualifiedByName = "longToUserId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	@Mapping(target = "taxAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "subtotalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "discountAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "paymentTransactionId", qualifiedByName = "longToPaymentTransactionId")
	OrderDto toDto(OrderEntity entity);

	@Mapping(target = "id", qualifiedByName = "longToOrderLineItemId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "orderId", qualifiedByName = "longToOrderId")
	@Mapping(target = "productId", qualifiedByName = "longToProductId")
	@Mapping(target = "menuId", qualifiedByName = "longToMenuId")
	@Mapping(target = "menuItemId", qualifiedByName = "longToMenuItemId")
	@Mapping(target = "preOrderMenuId", qualifiedByName = "longToPreOrderMenuId")
	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "longToPreOrderMenuItemId")
	@Mapping(target = "mealTimeId", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "unitPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "optionPrice", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalDiscount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "totalAmount", qualifiedByName = "bigIntegerToMoney")
	OrderLineItemDto toDto(OrderLineItemEntity entity);

	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTime", source = "mealTimeId")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", source = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	OrderEntity toEntity(Order order);

	@Mapping(target = "order", ignore = true)
	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "productId", source = "productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "product.id", source = "productId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuId", source = "menuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "menuItemId", source = "menuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuId", source = "preOrderMenuId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderMenuItemId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "optionPrice", source = "optionPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalDiscount", source = "totalDiscount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	OrderLineItemEntity toEntity(OrderLineItem orderLineItem);

	@Mapping(target = "unitPrice", source = "unitPrice", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "listingPrice", source = "listingPrice", qualifiedByName = "moneyToBigInteger")
	ProductSchemaDto productToProductEntity(ProductMetadata product);

	@Mapping(target = "additionalPrice", source = "additionalPrice", qualifiedByName = "moneyToBigInteger")
	ProductOptionItemSchemaDto productOptionItemToProductOptionItemSchemaEntity(
			ProductOptionItemMetadata productOptionItem);

	ProductImageSchemaDto productImageToProductImageSchemaEntity(ProductImageMetadata productImage);

	OrderNumberAuditSequence toModel(OrderNumberAuditSequenceEntity entity);

	OrderNumberAuditSequenceEntity toEntity(OrderNumberAuditSequence orderNumberAuditSequence);

	@Mapping(target = "lineItems", ignore = true)
	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "preOrderId", source = "preOrderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTimeId", source = "mealTimeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "mealTime", source = "mealTimeId")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "issuerId", source = "issuerId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "taxAmount", source = "taxAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "subtotalAmount", source = "subtotalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "serviceChargeAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "discountAmount", source = "discountAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "paymentTransactionId", source = "paymentTransactionId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "deletedAt", ignore = true)
	void updateEntity(@MappingTarget OrderEntity entity, OrderLight order);

	@Mapping(target = "id.orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "id.serviceChargeId", source = "serviceChargeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "chargeFixedAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "currencyCode", source = "currency", qualifiedByName = "currencyToCurrencyCode")
	OrderServiceChargeEntity toEntity(OrderServiceCharge model);

	@Mapping(target = "orderId", source = "id.orderId", qualifiedByName = "longToOrderId")
	@Mapping(target = "serviceChargeId", source = "id.serviceChargeId", qualifiedByName = "longToServiceChargeId")
	@Mapping(target = "chargeFixedAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "amount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToCurrency")
	OrderServiceCharge toModel(OrderServiceChargeEntity entity);
}
