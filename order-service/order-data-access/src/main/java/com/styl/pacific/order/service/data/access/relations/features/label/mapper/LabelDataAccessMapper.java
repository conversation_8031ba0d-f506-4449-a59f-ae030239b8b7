/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.label.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.label.entity.CellFormatPropertiesEntity;
import com.styl.pacific.order.service.data.access.jpa.features.label.entity.LabelConfigEntity;
import com.styl.pacific.order.service.data.access.jpa.features.label.entity.LabelLayoutPropertiesEntity;
import com.styl.pacific.order.service.data.access.jpa.features.label.entity.PageSetupPropertiesEntity;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.label.entity.CellFormatProperties;
import com.styl.pacific.order.service.domain.features.label.entity.LabelConfig;
import com.styl.pacific.order.service.domain.features.label.entity.LabelLayoutProperties;
import com.styl.pacific.order.service.domain.features.label.entity.PageSetupProperties;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, CommonDataAccessMapper.class,
		MapstructCommonMapper.class, MapstructCommonDomainMapper.class })
public interface LabelDataAccessMapper {
	LabelDataAccessMapper INSTANCE = Mappers.getMapper(LabelDataAccessMapper.class);

	@Mapping(target = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "baseLongIdToLong")
	LabelConfigEntity toEntity(LabelConfig model);

	CellFormatPropertiesEntity toEntity(CellFormatProperties model);

	PageSetupPropertiesEntity toEntity(PageSetupProperties model);

	LabelLayoutPropertiesEntity toEntity(LabelLayoutProperties model);

	@Mapping(target = "id", qualifiedByName = "longToLabelConfigId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	LabelConfig toModel(LabelConfigEntity entity);

	CellFormatProperties toModel(CellFormatPropertiesEntity entity);

	PageSetupProperties toModel(PageSetupPropertiesEntity entity);

	LabelLayoutProperties toModel(LabelLayoutPropertiesEntity entity);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	void updateEntity(@MappingTarget LabelConfigEntity entity, LabelConfig model);
}
