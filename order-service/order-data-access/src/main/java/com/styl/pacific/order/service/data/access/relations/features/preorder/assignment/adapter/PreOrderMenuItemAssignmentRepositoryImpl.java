/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.preorder.assignment.adapter;

import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.assignment.entity.PreOrderMenuAssignmentEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.assignment.repository.PreOrderMenuAssignmentJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.assignment.specification.PreOrderMenuAssignmentSpecifications;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.assignment.specification.PreOrderMenuItemAssignmentSpecifications;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.repository.PreOrderMenuItemJpaRepository;
import com.styl.pacific.order.service.data.access.relations.features.preorder.menu.mapper.PreOrderMenuDataAccessMapper;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemAssignmentQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query.PreOrderMenuItemSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.assignment.ports.output.repository.PreOrderMenuItemAssignmentRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import com.styl.pacific.order.service.domain.features.preorder.menu.enums.PreOrderMenuStatus;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.query.FluentQuery;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class PreOrderMenuItemAssignmentRepositoryImpl implements PreOrderMenuItemAssignmentRepository {
	private final PreOrderMenuItemJpaRepository preOrderMenuItemJpaRepository;
	private final PreOrderMenuAssignmentJpaRepository preOrderMenuAssignmentJpaRepository;

	@Override
	public List<PreOrderMenuItemDto> findAll(TenantId tenantId, List<UserGroupId> groupIds,
			PreOrderMenuItemAssignmentQuery query, boolean ignoreGroupEmpty) {
		if (groupIds.isEmpty()) {
			return Collections.emptyList();
		}
		List<PreOrderMenuAssignmentEntity> assignmentEntities = getMenuAssignmentsByGroup(tenantId, groupIds, Optional
				.ofNullable(query.getFilter())
				.map(PreOrderMenuItemAssignmentFilter::menuStatuses)
				.orElse(List.of()));

		List<Long> menuIds = getMenuIdsFromAssignments(assignmentEntities);

		if (menuIds.isEmpty()) {
			return Collections.emptyList();
		}

		PreOrderMenuItemAssignmentFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(PreOrderMenuItemAssignmentFilter.builder()
						.build());
		Specification<PreOrderMenuItemEntity> specification = PreOrderMenuItemAssignmentSpecifications
				.withMultipleCriteria(Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), menuIds, ignoreGroupEmpty, Optional.ofNullable(filter.fromDate())
								.map(LocalDate::parse)
								.orElse(null), Optional.ofNullable(filter.toDate())
										.map(LocalDate::parse)
										.orElse(null), filter.mealTimeId(), filter.name(), filter.categoryId(), filter
												.productStatuses(), filter.menuItemStatuses(), filter.menuStatuses());
		int page = -1;
		Integer totalPage = null;
		List<PreOrderMenuItemDto> preOrderMenuItemDtos = new ArrayList<>();
		do {
			Pageable pageable = JpaPageableUtils.createPageable(++page, 20, new HashSet<>(query.getSortFields()), query
					.getSortDirection(), PreOrderMenuItemEntity.SORTABLE_FIELDS, PreOrderMenuItemEntity.FIELD_ID);
			Page<PreOrderMenuItemDto> paging = preOrderMenuItemJpaRepository.findAll(specification, pageable)
					.map(PreOrderMenuDataAccessMapper.INSTANCE::toDto);
			if (Objects.isNull(totalPage)) {
				totalPage = paging.getTotalPages();
			}
			if (!paging.hasContent()) {
				break;
			}
			preOrderMenuItemDtos.addAll(paging.getContent());
		} while ((page + 1) < totalPage);
		return preOrderMenuItemDtos;
	}

	@Override
	public Paging<PreOrderMenuItem> findAllPaging(TenantId tenantId, List<UserGroupId> groupIds,
			PreOrderMenuItemSummaryQuery query, boolean ignoreGroupEmpty) {
		if (groupIds.isEmpty()) {
			return Paging.empty();
		}
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), PreOrderMenuItemEntity.SORTABLE_FIELDS, PreOrderMenuItemEntity.FIELD_ID);
		if (groupIds.isEmpty()) {
			return Paging.empty();
		}
		List<PreOrderMenuAssignmentEntity> assignmentEntities = getMenuAssignmentsByGroup(tenantId, groupIds, Optional
				.ofNullable(query.getFilter())
				.map(PreOrderMenuItemSummaryFilter::menuStatuses)
				.orElse(Collections.emptyList()));

		List<Long> menuIds = getMenuIdsFromAssignments(assignmentEntities);
		if (menuIds.isEmpty()) {
			return Paging.empty();
		}

		PreOrderMenuItemSummaryFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(PreOrderMenuItemSummaryFilter.builder()
						.build());
		Specification<PreOrderMenuItemEntity> specification = PreOrderMenuItemAssignmentSpecifications
				.withMultipleCriteria(Optional.of(tenantId)
						.map(TenantId::getValue)
						.orElse(null), menuIds, ignoreGroupEmpty, Optional.ofNullable(filter.fromDate())
								.map(LocalDate::parse)
								.orElse(null), Optional.ofNullable(filter.toDate())
										.map(LocalDate::parse)
										.orElse(null), filter.mealTimeId(), filter.name(), filter.categoryId(), filter
												.productStatuses(), filter.menuItemStatuses(), filter.menuStatuses());

		Page<PreOrderMenuItem> paging = preOrderMenuItemJpaRepository.findAll(specification, pageable)
				.map(PreOrderMenuDataAccessMapper.INSTANCE::toModel);
		return Paging.<PreOrderMenuItem>builder()
				.content(paging.getContent())
				.page(paging.getNumber())
				.totalPages(paging.getTotalPages())
				.totalElements(paging.getTotalElements())
				.sort(paging.getSort()
						.map(Sort.Order::toString)
						.toList())
				.build();
	}

	private List<PreOrderMenuAssignmentEntity> getMenuAssignmentsByGroup(TenantId tenantId, List<UserGroupId> groupIds,
			List<PreOrderMenuStatus> statuses) {
		if (groupIds.isEmpty()) {
			return Collections.emptyList();
		}
		Specification<PreOrderMenuAssignmentEntity> specification = PreOrderMenuAssignmentSpecifications
				.withTenantIdAndUserGroupIdsAndStatuses(tenantId.getValue(), groupIds.stream()
						.map(UserGroupId::getValue)
						.toList(), statuses);
		return preOrderMenuAssignmentJpaRepository.findBy(specification, FluentQuery.FetchableFluentQuery::all);
	}

	private List<Long> getMenuIdsFromAssignments(List<PreOrderMenuAssignmentEntity> assignmentEntities) {
		return assignmentEntities.stream()
				.map(entity -> entity.getId()
						.getPreOrderMenuId())
				.toList();
	}

}
