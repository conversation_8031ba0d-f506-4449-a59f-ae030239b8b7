/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.mealtime.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import com.styl.pacific.order.service.data.access.relations.features.common.mapper.CommonDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataAccessMapper.class, CommonDataMapper.class,
		MapstructCommonDomainMapper.class, MapstructCommonMapper.class })
public interface MealTimeDataAccessMapper {
	MealTimeDataAccessMapper INSTANCE = Mappers.getMapper(MealTimeDataAccessMapper.class);

	@Mapping(target = "id", source = "entity.id", qualifiedByName = "longToMealTimeId")
	@Mapping(target = "tenantId", source = "entity.tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "color", source = "entity.color", qualifiedByName = "stringToColor")
	MealTime mealTimeEntityToMealTime(MealTimeEntity entity);

	@Mapping(target = "id", source = "model.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "model.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "color", source = "model.color", qualifiedByName = "colorToString")
	@Mapping(target = "deletedAt", ignore = true)
	MealTimeEntity mealTimeToMealTimeEntity(MealTime model);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "startTime", ignore = true)
	@Mapping(target = "name", ignore = true)
	@Mapping(target = "endTime", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "color", ignore = true)
	@Mapping(target = "id", source = "id", qualifiedByName = "baseLongIdToLong")
	MealTimeEntity mealTimeIdToMealTimeEntity(MealTimeId id);
}
