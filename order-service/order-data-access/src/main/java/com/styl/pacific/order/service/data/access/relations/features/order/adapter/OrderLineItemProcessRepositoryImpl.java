/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.features.order.adapter;

import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.data.access.jpa.features.order.entity.OrderLineItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.order.repository.OrderLineItemJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.order.specification.OrderLineItemSpecifications;
import com.styl.pacific.order.service.data.access.relations.features.order.mapper.OrderDataAccessMapper;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLineItemDto;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderLineItemFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderLineItemPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderLineItemProcessRepository;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class OrderLineItemProcessRepositoryImpl implements OrderLineItemProcessRepository {
	private final OrderLineItemJpaRepository orderLineItemJpaRepository;

	@Override
	public List<OrderLineItem> saveAll(List<OrderLineItem> orderLineItems) {
		List<OrderLineItemEntity> orderLineItemEntities = orderLineItems.stream()
				.map(OrderDataAccessMapper.INSTANCE::toEntity)
				.toList();
		return orderLineItemJpaRepository.saveAll(orderLineItemEntities)
				.stream()
				.map(OrderDataAccessMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public List<OrderLineItem> findAllByOrderId(TenantId tenantId, OrderId orderId) {
		Specification<OrderLineItemEntity> specification = OrderLineItemSpecifications.withTenantIdAndOrderId(tenantId
				.getValue(), orderId.getValue());
		return orderLineItemJpaRepository.findAll(specification)
				.stream()
				.map(OrderDataAccessMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	public Paging<OrderLineItemDto> findAllPaging(TenantId tenantId, OrderLineItemPagingQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), OrderLineItemEntity.SORTABLE_FIELDS, OrderLineItemEntity.FIELD_ID);
		OrderLineItemFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(OrderLineItemFilter.builder()
						.build());
		Specification<OrderLineItemEntity> specification = OrderLineItemSpecifications.withMultipleCriteria(tenantId
				.getValue(), filter.orderIds(), filter.orderLineItemIds());
		Page<OrderLineItemEntity> page = orderLineItemJpaRepository.findAll(specification, pageable);
		return Paging.<OrderLineItemDto>builder()
				.content(page.stream()
						.map(OrderDataAccessMapper.INSTANCE::toDto)
						.toList())
				.totalElements(page.getTotalElements())
				.totalPages(page.getTotalPages())
				.page(page.getNumber())
				.sort(page.getSort()
						.stream()
						.map(it -> it.getProperty() + ","
								+ it.getDirection())
						.toList())
				.build();
	}
}
