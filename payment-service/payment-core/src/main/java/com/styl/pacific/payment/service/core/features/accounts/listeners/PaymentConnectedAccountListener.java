/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.accounts.listeners;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountCommandService;
import com.styl.pacific.payment.service.core.features.accounts.request.UpdateConnectedAccountCommand;
import com.styl.pacific.payment.spi.processors.accounts.event.PaymentAccountDeauthorizedEvent;
import com.styl.pacific.payment.spi.processors.accounts.event.PaymnentAccountUpdatedEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentConnectedAccountListener {
	private final PaymentConnectedAccountCommandService commandService;

	@EventListener
	public void handleAccountUpdatedEvent(PaymnentAccountUpdatedEvent event) {
		commandService.updateConnectedAccount(UpdateConnectedAccountCommand.builder()
				.clientExternalId(event.getClientExternalId())
				.tenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(event.getTenantId()))
				.ownerEmail(event.getOwnerEmail())
				.isActive(true)
				.build());
	}

	@EventListener
	public void handleAccountUpdatedEvent(PaymentAccountDeauthorizedEvent event) {
		commandService.deactivate(UpdateConnectedAccountCommand.builder()
				.clientExternalId(event.getClientExternalId())
				.tenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(event.getTenantId()))
				.isActive(false)
				.build());
	}
}
