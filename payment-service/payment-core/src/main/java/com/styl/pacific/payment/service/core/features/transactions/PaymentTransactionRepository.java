/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.transactions;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.IdempotencyKey;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.service.core.features.transactions.request.PaymentTransactionPaginationQuery;
import java.util.Optional;

public interface PaymentTransactionRepository {
	PaymentTransaction saveTransaction(PaymentTransaction transaction);

	Optional<PaymentTransaction> findTransactionByTenantIdAndId(TenantId tenantId,
			PaymentTransactionId paymentTransactionId);

	Paging<PaymentTransaction> queryTransactions(PaymentTransactionPaginationQuery query);

	Optional<PaymentTransaction> findTransactionByTenantIdAndOfflineIdempotencyKey(TenantId tenantId,
			IdempotencyKey offlineIdempotencyKey);
}
