/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.methods;

import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public interface PaymentMethodCommandService {
	PaymentMethod createPaymentMethod(@NotNull TenantId tenantId, @NotNull UpsertPaymentMethodCommand command);

	PaymentMethod updatePaymentMethod(@NotNull TenantId tenantId, @NotNull PaymentMethodId methodId,
			@NotNull UpsertPaymentMethodCommand upsertPaymentMethodCommand);

	void deletePaymentMethodById(TenantId tenantId, PaymentMethodId paymentMethodId);

	void updatePaymentMethods(List<PaymentMethod> methods);

}
