/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.spi.processors.settlements.events;

import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@RequiredArgsConstructor
@With
public class PaymentSettlementCommandRequestEvent {
	private final String tenantId;
	private final String paymentSessionId;
	private final PaymentProcessorId paymentProcessorId;
	private final Instant paidAt;
	private final Boolean isAsync;
	private final PaymentTransactionStatus transactionStatus;

	private final Map<String, String> settlementData;
	private final Map<String, String> extraSettlementData;

	@Builder.Default
	private final UUID eventId = UUID.randomUUID();

	@Builder.Default
	private final Instant createdAt = Instant.now();

}
