/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.clients;

import com.stripe.model.Account;
import com.stripe.model.WebhookEndpoint;
import com.stripe.model.checkout.Session;
import com.stripe.param.AccountCreateParams;
import com.stripe.param.AccountLinkCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import com.styl.pacific.payment.service.integration.stripeconnect.config.StripeConnectPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripeconnect.processor.valueobject.StripeConnectPaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripeconnect.webhooks.verifier.StripeConnectEventPayloadVerifier;
import com.styl.pacific.payment.spi.processors.accounts.model.ClientPaymentConnectedAccount;
import com.styl.pacific.payment.spi.processors.accounts.model.ClientPaymentConnectedAccountLink;
import com.styl.pacific.payment.spi.processors.sessions.request.PaymentSessionData;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;

public interface StripeConnectApiClient extends StripeConnectEventPayloadVerifier {
	ClientPaymentConnectedAccount createStripeConnectedAccount(AccountCreateParams params);

	ClientPaymentConnectedAccountLink createStripeConnectedAccountLink(AccountLinkCreateParams params);

	PaymentSessionData createSession(StripeConnectPaymentMethodConfiguration configuration,
			SessionCreateParams session);

	Session getCheckoutPaymentSession(StripeConnectPaymentMethodConfiguration configuration, String stripeSessionId);

	void cancelPaymentSession(StripeConnectPaymentMethodConfiguration configuration,
			StripeConnectPaymentSessionProcessorData sessionData);

	WebhookEndpoint createWebhookEndpoint(Long endpointId, String eventType, String tenantDomainUrl,
			PaymentWebhookEndpointConfig endpoint);

	WebhookEndpoint disableWebhookEndpoint(String externalClientId);

	Account getConnectedAccount(String externalClientId);
}
