/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.netsterminal.methods;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.service.integration.netsterminal.config.NetsTerminalPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.netsterminal.exceptions.NetsPaymentMethodConfigurationException;
import com.styl.pacific.payment.shared.enums.NetsFamilyCardType;
import com.styl.pacific.payment.spi.processors.PaymentMethodConfigurationValidator;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class NetsTerminalPaymentMethodConfigurationValidator implements PaymentMethodConfigurationValidator {

	private final ObjectMapper objectMapper;

	@Override
	public PaymentProcessorId getPaymentProcessorId() {
		return PaymentProcessorId.NETS_TERMINAL_PAYMENT;
	}

	@Override
	public void validateCreateRequest(SimplePaymentMethodConfiguration processorConfig) {
		if (processorConfig == null || processorConfig.getConfigs() == null) {
			throw new NetsPaymentMethodConfigurationException("NETS payment method config is required");
		}

		final var methodConfig = objectMapper.convertValue(processorConfig.getConfigs(),
				NetsTerminalPaymentMethodConfiguration.class);

		if (methodConfig.getNetsFamilyCardType() == null) {
			throw new NetsPaymentMethodConfigurationException(String.format("NETS Family Card Type is required. %s",
					Arrays.stream(NetsFamilyCardType.values())
							.map(Enum::name)
							.collect(Collectors.joining(","))));
		}
	}
}