/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.ewallet.config;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.spi.processors.config.SystemProcessorConfiguration;
import java.util.Set;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemEWalletProcessorConfiguration implements SystemProcessorConfiguration {
	@Override
	public PaymentProcessorId getProcessorId() {
		return PaymentProcessorId.E_WALLET_PAYMENT;
	}

	@Override
	public Set<AcceptedApplication> getAcceptedApplications() {
		return Set.of(AcceptedApplication.POS, AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK,
				AcceptedApplication.GO_APP, AcceptedApplication.SOK);

	}

	@Override
	public String getProcessorName() {
		return "E-Wallet Processor";
	}

	@Override
	public String getProcessorDescription() {
		return "E-Wallet Processor handles the payment by the built-in E-Wallet system";
	}

	@Override
	public boolean getTransactionReversible() {
		return true;
	}

}
