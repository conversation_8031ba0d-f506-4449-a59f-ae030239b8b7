/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.ewallet.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.spi.processors.PaymentConfigParser;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EWalletConfigMapParserConfiguration {
	@Bean
	public PaymentConfigParser<EWalletPaymentMethodConfiguration> eWalletConfigParser(ObjectMapper objectMapper) {
		return new PaymentConfigParser<>(PaymentProcessorId.E_WALLET_PAYMENT, EWalletPaymentMethodConfiguration.class,
				objectMapper);
	}
}
