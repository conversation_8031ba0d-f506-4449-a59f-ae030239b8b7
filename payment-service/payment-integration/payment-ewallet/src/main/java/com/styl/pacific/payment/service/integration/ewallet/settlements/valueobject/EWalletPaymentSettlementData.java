/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.ewallet.settlements.valueobject;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class EWalletPaymentSettlementData {
	private final PaymentProcessorId processorId;
	private final String eventId;
	private final Long walletTransactionId;
	private final Long walletId;
	private final Long customerId;
	private final Long sourceWalletId;
	private final Long destinationWalletId;
	private final String currency;
	private final String transactionType;
	private final String transactionCategory;
	private final String amount;
	private final Instant walletTransactionCreatedAt;
	private final Long createdAt;
}
