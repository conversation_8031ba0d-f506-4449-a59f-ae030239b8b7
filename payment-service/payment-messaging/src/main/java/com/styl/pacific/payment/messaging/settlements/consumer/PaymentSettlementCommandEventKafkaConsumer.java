/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.consumer;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.payment.messaging.settlements.consumer.mapper.PaymentSettlementCommandMapper;
import com.styl.pacific.payment.service.core.features.settlements.PaymentSettlementCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.consumers.payment-service.payment-settlement-command-event.enabled", havingValue = "true")
public class PaymentSettlementCommandEventKafkaConsumer implements
		KafkaConsumer<String, com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel> {

	private final PaymentSettlementCommandService commandService;

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.consumers.payment-service.payment-settlement-command-event.retry-interval-ms}}"), attempts = "${pacific.kafka.consumers.payment-service.payment-settlement-command-event.retry-attempts}", autoCreateTopics = "false")
	@KafkaListener(groupId = "${pacific.kafka.consumers.payment-service.payment-settlement-command-event.group-id}", topics = "${pacific.kafka.consumers.payment-service.payment-settlement-command-event.topic-name}", concurrency = "${pacific.kafka.consumers.payment-service.payment-settlement-command-event.concurrency}")
	public void receive(
			com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel message,
			String key, Integer partition, Long offset) {
		log.info("Received PaymentSettlementCommandRequestEventAvroModel [{}]", message.getTenantId());

		commandService.settlePaymentSession(MapstructCommonDomainMapper.INSTANCE.longToTenantId(message.getTenantId()),
				PaymentSettlementCommandMapper.INSTANCE.toCommand(message));
	}
}
