/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.messaging.settlements.consumer.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.settlements.request.SettlePaymentSessionCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentSettlementCommandMapper {
	PaymentSettlementCommandMapper INSTANCE = Mappers.getMapper(PaymentSettlementCommandMapper.class);

	@Mapping(target = "paymentSessionId", source = "paymentSessionId", qualifiedByName = "stringToPaymentSessionId")
	@Mapping(target = "paidAt", source = "paidAt", qualifiedByName = "longToInstant")
	@Mapping(target = "paymentProcessorId", ignore = true)
	@Mapping(target = "transactionNumber", ignore = true)
	@Mapping(target = "deviceTransactionNumber", ignore = true)
	SettlePaymentSessionCommand toCommand(
			com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel event);
}
