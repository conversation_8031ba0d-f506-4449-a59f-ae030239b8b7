/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.sessions.response;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.http.sessions.response.cash.CashPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.ewallet.EWalletPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.nets.NetsPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.offline.OfflinePaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripeWebPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionParamsResponse;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = PaymentSessionParamsResponse.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = StripeWebPaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_WEB_PAYMENT),
		@JsonSubTypes.Type(value = StripeConnectPaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_CONNECT_STANDARD_WEB_PAYMENT),
		@JsonSubTypes.Type(value = NetsPaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.NETS_TERMINAL_PAYMENT),
		@JsonSubTypes.Type(value = OfflinePaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.OFFLINE_PAYMENT),
		@JsonSubTypes.Type(value = CashPaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.CASH_PAYMENT),
		@JsonSubTypes.Type(value = EWalletPaymentSessionParamsResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.E_WALLET_PAYMENT), })
@Getter
public abstract class PaymentSessionParamsResponse {
	public static final String TYPE_FIELD_NAME = "processorId";
	private final PaymentProcessorId processorId;

	private final Long expiredInMilliseconds;

	protected PaymentSessionParamsResponse(PaymentProcessorId processorId, Long expiredInMilliseconds) {
		this.processorId = processorId;
		this.expiredInMilliseconds = expiredInMilliseconds;
	}
}
