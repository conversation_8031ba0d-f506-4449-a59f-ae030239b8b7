/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.methods.request;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterPaymentMethodRequest {
	@Length(max = 120)
	private String byMethodName;

	private Set<PaymentProcessorId> byProcessorIds;

	private Set<AcceptedApplication> byAcceptedApplications;

	private Set<Long> byPaymentMethodIds;

	private Boolean isActive;
}