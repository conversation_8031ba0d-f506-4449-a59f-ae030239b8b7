/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.methods.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.methods.mapper.SimplePaymentMethodConfigurationMapper;
import com.styl.pacific.payment.service.core.features.methods.request.FilterPaymentMethodQuery;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.shared.http.methods.request.CreatePaymentMethodRequest;
import com.styl.pacific.payment.shared.http.methods.request.FilterPaymentMethodRequest;
import com.styl.pacific.payment.shared.http.methods.request.UpdatePaymentMethodRequest;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, SimplePaymentMethodConfigurationMapper.class })
public interface PaymentMethodRequestMapper {
	PaymentMethodRequestMapper INSTANCE = Mappers.getMapper(PaymentMethodRequestMapper.class);

	@Mapping(target = "processorConfig", source = "processorConfig", qualifiedByName = "mapToSimplePaymentMethodConfiguration")
	@Mapping(target = "surchargeRate", source = "surchargeRate", qualifiedByName = "bigDecimalToZeroIfNull")
	@Mapping(target = "fixedSurcharge", source = "fixedSurcharge", qualifiedByName = "longToZeroIfNull")
	@Mapping(target = "connectedAccountId", ignore = true)
	UpsertPaymentMethodCommand toUpsertPaymentMethodCommand(CreatePaymentMethodRequest request,
			@Context ObjectMapper mapper);

	@Mapping(target = "processorConfig", source = "processorConfig", qualifiedByName = "mapToSimplePaymentMethodConfiguration")
	@Mapping(target = "processorId", ignore = true)
	@Mapping(target = "surchargeRate", source = "surchargeRate", qualifiedByName = "bigDecimalToZeroIfNull")
	@Mapping(target = "fixedSurcharge", source = "fixedSurcharge", qualifiedByName = "longToZeroIfNull")
	@Mapping(target = "connectedAccountId", source = "connectedAccountId", qualifiedByName = "stringToPaymentConnectedAccountId")
	UpsertPaymentMethodCommand toUpsertPaymentMethodCommand(UpdatePaymentMethodRequest request,
			@Context ObjectMapper mapper);

	@Mapping(target = "byTenantId", ignore = true)
	@Mapping(target = "byPaymentMethodIds", source = "byPaymentMethodIds", qualifiedByName = "longsToPaymentMethodIds")
	FilterPaymentMethodQuery toFilterQueryPaymentMethod(FilterPaymentMethodRequest request);
}