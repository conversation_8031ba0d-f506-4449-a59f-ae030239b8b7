/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.methods;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.payment.rest.methods.mapper.PaymentMethodPaginationMapper;
import com.styl.pacific.payment.rest.methods.mapper.PaymentMethodRequestMapper;
import com.styl.pacific.payment.rest.methods.mapper.PaymentMethodResponseMapper;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodQueryService;
import com.styl.pacific.payment.service.core.features.methods.request.FilterPaymentMethodQuery;
import com.styl.pacific.payment.shared.exceptions.PaymentMethodNotFoundException;
import com.styl.pacific.payment.shared.http.apis.PaymentMethodApi;
import com.styl.pacific.payment.shared.http.methods.request.CreatePaymentMethodRequest;
import com.styl.pacific.payment.shared.http.methods.request.QueryPaymentMethodPaginationRequest;
import com.styl.pacific.payment.shared.http.methods.request.UpdatePaymentMethodRequest;
import com.styl.pacific.payment.shared.http.methods.response.PaymentMethodResponse;
import jakarta.validation.Valid;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PaymentMethodController implements PaymentMethodApi {

	private final PaymentMethodCommandService commandService;
	private final PaymentMethodQueryService queryService;
	private final RequestContext requestContext;
	private final ObjectMapper objectMapper;

	@Override
	public PaymentMethodResponse createPaymentMethod(@Valid CreatePaymentMethodRequest request) {
		return PaymentMethodResponseMapper.INSTANCE.toResponse(commandService.createPaymentMethod(
				MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()),
				PaymentMethodRequestMapper.INSTANCE.toUpsertPaymentMethodCommand(request, objectMapper)), objectMapper);
	}

	@Override
	public PaymentMethodResponse updatePaymentMethod(Long methodId, @Valid UpdatePaymentMethodRequest request) {
		return PaymentMethodResponseMapper.INSTANCE.toResponse(commandService.updatePaymentMethod(
				MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()),
				MapstructCommonDomainMapper.INSTANCE.longToPaymentMethodId(methodId),
				PaymentMethodRequestMapper.INSTANCE.toUpsertPaymentMethodCommand(request, objectMapper)), objectMapper);

	}

	@Override
	public PaymentMethodResponse getPaymentMethod(Long methodId) {
		return queryService.getPaymentMethodByTenantIdAndId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(
				requestContext.getTenantId()), MapstructCommonDomainMapper.INSTANCE.longToPaymentMethodId(methodId))
				.map(it -> PaymentMethodResponseMapper.INSTANCE.toResponse(it, objectMapper))
				.orElseThrow(PaymentMethodNotFoundException::new);

	}

	@Override
	public void deletePaymentMethod(Long methodId) {
		commandService.deletePaymentMethodById(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext
				.getTenantId()), MapstructCommonDomainMapper.INSTANCE.longToPaymentMethodId(methodId));
	}

	@Override
	public Paging<PaymentMethodResponse> queryPaymentMethods(
			@ModelAttribute @Valid QueryPaymentMethodPaginationRequest request) {
		final var tenantId = (MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()));
		final var paginationQuery = PaymentMethodPaginationMapper.INSTANCE.toPagingQuery(request);
		return PaymentMethodPaginationMapper.INSTANCE.toPagingResponse(queryService.queryPaymentMethods(paginationQuery
				.withFilter(Optional.ofNullable(paginationQuery.getFilter())
						.map(it -> it.withByTenantId(tenantId))
						.orElseGet(() -> FilterPaymentMethodQuery.builder()
								.byTenantId(tenantId)
								.build()))), objectMapper);
	}
}
