CREATE
    TABLE
        IF NOT EXISTS payment_reverse(
            id BIGSERIAL NOT NULL CONSTRAINT payment_reverse_pk PRIMARY KEY,
            tenant_id BIGINT NOT NULL,
            payment_session_id VARCHAR(36) NOT NULL,
            payment_transaction_id BIGINT NOT NULL,
            idempotency_key VARCHAR(64) NOT NULL,
            refunding_amount BIGINT DEFAULT 0,
            total_refunded_amount BIGINT DEFAULT 0,
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            created_at TIMESTAMP(6) DEFAULT NOW(),
            CONSTRAINT payment_reverse_payment_transaction_id_fk FOREIGN KEY(payment_transaction_id) REFERENCES payment_transactions(id)
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS payment_reverse_tenant_transaction_idempotency_uidx ON
    payment_reverse(
        tenant_id,
        payment_transaction_id,
        idempotency_key
    );
