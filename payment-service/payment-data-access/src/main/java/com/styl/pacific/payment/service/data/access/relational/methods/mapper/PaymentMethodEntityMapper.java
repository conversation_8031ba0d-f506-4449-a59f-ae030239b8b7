/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.relational.methods.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.data.access.relational.accounts.mapper.PaymentConnectedAccountEntityMapper;
import com.styl.pacific.payment.service.data.access.relational.methods.entities.PaymentMethodEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, PaymentConnectedAccountEntityMapper.class })
public interface PaymentMethodEntityMapper {
	PaymentMethodEntityMapper INSTANCE = Mappers.getMapper(PaymentMethodEntityMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "connectedAccountEntity", ignore = true)
	PaymentMethodEntity toNewEntity(PaymentMethod method);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "connectedAccountEntity", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	PaymentMethodEntity toUpdateEntity(@MappingTarget PaymentMethodEntity existing, PaymentMethod method);

	@Mapping(target = "id", source = "id", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "isTransactionReversible", ignore = true)
	@Mapping(target = "connectedAccount", source = "connectedAccountEntity")
	PaymentMethod toModel(PaymentMethodEntity entity);

}
