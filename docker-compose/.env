# Global config
COMPOSE_PROJECT_NAME=pacific
GLOBAL_NETWORK=pacific-network
GROUP_ID=pacific-backend

# Applications
APPLICATION_VERSION=0.0.1

# Kafka
KAFKA_VERSION=7.5.0
KAFDROP_VERSION=latest

# Postgres
POSTGRES_VERSION=16.3

# Keycloak
KEYCLOAK_VERSION=26.1.1
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_POSTGRES_VERSION=16.3

# MinIO
MINIO_VERSION=latest
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_DEFAULT_BUCKETS=pacific

# Debezium
DEBEZIUM_VERSION=latest
DEBEZIUM_UI_VERSION=latest

# Jaeger
JAEGER_VERSION=1.58

# Ports config
POSTGRES_PORT=5432

DEBEZIUM_PORT=8083
DEBEZIUM_UI_PORT=8084

ZOOKEEPER_PORT=2181
SCHEMA_REGISTRY_PORT=8081
KAFKA_BROKER_1_PORT=19092
KAFKA_BROKER_2_PORT=29092
KAFKA_BROKER_3_PORT=39092
KAFKA_MANAGER_PORT=9090
KAFKA_DROP_PORT=9091

MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001

KEYCLOAK_PORT=8200

JAEGER_GRPC_PORT=4317
JAEGER_HTTP_PORT=4318
JAEGER_UI_PORT=16686

SMTP4DEV_VERSION=v3
SMTP4DEV_UI_PORT=5000
SMTP4DEV_SMTP_PORT=25
SMTP4DEV_IMAP_PORT=143

DYNAMODB_VERSION=latest
DYNAMODB_PORT=8000
DYNAMODB_ADMIN_VERSION=latest
DYNAMODB_ADMIN_UI_PORT=8001


