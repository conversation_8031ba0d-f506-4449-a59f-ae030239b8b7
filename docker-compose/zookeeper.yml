version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:${KAFKA_VERSION:-latest}
    user: "0:0"
    hostname: zookeeper
    ports:
      - "${ZOOKEEPER_PORT}:2181"
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
      ZOOKEEPER_SERVERS: zookeeper:2888:3888
      KAFKA_OPTS: "-Dzookeeper.4lw.commands.whitelist=ruok"
    healthcheck:
      test: nc -z localhost 2181 || exit -1
      #test: ["CMD", "bash", "-c", "unset" , "JMX_PORT" ,";" ,"kafka-topics.sh","--zookeeper","zookeeper:2181","--list"]
      interval: 5s
      timeout: 5s
      retries: 15
    volumes:
      - "./volumes/zookeeper/data:/var/lib/zookeeper/data"
      - "./volumes/zookeeper/transactions:/var/lib/zookeeper/log"
    networks:
      - ${GLOBAL_NETWORK:-kafka}
