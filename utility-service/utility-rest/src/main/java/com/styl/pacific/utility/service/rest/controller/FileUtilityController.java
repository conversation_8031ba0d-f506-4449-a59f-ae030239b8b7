/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.rest.controller;

import com.styl.pacific.utility.service.FileUtilitiesApi;
import com.styl.pacific.utility.service.PresignedUrlResponse;
import com.styl.pacific.utility.service.domain.exception.UtilityDomainException;
import com.styl.pacific.utility.service.domain.file.FileType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utility.service.domain.file.PresignedUrl;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@RestController
public class FileUtilityController implements FileUtilitiesApi {

	private final FileUtilitiesService fileUtilitiesService;

	@Override
	public ResponseEntity<PresignedUrlResponse> getPresignedUrlDownloadFile(String filePath) {
		String url = fileUtilitiesService.getPresignedUrlDownloadFile(filePath);
		return ResponseEntity.ok(PresignedUrlResponse.builder()
				.presignedUrl(url)
				.filePath(filePath)
				.build());

	}

	@Override
	public ResponseEntity<PresignedUrlResponse> getPresignedUrlUploadImage(String fileName) {
		validateImageExtension(fileName);
		PresignedUrl presignedUrl = fileUtilitiesService.getPresignedUrlUploadFile(FileType.IMAGE, fileName, null);
		return ResponseEntity.ok(PresignedUrlResponse.builder()
				.presignedUrl(presignedUrl.url())
				.filePath(presignedUrl.filePath())
				.build());
	}

	@Override
	public ResponseEntity<PresignedUrlResponse> getPresignedUrlUploadPreset(String fileName) {
		validateCsvExtension(fileName);
		PresignedUrl presignedUrl = fileUtilitiesService.getPresignedUrlUploadFile(FileType.PRESET, fileName, null);
		return ResponseEntity.ok(PresignedUrlResponse.builder()
				.presignedUrl(presignedUrl.url())
				.filePath(presignedUrl.filePath())
				.build());
	}

	private void validateCsvExtension(String fileName) {
		if (!fileName.endsWith(".csv")) {
			throw new UtilityDomainException("Invalid csv extension");
		}
	}

	private void validateImageExtension(String fileName) {
		if (!fileName.endsWith(".jpg") && !fileName.endsWith(".jpeg") && !fileName.endsWith(".png")) {
			throw new UtilityDomainException("Invalid image extension");
		}
	}
}
