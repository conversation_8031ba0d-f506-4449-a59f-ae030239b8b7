/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.domain.dashboard;

import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utility.service.domain.dashboard.entity.DashboardConfig;
import com.styl.pacific.utility.service.domain.dashboard.input.DashboardConfigService;
import com.styl.pacific.utility.service.domain.dashboard.port.output.DashboardConfigRepository;
import com.styl.pacific.utility.service.domain.dashboard.token.DashboardParams;
import com.styl.pacific.utility.service.domain.dashboard.token.DashboardResources;
import com.styl.pacific.utility.service.domain.dashboard.token.JwtConfigProperties;
import com.styl.pacific.utility.service.domain.exception.UtilityDomainException;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import java.security.Key;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import javax.crypto.spec.SecretKeySpec;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class DashboardConfigImpl implements DashboardConfigService {

	private final DashboardConfigRepository dashboardConfigRepository;

	private final JwtConfigProperties jwtConfigProperties;

	@Override
	public DashboardConfig getDashboardConfig(TenantId tenantId, boolean tenantOnly) {
		DashboardConfig tenantDashboard = dashboardConfigRepository.getDashboardConfig(tenantId)
				.orElse(new DashboardConfig());
		if (tenantOnly || tenantId.equals(CommonDomainConstants.SYSTEM_TENANT_ID)) {
			return tenantDashboard;
		}

		DashboardConfig defaultDashboard = dashboardConfigRepository.getDashboardConfig(
				CommonDomainConstants.SYSTEM_TENANT_ID)
				.orElse(new DashboardConfig());

		tenantDashboard.setBaseUrl(defaultDashboard.getBaseUrl());
		if (!StringUtils.hasText(tenantDashboard.getDashboardId())) {
			tenantDashboard.setDashboardId(defaultDashboard.getDashboardId());
			tenantDashboard.setDashboardParams(defaultDashboard.getDashboardParams());
			tenantDashboard.setDashboardHeight(defaultDashboard.getDashboardHeight());
		}
		if (tenantDashboard.getReports() == null || tenantDashboard.getReports()
				.isEmpty()) {
			tenantDashboard.setReports(defaultDashboard.getReports());
		} else if (defaultDashboard.getReports() != null && !defaultDashboard.getReports()
				.isEmpty()) {
			tenantDashboard.getReports()
					.addAll(defaultDashboard.getReports());
		}

		return tenantDashboard;
	}

	@Override
	public DashboardConfig saveDashboardConfig(DashboardConfig dashboardConfig) {
		if (!CommonDomainConstants.SYSTEM_TENANT_ID.getValue()
				.equals(dashboardConfig.getId())) {
			dashboardConfig.setBaseUrl(null);
			dashboardConfig.setSecretKey(null);
		}
		return dashboardConfigRepository.saveDashboardConfig(dashboardConfig);
	}

	@Override
	@Transactional(readOnly = true)
	public String signDashboardToken(Long tenantId, long dashboardId) {
		DashboardConfig dashboardConfig = this.getDashboardConfig(new TenantId(tenantId), false);
		if (dashboardConfig == null) {
			throw new UtilityDomainException("Dashboard config not found");
		}
		if (!validateDashboardId(dashboardConfig.getDashboardId(), dashboardConfig)) {
			throw new UtilityDomainException("Dashboard config not found");
		}

		// Get secret key, if tenant secret key is not set, use system-tenant secret key
		String secretKey = Optional.of(dashboardConfig)
				.map(DashboardConfig::getSecretKey)
				.orElse(dashboardConfigRepository.getDashboardConfig(CommonDomainConstants.SYSTEM_TENANT_ID)
						.orElse(new DashboardConfig())
						.getSecretKey());
		if (StringUtils.hasText(secretKey)) {
			Key signingKey = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
			JwtBuilder builder = Jwts.builder()
					.claim("resource", new DashboardResources(dashboardId))
					.claim("params", new DashboardParams(String.valueOf(tenantId)))
					.expiration(new Date(System.currentTimeMillis() + jwtConfigProperties.getExpiresIn()))
					.signWith(signingKey);
			return builder.compact();
		}
		throw new UtilityDomainException("Secret Key it not set");
	}

	private boolean validateDashboardId(String dashboardId, DashboardConfig dashboardConfig) {
		if (Objects.equals(dashboardId, dashboardConfig.getDashboardId())) {
			return true;
		}
		if (dashboardConfig.getReports() != null) {
			for (DashboardConfig.ReportConfig reportConfig : dashboardConfig.getReports()) {
				if (Objects.equals(dashboardId, reportConfig.getReportId())) {
					return true;
				}
			}
		}
		return false;
	}

}
