/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.entity;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.utility.service.api.dto.PresetRecordFilter;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Builder
@RequiredArgsConstructor
public class PresetRecordEntitySpecification extends BaseSpecification<PresetRecordEntity> {

	private final Long tenantId;

	private final String presetId;

	private final Integer firstRow;

	private final Integer lastRow;

	private final Integer col;

	public PresetRecordEntitySpecification(PresetRecordFilter filter, Integer firstRow, Integer lastRow) {
		this(filter, firstRow, lastRow, null);
	}

	public PresetRecordEntitySpecification(PresetRecordFilter filter, Integer firstRow, Integer lastRow, Integer col) {
		this.tenantId = filter.getTenantId();
		this.presetId = filter.getPresetId();
		this.firstRow = firstRow;
		this.lastRow = lastRow;
		this.col = col;
	}

	@Override
	public Predicate toPredicate(Root<PresetRecordEntity> root, CriteriaQuery<?> query,
			CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotBlank(presetId)) {
			predicates.add(equals(criteriaBuilder, root.get("presetId"), presetId));
		}

		if (isNotNull(firstRow)) {
			predicates.add(greaterThanOrEqualTo(criteriaBuilder, root.get("row"), firstRow));
		}

		if (isNotNull(lastRow)) {
			predicates.add(lessThanOrEqualTo(criteriaBuilder, root.get("row"), lastRow));
		}

		if (isNotNull(col)) {
			predicates.add(equals(criteriaBuilder, root.get("col"), col));
		}

		return and(criteriaBuilder, predicates);
	}
}
