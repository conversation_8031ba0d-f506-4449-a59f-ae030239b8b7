/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.api.dto.ImportStrategy;
import com.styl.pacific.utility.service.api.dto.PresetType;
import com.styl.pacific.utility.service.data.access.jpa.converter.PresetMetadataDtoConverter;
import com.styl.pacific.utility.service.data.access.jpa.entity.dto.PresetMetadataSchemaDto;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "tb_preset")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PresetEntity extends AuditableEntity {

	@Id
	private String id;
	private Long tenantId;
	private String name;
	private String description;
	private String filePath;
	private String fileName;
	private long totalRecords;
	private long successRecords;
	private long failedRecords;

	@Enumerated(EnumType.STRING)
	private ImportStrategy strategy;

	@Enumerated(EnumType.STRING)
	private ImportStatus status;

	@Enumerated(EnumType.STRING)
	private PresetType presetType;

	@Convert(converter = PresetMetadataDtoConverter.class)
	private PresetMetadataSchemaDto metadata;

}