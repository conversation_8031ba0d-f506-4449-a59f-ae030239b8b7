/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.core.records.request.FilterDataSyncRecordQuery;
import com.styl.pacific.utility.service.data.access.jpa.entity.DataSyncRecordEntity;
import com.styl.pacific.utility.service.data.access.jpa.entity.DataSyncRecordEntitySpecification;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface DataSyncRecordEntityMapper extends StringFunctionSupport {
	DataSyncRecordEntityMapper INSTANCE = Mappers.getMapper(DataSyncRecordEntityMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "stacktrace", expression = "java(truncateString(source.getStacktrace(), 1500))")
	@Mapping(target = "jobId", source = "jobId", qualifiedByName = "dataSyncJobIdToLong")
	DataSyncRecordEntity toNewEntity(DataSyncRecord source);

	@Mapping(target = "id", source = "id", qualifiedByName = "longToDataSyncRecordId")
	@Mapping(target = "jobId", source = "jobId", qualifiedByName = "longToDataSyncJobId")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	DataSyncRecord toModel(DataSyncRecordEntity entity);

	@Mapping(target = "byTenantId", source = "byTenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "byJobId", source = "byJobId", qualifiedByName = "dataSyncJobIdToLong")
	DataSyncRecordEntitySpecification toSpecification(FilterDataSyncRecordQuery filter);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "jobId", source = "jobId", qualifiedByName = "dataSyncJobIdToLong")
	@Mapping(target = "stacktrace", expression = "java(truncateString(source.getStacktrace(), 1500))")
	DataSyncRecordEntity toUpdateEntity(@MappingTarget DataSyncRecordEntity dataSyncRecordEntity,
			DataSyncRecord source);

}
