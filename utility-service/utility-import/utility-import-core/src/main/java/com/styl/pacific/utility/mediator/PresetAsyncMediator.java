/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.mediator;

import com.styl.pacific.utility.service.PresetProcessor;
import com.styl.pacific.utility.service.PresetRepository;
import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.domain.helper.NotificationHelper;
import com.styl.pacific.utility.service.domain.preset.Preset;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PresetAsyncMediator {
	private static final Logger log = LoggerFactory.getLogger(PresetAsyncMediator.class);
	private final NotificationHelper notificationHelper;
	private final PresetRepository presetRepository;

	@Async
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void startImport(PresetProcessor presetProcessor, Preset preset, String userId) {
		try {
			presetProcessor.startImport(preset, userId, presetRepository::update);
		} catch (Exception e) {
			log.error("Error while importing preset", e);
			preset.setStatus(ImportStatus.ERROR);
			preset = presetRepository.update(preset);
		} finally {
			notificationHelper.sendNotificationImport(preset, userId);
		}

	}
}
