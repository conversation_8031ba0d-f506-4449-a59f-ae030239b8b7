/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class ThreadLocalMap<K, V> extends ThreadLocal<Map<K, V>> {

	@Override
	protected Map<K, V> initialValue() {
		return new ConcurrentHashMap<>();
	}

	public V get(K key) {
		return Optional.ofNullable(super.get())
				.map(m -> m.get(key))
				.orElse(null);
	}

	public void put(K key, V value) {
		if (super.get() == null) {
			super.set(initialValue());
		}
		super.get().put(key, value);
	}

	public boolean containsKey(K key) {
		return Optional.ofNullable(super.get())
				.map(m -> m.containsKey(key))
				.orElse(false);
	}

	public V remove(K key) {
		return Optional.ofNullable(super.get())
				.map(m -> m.remove(key))
				.orElse(null);
	}

	public boolean isEmpty() {
		return Optional.ofNullable(super.get())
				.map(Map::isEmpty)
				.orElse(true);
	}

	public V getOrDefault(K key, V value) {
		return Optional.ofNullable(super.get())
				.map(m -> m.getOrDefault(key, value))
				.orElse(value);
	}
}
