/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utility.service.domain.preset.Preset;
import com.styl.pacific.utility.service.domain.preset.PresetRecord;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class PresetHandlerHelper {

	public static final int INSERT_BATCH_SIZE = 50;

	// Match the pattern "(* SOME_TEXT_HERE *)" e.g.  customer_no (* Unique identifier *)
	public static final String HEADER_EXTRA_INFO_PATTERN = "\\s{0,10}\\(\\*[\\w\\d\\s]{0,30}\\*\\)\\s{0,10}";
	private static final Logger log = LoggerFactory.getLogger(PresetHandlerHelper.class);
	private static final String COMMA_DELIMITER = ",";
	private final PresetRepository presetRepository;

	private final FileUtilitiesService fileUtilitiesService;

	@Async
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void upload(Preset preset) {
		// handle preset
		InputStream inputStream = fileUtilitiesService.getObject(preset.getFilePath());
		int row = 0;
		try (CSVReader csvReader = new CSVReader(new InputStreamReader(inputStream))) {
			List<PresetRecord> records = new ArrayList<>();
			String[] values = null;
			while ((values = csvReader.readNext()) != null) {
				if (row == PresetRecord.ROW_HEADER) {
					for (int i = 0; i < values.length; i++) {
						// Replace the extra info in the header
						values[i] = values[i].replaceAll(HEADER_EXTRA_INFO_PATTERN, "");
					}
				}

				PresetRecord presetRecord = new PresetRecord();
				presetRecord.setTenantId(preset.getTenantId());
				presetRecord.setPresetId(preset.getId());
				presetRecord.setRow(row);
				presetRecord.setData(getRecordData(row, values));
				records.add(presetRecord);

				if (row % INSERT_BATCH_SIZE == 0) {
					insertRecords(records);
					records.clear();
				}
				row++;
			}
			insertRecords(records);
			preset.setTotalRecords(row - 1);
			preset.setStatus(ImportStatus.UPLOADED);
		} catch (IOException e) {
			preset.setStatus(ImportStatus.ERROR);
			log.error("Error while reading file", e);
		} catch (CsvValidationException e) {
			preset.setStatus(ImportStatus.ERROR);
			log.error("CSV format is invalid", e);
		} catch (Exception e) {
			preset.setStatus(ImportStatus.ERROR);
			log.error("Unexpected error during process CSV", e);
		} finally {
			presetRepository.update(preset);
		}
	}

	private void insertRecords(List<PresetRecord> records) {
		presetRepository.insertRecords(records);
	}

	private Map<Integer, String> getRecordData(int row, String[] values) {
		Map<Integer, String> data = new HashMap<>();
		for (int i = 0; i < values.length; i++) {
			data.put(i + 1, Optional.ofNullable(values[i])
					.map(String::trim)
					.filter(StringUtils::hasText)
					.orElse(""));
		}
		return data;
	}

}
