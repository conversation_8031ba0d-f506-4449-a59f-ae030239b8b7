/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.preset.menu.util;

import com.styl.pacific.domain.dto.TimezoneResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.utility.service.domain.exception.UtilityImportException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MenuPresetUtils {
	private static final Logger log = LoggerFactory.getLogger(MenuPresetUtils.class);

	public static Instant processStartDate(String startDate, TenantResponse tenant) {
		return Optional.ofNullable(startDate)
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> {
					try {
						return ZonedDateTime.of(LocalDate.from(DateTimeFormatter.ISO_LOCAL_DATE.parse(s)),
								LocalTime.MIN, Optional.ofNullable(tenant.getSettings())
										.map(TenantSettingsResponse::getTimeZone)
										.map(TimezoneResponse::getZoneId)
										.map(ZoneId::of)
										.orElseGet(() -> ZoneId.of(ZoneOffset.UTC.getId())))
								.toInstant();
					} catch (Exception e) {
						log.info("Invalid start date", e);
						throw new UtilityImportException("Invalid start date");
					}
				})
				.orElse(null);
	}

	public static Instant processEndDate(String endDate, TenantResponse tenant) {
		return Optional.ofNullable(endDate)
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> {
					try {
						return ZonedDateTime.of(LocalDate.from(DateTimeFormatter.ISO_LOCAL_DATE.parse(s)),
								LocalTime.MIN, Optional.ofNullable(tenant.getSettings())
										.map(TenantSettingsResponse::getTimeZone)
										.map(TimezoneResponse::getZoneId)
										.map(ZoneId::of)
										.orElseGet(() -> ZoneId.of(ZoneOffset.UTC.getId())))
								.toInstant();
					} catch (Exception e) {
						log.info("Invalid end date", e);
						throw new UtilityImportException("Invalid end date");
					}
				})
				.orElse(null);
	}

	public static LocalTime processStartTime(String time) {
		return Optional.ofNullable(time)
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> parseTime(s).orElseThrow(() -> new UtilityImportException("Invalid startTime")))
				.orElse(null);
	}

	public static LocalTime processEndTime(String time) {
		return Optional.ofNullable(time)
				.filter(StringUtils::isNotBlank)
				.map(String::trim)
				.map(s -> parseTime(s).orElseThrow(() -> new UtilityImportException("Invalid endTime")))
				.orElse(null);
	}

	public static Optional<LocalTime> parseTime(String timeString) {
		List<String> patterns = List.of("HH:mm", "HH:mm:ss");
		return detectAndParseTime(timeString, patterns);
	}

	private static Optional<LocalTime> detectAndParseTime(String timeString, List<String> patterns) {
		for (String pattern : patterns) {
			try {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
				LocalTime time = LocalTime.parse(timeString.toUpperCase(), formatter);
				System.out.println("Matched pattern: " + pattern);
				return Optional.of(time);
			} catch (DateTimeParseException ignored) {
			}
		}
		return Optional.empty();
	}
}
