/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.preset.customer;

import com.styl.pacific.utility.service.PresetTypeAssetHandler;
import com.styl.pacific.utility.service.PresetTypeAssetRepository;
import com.styl.pacific.utility.service.api.dto.PresetType;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@PresetTypeAssetHandler(type = PresetType.CUSTOMER)
@Repository
@RequiredArgsConstructor
public class CustomerPresetAssetRepository implements PresetTypeAssetRepository {
	private final ResourceLoader resourceLoader;
	private static final String ASSET_NAME = "customer-sample";

	@Override
	public List<String> findTemplateHeaders() {
		return List.of("customer_no", "first_name", "last_name", "email", "parent_email", "group_name", "allergens");
	}

	@Override
	public Optional<ByteBuffer> findAsset() {
		try {
			final var buffer = ByteBuffer.wrap(resourceLoader.getResource(String.format("classpath:assets/%s.csv",
					ASSET_NAME))
					.getInputStream()
					.readAllBytes());
			return Optional.of(buffer);
		} catch (Exception e) {
			return Optional.empty();
		}
	}
}
