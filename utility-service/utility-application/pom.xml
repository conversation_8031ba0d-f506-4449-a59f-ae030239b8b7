<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.styl.pacific.utility.service</groupId>
        <artifactId>utility-service</artifactId>
        <version>1.2.5</version>
    </parent>
    <artifactId>utility-application</artifactId>

    <properties>
        <docker.skip>true</docker.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific</groupId>
            <artifactId>common-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-data-access-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-data-access-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service</groupId>
            <artifactId>utility-scheduling</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.datasync</groupId>
            <artifactId>utility-datasync-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.datasync</groupId>
            <artifactId>utility-datasync-scheduling</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.import</groupId>
            <artifactId>utility-import-customer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.import</groupId>
            <artifactId>utility-import-menu</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.import</groupId>
            <artifactId>utility-import-pre-order-menu</artifactId>
        </dependency>
        <dependency>
            <groupId>com.styl.pacific.utility.service.import</groupId>
            <artifactId>utility-import-product</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>build-info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                    <execution>
                        <goals>
                            <goal>build-image</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <image>
                                <name>${project.groupId}:${project.version}</name>
                                <createdDate>${maven.build.timestamp}</createdDate>
                            </image>
                            <skip>${docker.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
