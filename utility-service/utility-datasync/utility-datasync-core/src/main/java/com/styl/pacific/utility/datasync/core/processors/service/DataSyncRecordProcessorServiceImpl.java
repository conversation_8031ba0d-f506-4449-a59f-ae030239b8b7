/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.context.AsyncRequestContextHolder;
import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.utility.data.access.client.AllergenClient;
import com.styl.pacific.utility.data.access.client.CustomerAlternativeAccessClient;
import com.styl.pacific.utility.data.access.client.CustomerCardClient;
import com.styl.pacific.utility.data.access.client.CustomerClient;
import com.styl.pacific.utility.data.access.client.CustomerGroupClient;
import com.styl.pacific.utility.data.access.client.CustomerSubAccountClient;
import com.styl.pacific.utility.data.access.client.UserAllergenClient;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.notifications.DataSyncJobNotificationService;
import com.styl.pacific.utility.datasync.core.processors.DataSyncRecordProcessorService;
import com.styl.pacific.utility.datasync.core.processors.cards.CardDataSyncRecordProcessor;
import com.styl.pacific.utility.datasync.core.processors.chains.DataSyncChainProcessorContext;
import com.styl.pacific.utility.datasync.core.processors.customers.CustomerDataSyncRecordProcessor;
import com.styl.pacific.utility.datasync.core.processors.groups.GroupDataSyncRecordProcessor;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordQueryService;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataSyncRecordProcessorServiceImpl implements DataSyncRecordProcessorService {
	private final Function<DataSyncChainProcessorContext, DataSyncChainProcessorContext> chainProcessor;
	private final DataSyncJobCommandService jobCommandService;
	private final DataSyncJobNotificationService notificationService;

	public DataSyncRecordProcessorServiceImpl(DataSyncJobCommandService jobCommandService,
			DataSyncRecordQueryService recordQueryService, DataSyncRecordCommandService recordCommandService,
			ObjectMapper mapper, CustomerGroupClient customerGroupClient, UserAllergenClient userAllergenClient,
			CustomerClient customerClient, CustomerCardClient customerCardClient, AllergenClient allergenClient,
			FileUtilitiesService fileUtilitiesService, CustomerSubAccountClient subAccountClient,
			DataSyncJobNotificationService notificationService,
			CustomerAlternativeAccessClient customerAlternativeAccessClient) {
		this.jobCommandService = jobCommandService;
		this.notificationService = notificationService;
		this.chainProcessor = new GroupDataSyncRecordProcessor(jobCommandService, recordQueryService,
				recordCommandService, mapper, customerClient, customerGroupClient).andThen(
						new CustomerDataSyncRecordProcessor(jobCommandService, recordQueryService, recordCommandService,
								mapper, customerGroupClient, customerClient, userAllergenClient, allergenClient,
								fileUtilitiesService, subAccountClient, customerAlternativeAccessClient))
				.andThen(new CardDataSyncRecordProcessor(jobCommandService, recordQueryService, recordCommandService,
						mapper, customerClient, customerCardClient));
	}

	@Override
	public void process(DataSyncJob job) {
		log.info("Started processing DataSyncJob - %s".formatted(job.getId()
				.getValue()));

		job = jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.IN_PROGRESS));

		try {
			final RequestContext requestContext = new RequestContext();
			requestContext.setTenantId(job.getTenantId()
					.getValue());
			AsyncRequestContextHolder.setContext(requestContext);

			final var result = chainProcessor.apply(DataSyncChainProcessorContext.builder()
					.job(job)
					.build());

			job = result.getJob();
			final var hasError = DataSyncJobStatus.ERROR.equals(job.getStatus());
			job = hasError
					? job
					: jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.COMPLETED)
							.withErrorMessage(null));
			log.info("Completed processing for DataSyncJob %s, status: %s".formatted(job.getId()
					.getValue(), job.getStatus()));

			if (hasError) {
				notificationService.notifyTenantOwner(job);
			}

		} catch (Exception exception) {
			log.warn(exception.getMessage(), exception);
			final var updatedJob = jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.ERROR)
					.withErrorMessage(job.appendErrorMessage(exception.getMessage())));
			notificationService.notifyTenantOwner(updatedJob);
			throw new DataSyncDomainException(exception.getMessage(), exception);
		} finally {
			AsyncRequestContextHolder.clearContext();
		}

	}
}
