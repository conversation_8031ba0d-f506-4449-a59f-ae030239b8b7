/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.cards;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.commons.models.CardDataModel;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.commons.models.RowValidationResult;
import com.styl.pacific.utility.datasync.core.commons.validator.CsvRowDataValidator;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.shared.schema.CardDataModelSchema;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class CardCsvRowDataValidator implements CsvRowDataValidator {
	private final ObjectMapper mapper;
	private final Validator validator;

	@Override
	public RowValidationResult validate(DataSyncJob job, CsvDataSchema csvSchema, String[] rowData) {
		final var cardDataModel = CardDataModel.builder()
				.customerNo(getColumnDataByKey(csvSchema, rowData, CardDataModelSchema.CUSTOMER_NO.getHeaderKey()))
				.email(getColumnDataByKey(csvSchema, rowData, CardDataModelSchema.CUSTOMER_EMAIL.getHeaderKey()))
				.cardId(getColumnDataByKey(csvSchema, rowData, CardDataModelSchema.CUSTOMER_CARD_ID.getHeaderKey()))
				.build();

		final var results = validator.validate(cardDataModel);

		var errorMessage = getErrorMessage(results);

		if (StringUtils.isBlank(cardDataModel.getCustomerNo()) && StringUtils.isBlank(cardDataModel.getEmail())) {
			final var error = "One of Email or Customer No must be provided";

			errorMessage = StringUtils.isBlank(errorMessage) ? error : "%s; %s".formatted(errorMessage, error);
		}

		return RowValidationResult.builder()
				.isValid(StringUtils.isBlank(errorMessage))
				.errorMessage(StringUtils.isNotBlank(errorMessage) ? errorMessage : null)
				.modelData(mapper.convertValue(cardDataModel, new TypeReference<>() {
				}))
				.build();
	}
}
