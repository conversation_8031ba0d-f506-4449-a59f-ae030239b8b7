/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.groups;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.commons.models.GroupDataModel;
import com.styl.pacific.utility.datasync.core.commons.models.RowValidationResult;
import com.styl.pacific.utility.datasync.core.commons.validator.CsvRowDataValidator;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.shared.schema.GroupDataModelSchema;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class GroupCsvRowDataValidator implements CsvRowDataValidator {
	private final ObjectMapper mapper;
	private final Validator validator;

	@Override
	public RowValidationResult validate(DataSyncJob job, CsvDataSchema csvSchema, String[] rowData) {
		final var groupDataModel = GroupDataModel.builder()
				.groupPath(getColumnDataByKey(csvSchema, rowData, GroupDataModelSchema.GROUP_PATH.getHeaderKey()))
				.groupDescription(getColumnDataByKey(csvSchema, rowData, GroupDataModelSchema.GROUP_DESCRIPTION
						.getHeaderKey()))
				.build();

		final var results = validator.validate(groupDataModel);

		final var errorMessage = getErrorMessage(results);

		return RowValidationResult.builder()
				.isValid(StringUtils.isBlank(errorMessage))
				.errorMessage(StringUtils.isNotBlank(errorMessage) ? errorMessage : null)
				.modelData(mapper.convertValue(groupDataModel, new TypeReference<>() {
				}))
				.build();
	}
}
