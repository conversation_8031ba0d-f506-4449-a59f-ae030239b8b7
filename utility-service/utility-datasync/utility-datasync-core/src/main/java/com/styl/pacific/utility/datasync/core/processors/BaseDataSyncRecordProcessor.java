/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors;

import com.styl.pacific.application.rest.exception.PacificFeignException;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.utility.data.access.client.CustomerClient;
import com.styl.pacific.utility.datasync.core.commons.constants.DataSyncConstants;
import com.styl.pacific.utility.datasync.core.commons.models.CustomerDataModel;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.processors.chains.DataSyncChainProcessorContext;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordQueryService;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.core.records.request.DataSyncRecordPaginationQuery;
import com.styl.pacific.utility.datasync.core.records.request.FilterDataSyncRecordQuery;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import com.styl.pacific.utils.fallback.FallbackFlow;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

@Slf4j
public abstract class BaseDataSyncRecordProcessor implements DataSyncRecordProcessor {
	protected final DataSyncRecordType recordType;
	protected final DataSyncRecordQueryService recordQueryService;
	protected final DataSyncRecordCommandService recordCommandService;
	protected final DataSyncJobCommandService jobCommandService;
	protected final CustomerClient customerClient;

	protected BaseDataSyncRecordProcessor(DataSyncRecordType recordType, DataSyncJobCommandService jobCommandService,
			DataSyncRecordQueryService recordQueryService, DataSyncRecordCommandService recordCommandService,
			CustomerClient customerClient) {
		this.recordType = recordType;
		this.jobCommandService = jobCommandService;
		this.recordQueryService = recordQueryService;
		this.recordCommandService = recordCommandService;
		this.customerClient = customerClient;
	}

	@Override
	public DataSyncChainProcessorContext apply(DataSyncChainProcessorContext context) {
		context = preProcess(context);
		int totalPages = -1;
		int pageIndex = 0;
		long totalRecords = 0;
		final var errorMessages = new ArrayList<String>();
		do {
			final var pages = recordQueryService.queryDataSyncRecords(DataSyncRecordPaginationQuery.builder()
					.filter(FilterDataSyncRecordQuery.builder()
							.byJobId(context.getJob()
									.getId())
							.byTenantId(context.getJob()
									.getTenantId())
							.byType(recordType)
							.byStatuses(Set.of(DataSyncRecordStatus.VALIDATED))
							.build())
					.page(0)
					.size(DataSyncConstants.BATCH_RECORD_MAX_SIZE)
					.sortDirection("ASC")
					.sortFields(List.of("recordIndex"))
					.build());

			if (totalPages == -1) {
				totalPages = pages.getTotalPages();
			}
			totalRecords = totalRecords + pages.getContent()
					.size();

			DataSyncChainProcessorContext finalContext = context;

			final var syncedRecords = recordCommandService.updateRecords(pages.getContent()
					.stream()
					.map(it -> it.withRecordStatus(DataSyncRecordStatus.IN_PROGRESS))
					.toList())
					.stream()
					.map(dataSyncRecord -> {
						try {
							syncRecord(finalContext, dataSyncRecord);
							return dataSyncRecord.withRecordStatus(DataSyncRecordStatus.COMPLETED);
						} catch (Exception exception) {
							var errorMessage = exception.getMessage();

							if (exception instanceof PacificFeignException pacificFeignException) {
								errorMessage = "%s - %s".formatted(errorMessage, Optional.ofNullable(
										pacificFeignException.getError()
												.getDetails())
										.map(it -> String.join("; ", it))
										.orElse(""))
										.trim();
							}

							log.error(errorMessage, exception);
							errorMessages.add(errorMessage);
							return dataSyncRecord.withRecordStatus(DataSyncRecordStatus.ERROR)
									.withErrorMessage(errorMessage)
									.withStacktrace(ExceptionUtils.getStackTrace(exception));
						}
					})
					.toList();

			recordCommandService.updateRecords(syncedRecords);
		} while (pageIndex++ < totalPages);

		context = postProcess(context, totalRecords);

		if (!errorMessages.isEmpty()) {
			throw new DataSyncDomainException(
					"DataSync process needs to be stopped due to error in syncing records at %s processor. Error messages: %s"
							.formatted(recordType, String.join(";", errorMessages)));
		}

		return context;
	}

	protected abstract void syncRecord(DataSyncChainProcessorContext context, DataSyncRecord dataSyncRecord);

	protected abstract DataSyncChainProcessorContext postProcess(DataSyncChainProcessorContext context,
			long totalRecords);

	protected abstract DataSyncChainProcessorContext preProcess(DataSyncChainProcessorContext context);

	protected Optional<UserResponse> getCustomerByEmailAndCustomerNo(String email, String customerNo) {
		final var customerFilter = FallbackFlow.<CustomerDataModel, QueryUserRequest>builder()
				.addFallBack(customerData -> StringUtils.isNotBlank(email), customerData -> QueryUserRequest.builder()
						.byEmail(customerData.getEmail()
								.trim())
						.build())
				.addFallBack(customerData -> StringUtils.isNotBlank(customerNo), customerData -> QueryUserRequest
						.builder()
						.byExternalId(customerData.getCustomerNo()
								.trim())
						.build())
				.execute(CustomerDataModel.builder()
						.email(email)
						.customerNo(customerNo)
						.build());

		final var existingCustomerPageResult = customerClient.queryCustomers(QueryUserPaginationRequest.builder()
				.filter(customerFilter)
				.page(0)
				.size(1)
				.build());

		return existingCustomerPageResult.getTotalElements() == 0
				? Optional.empty()
				: Optional.of(existingCustomerPageResult.getContent()
						.getFirst());
	}
}
