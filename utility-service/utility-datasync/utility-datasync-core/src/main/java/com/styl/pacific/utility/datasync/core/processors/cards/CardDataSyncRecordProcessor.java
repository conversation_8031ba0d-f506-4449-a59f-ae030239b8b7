/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors.cards;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.user.shared.http.cards.request.CreateCardRequest;
import com.styl.pacific.user.shared.http.cards.request.FilterUserCardRequest;
import com.styl.pacific.user.shared.http.cards.request.QueryUserCardPaginationRequest;
import com.styl.pacific.utility.data.access.client.CustomerCardClient;
import com.styl.pacific.utility.data.access.client.CustomerClient;
import com.styl.pacific.utility.datasync.core.commons.models.CardDataModel;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.processors.BaseDataSyncRecordProcessor;
import com.styl.pacific.utility.datasync.core.processors.chains.DataSyncChainProcessorContext;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordQueryService;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncJobNotFoundException;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CardDataSyncRecordProcessor extends BaseDataSyncRecordProcessor {

	private final ObjectMapper mapper;
	private final CustomerCardClient customerCardClient;

	public CardDataSyncRecordProcessor(DataSyncJobCommandService jobCommandService,
			DataSyncRecordQueryService recordQueryService, DataSyncRecordCommandService recordCommandService,
			ObjectMapper mapper, CustomerClient customerClient, CustomerCardClient customerCardClient) {
		super(DataSyncRecordType.CARD, jobCommandService, recordQueryService, recordCommandService, customerClient);
		this.mapper = mapper;
		this.customerCardClient = customerCardClient;
	}

	@Override
	protected DataSyncChainProcessorContext preProcess(DataSyncChainProcessorContext context) {
		final var job = jobCommandService.updateDataSyncJob(context.getJob()
				.withRecordTracking(context.getJob()
						.getRecordTracking()
						.withCardCsvLoadingStartedAt(Instant.now())));
		return context.withJob(job);
	}

	@Override
	protected DataSyncChainProcessorContext postProcess(DataSyncChainProcessorContext context, long totalRecords) {
		return context.withJob(jobCommandService.updateDataSyncJob(context.getJob()
				.withRecordTracking(context.getJob()
						.getRecordTracking()
						.withTotalCardRecords(totalRecords)
						.withCardCsvLoadingCompletedAt(Instant.now()))));
	}

	@Override
	protected void syncRecord(DataSyncChainProcessorContext context, DataSyncRecord dataSyncRecord) {
		final var cardDataModel = mapper.convertValue(dataSyncRecord.getData(), CardDataModel.class);

		final var existingCustomer = getCustomerByEmailAndCustomerNo(cardDataModel.getEmail(), cardDataModel
				.getCustomerNo()).orElseThrow(() -> new DataSyncJobNotFoundException("Customer has not found"));
		final var customerId = Long.valueOf(existingCustomer.getId());
		final var cardId = cardDataModel.getCardId()
				.trim();

		final var existingCardIds = customerCardClient.queryUserCards(customerId, QueryUserCardPaginationRequest
				.builder()
				.filter(FilterUserCardRequest.builder()
						.byCardId(cardId)
						.build())
				.page(0)
				.size(1)
				.build());

		if (existingCardIds.getTotalElements() != 0) {
			return;
		}

		customerCardClient.createUserCard(customerId, CreateCardRequest.builder()
				.cardId(cardId)
				.build());
	}

}
