/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.customers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.commons.models.CustomerDataModel;
import com.styl.pacific.utility.datasync.core.commons.models.RowValidationResult;
import com.styl.pacific.utility.datasync.core.commons.validator.CsvRowDataValidator;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.shared.schema.CustomerDataModelSchema;
import com.styl.pacific.utility.datasync.shared.schema.CustomerDataRecordAction;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utils.string.PathUtils;
import jakarta.validation.Validator;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class CustomerCsvRowDataValidator implements CsvRowDataValidator {
	private final ObjectMapper mapper;
	private final Validator validator;
	private final FileUtilitiesService fileUtilitiesService;

	private static final String ERROR_CONCAT_PATTERN = "%s; %s";

	@Override
	public RowValidationResult validate(DataSyncJob job, CsvDataSchema csvSchema, String[] rowData) {
		final var customerDataModel = CustomerDataModel.builder()
				.customerNo(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_NO.getHeaderKey()))
				.firstName(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_FIRST_NAME
						.getHeaderKey()))
				.lastName(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_LAST_NAME
						.getHeaderKey()))
				.email(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_EMAIL.getHeaderKey()))
				.groupName(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_GROUP_NAME
						.getHeaderKey()))
				.familyCode(getColumnDataByKey(csvSchema, rowData, CustomerDataModelSchema.CUSTOMER_FAMILY_CODE
						.getHeaderKey()))
				.allergens(Optional.ofNullable(getColumnDataByKey(csvSchema, rowData,
						CustomerDataModelSchema.CUSTOMER_ALLERGENS.getHeaderKey()))
						.map(it -> List.of(it.split(",")))
						.orElse(null))
				.action(Optional.ofNullable(getColumnDataByKey(csvSchema, rowData,
						CustomerDataModelSchema.CUSTOMER_ACTION.getHeaderKey()))
						.orElse(CustomerDataRecordAction.UPDATE.name()))
				.avatarImagePath(getColumnDataByKey(csvSchema, rowData,
						CustomerDataModelSchema.CUSTOMER_AVATAR_IMAGE_PATH.getHeaderKey()))
				.customerSponsorEmail(getColumnDataByKey(csvSchema, rowData,
						CustomerDataModelSchema.CUSTOMER_SPONSOR_EMAIL.getHeaderKey()))
				.build();

		final var results = validator.validate(customerDataModel);

		var errorMessage = getErrorMessage(results);

		if (StringUtils.isNotBlank(customerDataModel.getAvatarImagePath()) && !PathUtils.isValidAbsolutePath(
				customerDataModel.getAvatarImagePath())) {
			errorMessage = ERROR_CONCAT_PATTERN.formatted(errorMessage, "avatar path is not well format");
		}
		if (StringUtils.isNotBlank(customerDataModel.getAvatarImagePath()) && !fileUtilitiesService.exists("%s%s"
				.formatted(job.getArchivedFileDirectory(), customerDataModel.getAvatarImagePath()))) {
			errorMessage = ERROR_CONCAT_PATTERN.formatted(errorMessage, "avatar path is not existed");
		}

		if (StringUtils.isBlank(customerDataModel.getCustomerNo()) && StringUtils.isBlank(customerDataModel
				.getEmail())) {
			errorMessage = ERROR_CONCAT_PATTERN.formatted(errorMessage, "One of Email or Customer No must be provided");
		}

		return RowValidationResult.builder()
				.isValid(StringUtils.isBlank(errorMessage))
				.errorMessage(StringUtils.isNotBlank(errorMessage) ? errorMessage : null)
				.modelData(mapper.convertValue(customerDataModel, new TypeReference<>() {
				}))
				.build();
	}
}
