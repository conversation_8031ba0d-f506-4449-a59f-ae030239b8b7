/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.user.shared.http.alternativecustomers.request.CreateAlternativeCustomerRequest;
import com.styl.pacific.user.shared.http.users.request.AddCustomerRequest;
import com.styl.pacific.user.shared.http.users.request.UpdateUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserLiteResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.utility.datasync.core.commons.models.CustomerDataModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface DataSyncRecordProcessorMapper {
	DataSyncRecordProcessorMapper INSTANCE = Mappers.getMapper(DataSyncRecordProcessorMapper.class);

	@Mapping(target = "externalId", source = "customerDataModel.customerNo")
	@Mapping(target = "firstName", source = "customerDataModel.firstName")
	@Mapping(target = "lastName", source = "customerDataModel.lastName")
	@Mapping(target = "email", source = "customerDataModel.email")
	@Mapping(target = "userGroupId", source = "userGroupId")
	@Mapping(target = "phoneNumber", ignore = true)
	@Mapping(target = "migrationId", ignore = true)
	@Mapping(target = "familyCode", ignore = true)
	@Mapping(target = "avatarPath", source = "avatarFilePath")
	@Mapping(target = "avatarHash", source = "avatarHash")
	AddCustomerRequest toCreateCustomer(CustomerDataModel customerDataModel, Long userGroupId, String avatarFilePath,
			String avatarHash);

	@Mapping(target = "id", source = "existing.id")
	@Mapping(target = "externalId", source = "customerDataModel.customerNo")
	@Mapping(target = "firstName", source = "customerDataModel.firstName")
	@Mapping(target = "lastName", source = "customerDataModel.lastName")
	@Mapping(target = "email", source = "existing.email") // Update email being possible leading to un-sync with Keycloak
	@Mapping(target = "userGroupId", source = "userGroupId")
	@Mapping(target = "phoneNumber", source = "existing.phoneNumber")
	@Mapping(target = "avatarPath", source = "avatarFilePath")
	@Mapping(target = "avatarHash", source = "avatarHash")
	@Mapping(target = "updatePermissions", source = "existing.permissions")
	UpdateUserRequest toUpdateCustomer(UserResponse existing, CustomerDataModel customerDataModel, Long userGroupId,
			String avatarFilePath, String avatarHash);

	@Mapping(target = "phoneNumber", ignore = true)
	@Mapping(target = "migrationId", ignore = true)
	@Mapping(target = "avatarHash", ignore = true)
	@Mapping(target = "externalId", source = "customerDataModel.customerNo")
	CreateAlternativeCustomerRequest toCreateCustomerAlternativeAccess(CustomerDataModel customerDataModel,
			Long userGroupId, String avatarPath);

	@Mapping(target = "permissions", ignore = true)
	@Mapping(target = "uniqueExternalId", ignore = true)
	@Mapping(target = "totalSubAccounts", ignore = true)
	@Mapping(target = "totalSponsors", ignore = true)
	UserResponse toUserResponseByAlternativeUserLiteResponse(UserLiteResponse alternativeUser);
}
