/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders;

import static com.styl.pacific.utils.json.JsonFormatUtil.convertToString;

import com.opencsv.CSVReader;
import com.styl.pacific.utility.datasync.core.commons.constants.DataSyncConstants;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.commons.models.ProcessedCsvResult;
import com.styl.pacific.utility.datasync.core.commons.validator.CsvRowDataValidator;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.core.records.request.CreateDataSyncRecordCommand;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseDataSyncCsvLoader {

	protected final DataSyncRecordType dataSyncRecordType;

	protected final DataSyncJobCommandService jobCommandService;
	protected final DataSyncRecordCommandService recordCommandService;
	protected final FileUtilitiesService fileUtilitiesService;
	protected final CsvRowDataValidator rowDataValidator;

	protected CsvDataSchema buildCsvDataSchemaByHeader(String[] actualHeaders, CsvDataSchema defaultSchema) {
		if (actualHeaders == null || actualHeaders.length == 0) {
			throw new DataSyncDomainException("CSV Header values is required instead of empty.");
		}
		final var headerValues = List.of(actualHeaders);
		final var columnDefinitions = defaultSchema.getColumDefinitions()
				.stream()
				.filter(it -> headerValues.contains(it.getKey()))
				.map(it -> it.withIndex(headerValues.indexOf(it.getKey())))
				.toList();

		if (columnDefinitions.size() != defaultSchema.getColumDefinitions()
				.size()) {
			throw new DataSyncDomainException("CSV Header was not well format. Expected: %s. Actual: %s".formatted(
					defaultSchema.getColumDefinitions()
							.stream()
							.map(CsvDataSchema.ColumDefinition::getKey)
							.collect(Collectors.joining(",")), Arrays.toString(actualHeaders)));
		}
		return defaultSchema.withColumDefinitions(columnDefinitions);
	}

	protected abstract CsvDataSchema getDefaultSchema();

	protected ProcessedCsvResult processCsv(DataSyncJob job, InputStream inputStream) {
		try (CSVReader csvReader = new CSVReader(new InputStreamReader(inputStream))) {
			String[] rowData = null;
			CsvDataSchema csvSchema = null;
			long totalRecords = 0;
			long totalErrorRecords = 0;
			final var defaultSchema = getDefaultSchema();
			final var records = new ArrayList<CreateDataSyncRecordCommand>();

			while ((rowData = csvReader.readNext()) != null) {
				if (defaultSchema.getHeaderRowIndex() == csvReader.getRecordsRead()) {
					csvSchema = buildCsvDataSchemaByHeader(rowData, defaultSchema);
					job = jobCommandService.updateDataSyncJob(updateCsvSchema(job, csvSchema));
					continue;
				}
				if (csvSchema == null) {
					continue;
				}
				final var rowValidationResult = rowDataValidator.validateRow(job, csvSchema, rowData);

				records.add(CreateDataSyncRecordCommand.builder()
						.jobId(job.getId())
						.tenantId(job.getTenantId())
						.recordIndex(csvReader.getRecordsRead())
						.recordType(dataSyncRecordType)
						.recordStatus(rowValidationResult.isValid()
								? DataSyncRecordStatus.VALIDATED
								: DataSyncRecordStatus.ERROR)
						.errorMessage(rowValidationResult.getErrorMessage())
						.data(rowValidationResult.getModelData())
						.rawData(convertToString(rowData))
						.build());
				if (records.size() >= DataSyncConstants.BATCH_RECORD_MAX_SIZE) {
					recordCommandService.createDataSyncRecords(new ArrayList<>(records));
					totalRecords = totalRecords + records.size();
					totalErrorRecords = totalErrorRecords + records.stream()
							.filter(it -> DataSyncRecordStatus.ERROR.equals(it.getRecordStatus()))
							.toList()
							.size();
					records.clear();
				}
			}

			if (!records.isEmpty()) {
				recordCommandService.createDataSyncRecords(new ArrayList<>(records));
				totalRecords = totalRecords + records.size();
				totalErrorRecords = totalErrorRecords + records.stream()
						.filter(it -> DataSyncRecordStatus.ERROR.equals(it.getRecordStatus()))
						.toList()
						.size();
			}

			return ProcessedCsvResult.builder()
					.job(job)
					.totalRecords(totalRecords)
					.totalErrorRecords(totalErrorRecords)
					.build();
		} catch (Exception exception) {
			throw new DataSyncDomainException(exception.getMessage(), exception);
		}
	}

	protected abstract DataSyncJob updateCsvSchema(DataSyncJob job, CsvDataSchema csvSchema);

	protected void deleteRecordsIfExisted(DataSyncJob updatedJob) {
		recordCommandService.deleteRecords(updatedJob.getTenantId(), updatedJob.getId(), dataSyncRecordType);
	}
}
