/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.loaders.DataSyncLoaderService;
import com.styl.pacific.utility.datasync.core.loaders.cards.CardCsvDataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.loaders.chains.DataSyncChainLoaderData;
import com.styl.pacific.utility.datasync.core.loaders.customers.CustomerCsvDataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.loaders.groups.GroupCsvDataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.loaders.manifest.ManifestJsonDataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.notifications.DataSyncJobNotificationService;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import jakarta.validation.Validator;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataSyncLoaderServiceImpl implements DataSyncLoaderService {
	private final Function<DataSyncChainLoaderData, DataSyncChainLoaderData> csvChainLoader;
	private final DataSyncJobCommandService jobCommandService;
	private final DataSyncJobNotificationService notificationService;

	public DataSyncLoaderServiceImpl(DataSyncJobCommandService jobCommandService,
			DataSyncRecordCommandService recordCommandService, FileUtilitiesService fileUtilitiesService,
			ObjectMapper objectMapper, Validator validator, DataSyncJobNotificationService notificationService) {
		this.jobCommandService = jobCommandService;
		this.notificationService = notificationService;
		this.csvChainLoader = new ManifestJsonDataSyncChainLoader(jobCommandService, fileUtilitiesService, objectMapper)
				.andThen(new GroupCsvDataSyncChainLoader(jobCommandService, recordCommandService, fileUtilitiesService,
						objectMapper, validator))
				.andThen(new CustomerCsvDataSyncChainLoader(jobCommandService, recordCommandService,
						fileUtilitiesService, objectMapper, validator))
				.andThen(new CardCsvDataSyncChainLoader(jobCommandService, recordCommandService, fileUtilitiesService,
						objectMapper, validator));
	}

	@Override
	public void loadRecords(DataSyncJob job) {
		log.info("Started loading DataSyncJob - %s".formatted(job.getId()
				.getValue()));

		job = jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.LOADING));

		try {
			final var result = csvChainLoader.apply(DataSyncChainLoaderData.builder()
					.job(job)
					.build());

			job = result.getJob();
			var hasError = DataSyncJobStatus.ERROR.equals(job.getStatus());
			job = hasError
					? job
					: jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.LOADED)
							.withErrorMessage(null));
			log.info("Completed loading for DataSyncJob %s, status: %s message: %s".formatted(job.getId()
					.getValue(), job.getStatus(), job.getErrorMessage()));

			if (hasError) {
				notificationService.notifyTenantOwner(job);
			}

		} catch (Exception exception) {
			log.warn(exception.getMessage(), exception);
			final var updatedJob = jobCommandService.updateDataSyncJob(job.withStatus(DataSyncJobStatus.ERROR)
					.withErrorMessage(job.appendErrorMessage(exception.getMessage())));
			notificationService.notifyTenantOwner(updatedJob);
			throw new DataSyncDomainException(exception.getMessage(), exception);
		}

	}
}
