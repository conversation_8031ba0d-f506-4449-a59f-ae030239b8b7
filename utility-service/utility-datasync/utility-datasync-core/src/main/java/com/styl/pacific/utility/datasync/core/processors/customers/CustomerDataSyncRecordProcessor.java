/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.processors.customers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.catalog.service.shared.http.allergen.request.AllergenFilterRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.request.AllergenQueryRequest;
import com.styl.pacific.catalog.service.shared.http.allergen.request.CreateAllergenRequest;
import com.styl.pacific.domain.dto.FileResponse;
import com.styl.pacific.user.shared.http.allergens.request.SaveUserAllergenRequest;
import com.styl.pacific.user.shared.http.allergens.request.UserAllergenItemRequest;
import com.styl.pacific.user.shared.http.customers.request.NotificationControlRequest;
import com.styl.pacific.user.shared.http.groups.request.CreateUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.FilterUserGroupRequest;
import com.styl.pacific.user.shared.http.groups.request.QueryUserGroupPaginationRequest;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import com.styl.pacific.user.shared.http.subaccounts.request.CreateSubAccountExistingUserRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.FilterSubAccountRequest;
import com.styl.pacific.user.shared.http.subaccounts.request.QuerySubAccountPaginationRequest;
import com.styl.pacific.user.shared.http.subaccounts.response.SubAccountResponse;
import com.styl.pacific.user.shared.http.users.request.DeleteUserRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserPaginationRequest;
import com.styl.pacific.user.shared.http.users.request.QueryUserRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.utility.data.access.client.AllergenClient;
import com.styl.pacific.utility.data.access.client.CustomerAlternativeAccessClient;
import com.styl.pacific.utility.data.access.client.CustomerClient;
import com.styl.pacific.utility.data.access.client.CustomerGroupClient;
import com.styl.pacific.utility.data.access.client.CustomerSubAccountClient;
import com.styl.pacific.utility.data.access.client.UserAllergenClient;
import com.styl.pacific.utility.data.access.utils.PaginationRestUtils;
import com.styl.pacific.utility.datasync.core.commons.models.CustomerDataModel;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.processors.BaseDataSyncRecordProcessor;
import com.styl.pacific.utility.datasync.core.processors.chains.DataSyncChainProcessorContext;
import com.styl.pacific.utility.datasync.core.processors.mapper.DataSyncRecordProcessorMapper;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordQueryService;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.datasync.shared.schema.CustomerDataRecordAction;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import com.styl.pacific.utility.service.domain.file.FileType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Slf4j
public class CustomerDataSyncRecordProcessor extends BaseDataSyncRecordProcessor {

	private final ObjectMapper mapper;
	private final CustomerGroupClient customerGroupClient;
	private final AllergenClient allergenClient;
	private final FileUtilitiesService fileUtilitiesService;
	private final UserAllergenClient userAllergenClient;
	private final CustomerSubAccountClient subAccountClient;
	private final CustomerAlternativeAccessClient customerAlternativeAccessClient;

	public CustomerDataSyncRecordProcessor(DataSyncJobCommandService jobCommandService,
			DataSyncRecordQueryService recordQueryService, DataSyncRecordCommandService recordCommandService,
			ObjectMapper mapper, CustomerGroupClient customerGroupClient, CustomerClient customerClient,
			UserAllergenClient userAllergenClient, AllergenClient allergenClient,
			FileUtilitiesService fileUtilitiesService, CustomerSubAccountClient subAccountClient,
			CustomerAlternativeAccessClient customerAlternativeAccessClient) {
		super(DataSyncRecordType.CUSTOMER, jobCommandService, recordQueryService, recordCommandService, customerClient);
		this.mapper = mapper;
		this.customerGroupClient = customerGroupClient;
		this.allergenClient = allergenClient;
		this.fileUtilitiesService = fileUtilitiesService;
		this.userAllergenClient = userAllergenClient;
		this.subAccountClient = subAccountClient;
		this.customerAlternativeAccessClient = customerAlternativeAccessClient;
	}

	@Override
	protected DataSyncChainProcessorContext preProcess(DataSyncChainProcessorContext context) {
		return context.withGroupData(PaginationRestUtils.getAllPaginatedData(customerGroupClient::queryUserGroups,
				FilterUserGroupRequest.builder()
						.build(), QueryUserGroupPaginationRequest.class))
				.withAllergenData(allergenClient.findAll(new AllergenQueryRequest(new AllergenFilterRequest(null, null,
						null), null, null))
						.getContent())
				.withJob(jobCommandService.updateDataSyncJob(context.getJob()
						.withRecordTracking(context.getJob()
								.getRecordTracking()
								.withCustomerCsvLoadingStartedAt(Instant.now()))));
	}

	@Override
	protected DataSyncChainProcessorContext postProcess(DataSyncChainProcessorContext context, long totalRecords) {
		return context.withJob(jobCommandService.updateDataSyncJob(context.getJob()
				.withRecordTracking(context.getJob()
						.getRecordTracking()
						.withTotalCustomerRecords(totalRecords)
						.withCustomerCsvLoadingCompletedAt(Instant.now()))));
	}

	@Override
	protected void syncRecord(DataSyncChainProcessorContext context, DataSyncRecord dataSyncRecord) {
		final var job = context.getJob();

		final var customerDataModel = mapper.convertValue(dataSyncRecord.getData(), CustomerDataModel.class);
		final var action = CustomerDataRecordAction.parse(customerDataModel.getAction());

		final var existingCustomerOptional = getCustomerByEmailAndCustomerNo(customerDataModel.getEmail(),
				customerDataModel.getCustomerNo());
		final var isUserExisted = existingCustomerOptional.isPresent();

		if (CustomerDataRecordAction.UPDATE.equals(action)) {
			final var group = StringUtils.isNotBlank(customerDataModel.getGroupName())
					? context.getGroupData()
							.stream()
							.filter(it -> it.getGroupName()
									.equalsIgnoreCase(customerDataModel.getGroupName()))
							.findFirst()
							.orElseGet(() -> {
								final var newGroup = customerGroupClient.createUserGroup(CreateUserGroupRequest
										.builder()
										.groupName(customerDataModel.getGroupName())
										.build());
								context.getGroupData()
										.add(newGroup);
								return newGroup;
							})
					: null;

			UserResponse updatedUser = null;

			if (isUserExisted) {
				final var existingUser = existingCustomerOptional.get();
				final var hasChangedAvatarImage = customerDataModel.getAvatarImagePath() != null
						&& hasChangedAvatarImage(existingUser, "%s%s".formatted(job.getArchivedFileDirectory(),
								customerDataModel.getAvatarImagePath()));

				final var avatarPath = hasChangedAvatarImage
						? copyAvatarFile("%s%s".formatted(job.getArchivedFileDirectory(), customerDataModel
								.getAvatarImagePath()))
						: Optional.ofNullable(existingUser.getAvatar())
								.map(FileResponse::path)
								.orElse(null);

				final var avatarHashValue = hasChangedAvatarImage
						? getFileHashValue(avatarPath)
						: existingUser.getAvatarHash();

				updatedUser = customerClient.updateCustomer(DataSyncRecordProcessorMapper.INSTANCE.toUpdateCustomer(
						existingCustomerOptional.get(), customerDataModel, group != null
								? Long.valueOf(group.getId())
								: null, avatarPath, avatarHashValue));
			} else {
				final var avatarPath = customerDataModel.getAvatarImagePath() != null
						? copyAvatarFile("%s%s".formatted(job.getArchivedFileDirectory(), customerDataModel
								.getAvatarImagePath()))
						: null;
				final var avatarHashValue = avatarPath != null ? getFileHashValue(avatarPath) : null;
				final var familyCustomers = customerClient.queryCustomers(QueryUserPaginationRequest.builder()
						.filter(QueryUserRequest.builder()
								.byFamilyCode(customerDataModel.getFamilyCode())
								.isAlternativeUser(Boolean.FALSE)
								.build())
						.page(0)
						.size(100)
						.build())
						.getContent();

				/*
				 * Find customer by 2 cases:
				 * 1. Main customer has sub-accounts
				 * 2. Main customer has no sub-accounts, is new one
				 * The list familyCustomers may include main customer and sub-accounts customer (parent-child)
				 */
				final var mainCustomer = familyCustomers.stream()
						.filter(it -> Objects.nonNull(it.getTotalSubAccounts()))
						.filter(it -> it.getTotalSubAccounts() > 0)
						.findFirst()
						.orElseGet(() -> familyCustomers.stream()
								.min(Comparator.comparing(user -> Long.valueOf(user.getId())))
								.orElse(null));

				final var isAlternativeUser = StringUtils.isNotBlank(customerDataModel.getCustomerSponsorEmail())
						&& familyCustomers.stream()
								.filter(it -> Boolean.FALSE.equals(it.getIsAlternativeUser()))
								.findAny()
								.isEmpty() && mainCustomer != null;

				updatedUser = isAlternativeUser
						? DataSyncRecordProcessorMapper.INSTANCE.toUserResponseByAlternativeUserLiteResponse(
								customerAlternativeAccessClient.createAlternativeCustomerAccess(Long.valueOf(
										mainCustomer.getId()), DataSyncRecordProcessorMapper.INSTANCE
												.toCreateCustomerAlternativeAccess(customerDataModel, Optional
														.ofNullable(group)
														.map(UserGroupResponse::getId)
														.map(Long::valueOf)
														.orElse(null), avatarPath), NotificationControlRequest.builder()
																.isSkipNotification(true)
																.build())
										.getAlternativeUser())
						: customerClient.createCustomer(DataSyncRecordProcessorMapper.INSTANCE.toCreateCustomer(
								customerDataModel, Optional.ofNullable(group)
										.map(UserGroupResponse::getId)
										.map(Long::valueOf)
										.orElse(null), avatarPath, avatarHashValue), NotificationControlRequest
												.builder()
												.isSkipNotification(true)
												.build());
			}

			syncAllergens(context, updatedUser, customerDataModel);
			syncSubAccount(updatedUser, customerDataModel);

		} else {
			if (isUserExisted) {
				customerClient.deleteCustomersInSchedule(DeleteUserRequest.builder()
						.byUserId(Long.valueOf(existingCustomerOptional.get()
								.getId()))
						.build());
			}
		}

	}

	private void syncSubAccount(UserResponse subUser, CustomerDataModel customerDataModel) {
		if (StringUtils.isBlank(customerDataModel.getCustomerSponsorEmail())) {
			return;
		}

		if (StringUtils.isNotBlank(customerDataModel.getCustomerSponsorEmail()) && customerDataModel
				.getCustomerSponsorEmail()
				.trim()
				.equalsIgnoreCase(subUser.getEmail())) {
			return;
		}

		final var sponsorUserPageResult = subAccountClient.querySubAccounts(Long.valueOf(subUser.getId()),
				QuerySubAccountPaginationRequest.builder()
						.filter(FilterSubAccountRequest.builder()
								.isSubUserFilter(true)
								.build())
						.page(0)
						.size(1)
						.build());
		final var isAlreadyLinkedSponsor = !sponsorUserPageResult.getContent()
				.isEmpty();

		if (isAlreadyLinkedSponsor) {
			return;
		}

		final var sponsorUser = getCustomerByEmailAndCustomerNo(customerDataModel.getCustomerSponsorEmail(), null)
				.orElseThrow(() -> new DataSyncDomainException("Not found sponsor customer email %s".formatted(
						customerDataModel.getCustomerSponsorEmail()
								.trim())));

		final var isAlreadyLinkedByName = subAccountClient.querySubAccounts(Long.valueOf(subUser.getId()),
				QuerySubAccountPaginationRequest.builder()
						.page(0)
						.size(100)
						.build())
				.getContent()
				.stream()
				.map(SubAccountResponse::getSubUser)
				.anyMatch(it -> subUser.getFirstName()
						.equalsIgnoreCase(it.getFirstName()) && subUser.getLastName()
								.equalsIgnoreCase(it.getLastName()));

		if (isAlreadyLinkedByName) {
			return;
		}

		subAccountClient.inviteExistingCustomer(CreateSubAccountExistingUserRequest.builder()
				.userId(Long.valueOf(subUser.getId()))
				.sponsorUserId(Long.valueOf(sponsorUser.getId()))
				.build());
	}

	private void syncAllergens(DataSyncChainProcessorContext context, UserResponse updatedUser,
			CustomerDataModel customerDataModel) {
		final var userAllergens = Optional.ofNullable(customerDataModel.getAllergens())
				.map(list -> list.stream()
						.map(String::trim)
						.filter(StringUtils::isNotBlank)
						.toList())
				.orElseGet(List::of);

		if (!CollectionUtils.isEmpty(userAllergens)) {
			final var allergenResponses = userAllergens.stream()
					.map(allergenName -> context.getAllergenData()
							.stream()
							.filter(it -> it.name()
									.equalsIgnoreCase(allergenName))
							.findFirst()
							.orElseGet(() -> {
								final var newAllergen = allergenClient.create(CreateAllergenRequest.builder()
										.name(allergenName)
										.build());
								context.getAllergenData()
										.add(newAllergen);
								return newAllergen;

							}))
					.toList();

			userAllergenClient.saveUserAllergens(Long.valueOf(updatedUser.getId()), new SaveUserAllergenRequest(
					allergenResponses.stream()
							.map(it -> new UserAllergenItemRequest(null, it.id(), true))
							.toList()));
		}

	}

	private String getFileHashValue(String avatarPath) {
		return fileUtilitiesService.getHeadObject(avatarPath)
				.eTag()
				.replace("\"", "");
	}

	private boolean hasChangedAvatarImage(UserResponse existingUser, String newPath) {
		final var newAvatarImageHash = getFileHashValue(newPath);
		return !newAvatarImageHash.equalsIgnoreCase(existingUser.getAvatarHash());
	}

	private String copyAvatarFile(String imagePath) {
		final var imageName = FilenameUtils.getName(imagePath);
		final var newImagePath = fileUtilitiesService.getUploadFilePath(FileType.IMAGE, imageName);
		return fileUtilitiesService.copyFile(imagePath, newImagePath.filePath());
	}
}
