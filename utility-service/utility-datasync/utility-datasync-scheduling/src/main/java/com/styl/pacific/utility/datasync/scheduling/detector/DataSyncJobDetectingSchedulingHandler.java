/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.scheduling.detector;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.utility.datasync.core.commons.constants.DataSyncConstants;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobQueryService;
import com.styl.pacific.utility.datasync.core.jobs.request.FilterDataSyncJobQuery;
import com.styl.pacific.utility.datasync.core.notifications.DataSyncJobNotificationService;
import com.styl.pacific.utility.datasync.scheduling.detector.config.JobDetectorProperties;
import com.styl.pacific.utility.datasync.scheduling.detector.mapper.DataSyncJobDetectorMapper;
import com.styl.pacific.utility.datasync.scheduling.detector.models.QueueObjectMessage;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncJobAlreadyExistingException;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;

@Service
@Slf4j
@RequiredArgsConstructor
public class DataSyncJobDetectingSchedulingHandler {
	private final JobDetectorProperties config;
	private final DataSyncJobCommandService commandService;
	private final DataSyncJobQueryService queryService;
	private final ObjectMapper objectMapper;
	private final S3Client s3Client;
	private final DataSyncJobNotificationService notificationService;

	public void scanS3Bucket() {
		s3Client.listObjectsV2(ListObjectsV2Request.builder()
				.bucket(config.getS3Bucket())
				.prefix(config.getS3QueuingPrefix())
				.maxKeys(200)
				.build())
				.contents()
				.stream()
				.filter(s3Object -> FilenameUtils.isExtension(s3Object.key(), "json"))
				.forEach(s3Object -> {
					final var queueS3Object = s3Client.getObject(GetObjectRequest.builder()
							.bucket(config.getS3Bucket())
							.key(s3Object.key())
							.build());
					final var queueObjectMessage = parseToQueueObjectMessage(queueS3Object);
					try {
						var command = DataSyncJobDetectorMapper.INSTANCE.toCommand(queueObjectMessage
								.withArchivedFileDirectory(DataSyncConstants.S3_BUCKET_PATH_PATTERN.formatted(config
										.getS3Bucket(), queueObjectMessage.getArchivedFileDirectory()))
								.withArchivedZipFilePath(DataSyncConstants.S3_BUCKET_PATH_PATTERN.formatted(config
										.getS3Bucket(), queueObjectMessage.getArchivedZipFilePath()))
								.withArchivedChecksumFilePath(DataSyncConstants.S3_BUCKET_PATH_PATTERN.formatted(config
										.getS3Bucket(), queueObjectMessage.getArchivedChecksumFilePath())));

						if (queryService.existsJob(FilterDataSyncJobQuery.builder()
								.byTenantId(MapstructCommonDomainMapper.INSTANCE.stringToTenantId(queueObjectMessage
										.getTenantId()))
								.byArchivedZipFileName(command.getArchivedZipFileName())
								.build())) {
							command = command.withStatus(DataSyncJobStatus.ERROR)
									.withErrorMessage(StringUtils.isNotBlank(command.getErrorMessage())
											? "%s%s".formatted(command.getErrorMessage(),
													"Zip file has been existed. Will be skipped.")
											: "Zip file has been existed. Will be skipped.");
						}

						final var job = commandService.createDataSyncJob(command);
						log.info("A New Job %s has been created for tenant %s".formatted(job.getId()
								.getValue(), job.getTenantId()
										.getValue()));
						if (DataSyncJobStatus.ERROR.equals(job.getStatus())) {
							notificationService.notifyTenantOwner(job);
						}
					} catch (DataSyncJobAlreadyExistingException exception) {
						log.warn("Job [%s] has been scheduled. So will skip.".formatted(queueObjectMessage.getJobId()));
						return;
					}

					// Delete queue json file
					s3Client.deleteObject(DeleteObjectRequest.builder()
							.bucket(config.getS3Bucket())
							.key(s3Object.key())
							.build());
				});

	}

	private QueueObjectMessage parseToQueueObjectMessage(ResponseInputStream<GetObjectResponse> queueS3Object) {
		try {
			return objectMapper.readValue(queueS3Object.readAllBytes(), QueueObjectMessage.class);
		} catch (IOException e) {
			throw new DataSyncDomainException("Cannot parse to queuing object message", e);
		}
	}
}
