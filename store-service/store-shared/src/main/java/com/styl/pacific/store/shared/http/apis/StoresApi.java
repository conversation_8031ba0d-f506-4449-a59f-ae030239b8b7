/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.store.shared.http.requests.store.CreateStoreRequest;
import com.styl.pacific.store.shared.http.requests.store.FindStoresRequest;
import com.styl.pacific.store.shared.http.requests.store.SuspendStoreRequest;
import com.styl.pacific.store.shared.http.requests.store.UpdateStoreRequest;
import com.styl.pacific.store.shared.http.responses.store.ListStoresResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface StoresApi {

	@PostMapping(path = "/api/store/stores")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.STORE_MGMT_ADD)
	ResponseEntity<StoreResponse> createStore(@RequestBody @Valid CreateStoreRequest request);

	@PutMapping(path = "/api/store/stores/{id}")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.STORE_MGMT_UPDATE)
	ResponseEntity<StoreResponse> updateStore(@PathVariable long id, @RequestBody @Valid UpdateStoreRequest request);

	@GetMapping(path = "/api/store/stores/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ResponseEntity<StoreResponse> getStore(@PathVariable long id);

	@DeleteMapping(path = "/api/store/stores/{id}")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.STORE_MGMT_DELETE)
	ResponseEntity<Void> deleteStore(@PathVariable long id);

	@GetMapping(path = "/api/store/stores")
	@PacificApiAuthorized
	ResponseEntity<ListStoresResponse> findStores(@SpringQueryMap @ModelAttribute @Valid FindStoresRequest request);

	@PutMapping(path = "/api/store/stores/{id}/suspend")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.STORE_MGMT_UPDATE)

	ResponseEntity<StoreResponse> suspendStore(@PathVariable long id, @RequestBody @Valid SuspendStoreRequest request);

	@PutMapping(path = "/api/store/stores/{id}/active")
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.STORE_MGMT_UPDATE)
	ResponseEntity<StoreResponse> activeStore(@PathVariable long id);
}
