/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.rest.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.store.service.config.MvcTestConfiguration;
import com.styl.pacific.store.service.domain.dto.staff.CreateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.FindStaffsQuery;
import com.styl.pacific.store.service.domain.dto.staff.StaffsFilterQuery;
import com.styl.pacific.store.service.domain.port.input.service.StaffAssignmentDomainService;
import com.styl.pacific.store.service.domain.port.input.service.StaffDomainService;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import com.styl.pacific.store.shared.http.requests.staff.CreateStaffRequest;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import com.styl.pacific.store.shared.http.requests.staff.ResetPinCodeRequest;
import com.styl.pacific.store.shared.http.requests.staff.UpdateStaffRequest;
import com.styl.pacific.store.shared.http.responses.staff.ListStaffResponse;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 */

@WebMvcTest(controllers = StaffController.class)
@Import({ StaffController.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class StaffControllerTest {

	private static final String STAFF_PATH = "/api/store/staffs";

	private static final Long TENANT_ID = 1L;

	private static final Long STAFF_ID = 1L;

	private static final String REQUEST_ID_HEADER = "X-Request-ID";

	private static final String TENANT_ID_HEADER = "X-Tenant-ID";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private StaffDomainService staffDomainService;

	@MockitoBean
	private StaffAssignmentDomainService staffAssignmentDomainService;

	@Test
	void shouldSuccess_whenCreateStaff() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("123")
				.cardId("11111111")
				.name("staff123")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.storeIds(List.of("1"))
				.build();

		StaffResponse staffResponse = StaffResponse.builder()
				.staffId(STAFF_ID)
				.tenantId(TENANT_ID)
				.staffCode("xyz123")
				.name("staffName")
				.cardId("1111111")
				.status(StaffStatus.ACTIVE)
				.type(StaffType.CASHIER)
				.build();
		when(staffDomainService.createStaff(any(CreateStaffCommand.class))).thenReturn(staffResponse);
		// Act && Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(staffResponse)));
	}

	@Test
	void testCreateStaffWhenNameEmptyThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("xyz123")
				.cardId("11111111")
				.name("")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenValidNumericStaffCodeShouldOk() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("xyz123")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk());
	}

	@Test
	void testCreateStaffWhenStaffCodeWithDashBetweenShouldOk() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("xyz-1mn123")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk());
	}

	@Test
	void testCreateStaffWhenInvalidNumericStaffCodeThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("xyz_123")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenStaffCodeWithDashEndingThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("xyz-")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenStaffCodeWithDashFirstThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("-xyz")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenPinCodeEmptyThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("1234")
				.cardId("11111111")
				.name("xyz")
				.pinCode("")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenStaffCodeEmptyThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("")
				.cardId("11111111")
				.name("xyz")
				.pinCode("12345")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenInvalidNumericPinCodeThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("123")
				.cardId("11111111")
				.name("xyz")
				.pinCode("xyz123")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testCreateStaffWhenInvalidAlphanumericCardIdThenThrowBadRequest() throws Exception {
		// Arrange
		CreateStaffRequest createStaffRequest = CreateStaffRequest.builder()
				.staffCode("123")
				.cardId("&xy$z1111")
				.name("xyz")
				.pinCode("xyz123")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(post(STAFF_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStaffRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testUpdateStaffWhenNameEmptyThenThrowBadRequest() throws Exception {
		// Arrange
		UpdateStaffRequest updateStoreRequest = UpdateStaffRequest.builder()
				.name("")
				.cardId("1234")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(updateStoreRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testUpdateStaffWhenTypeNullThenThrowBadRequest() throws Exception {
		// Arrange
		UpdateStaffRequest updateStoreRequest = UpdateStaffRequest.builder()
				.name("xyz123")
				.cardId("1234")
				.type(null)
				.build();

		// Act & Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(updateStoreRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void testUpdateStaffWhenInvalidAlphanumericCardIdThenThrowBadRequest() throws Exception {
		// Arrange
		UpdateStaffRequest updateStoreRequest = UpdateStaffRequest.builder()
				.name("xyz123")
				.cardId("@123")
				.type(StaffType.SUPERVISOR)
				.build();

		// Act & Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(updateStoreRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturnSuccess_whenArchiveStaff() throws Exception {
		// Act && Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID
				+ "/archive").contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isNoContent());
	}

	@Test
	void shouldReturnSuccess_whenInActiveStaff() throws Exception {
		// Act && Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID
				+ "/inactivate").contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isOk());
	}

	@Test
	void shouldReturnSuccess_whenActiveStaff() throws Exception {
		// Act && Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID
				+ "/activate").contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isOk());
	}

	@Test
	void testResetPinCodeWhenInvalidNumericPinCodeThenThrowBadRequest() throws Exception {
		// Arrange
		ResetPinCodeRequest resetPinCodeRequest = new ResetPinCodeRequest();
		resetPinCodeRequest.setPinCode("xyz123");

		// Act & Assert
		mockMvc.perform(put(STAFF_PATH + "/"
				+ STAFF_ID
				+ "/pin/reset").headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(resetPinCodeRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isBadRequest());
	}

	@Test
	void shouldReturnListStaffResponse_whenFindStaffQuery() throws Exception {
		// Arrange
		StaffResponse staffResponse1 = StaffResponse.builder()
				.staffId(1L)
				.tenantId(1L)
				.name("staff1")
				.status(StaffStatus.ACTIVE)
				.staffCode("xyz123")
				.cardId("1111111111")
				.type(StaffType.CASHIER)
				.build();

		StaffResponse staffResponse2 = StaffResponse.builder()
				.staffId(2L)
				.tenantId(1L)
				.name("staff2")
				.status(StaffStatus.ACTIVE)
				.staffCode("mnp123")
				.cardId("222222222")
				.type(StaffType.CASHIER)
				.build();

		ListStaffResponse response = new ListStaffResponse(List.of(staffResponse1, staffResponse2), 2, 1, 0, List.of(
				"name"));

		when(staffDomainService.findStaff(any(FindStaffsQuery.class))).thenReturn(response);
		StaffsFilterQuery staffsFilterQuery = StaffsFilterQuery.builder()
				.tenantId(TENANT_ID)
				.staffCode("mnp123")
				.name("staff2")
				.statuses(List.of(StaffStatus.ACTIVE))
				.build();
		FindStaffsQuery query = new FindStaffsQuery(staffsFilterQuery, 10, 0, "DESC", List.of("id"));

		String uri = UriComponentsBuilder.fromPath(STAFF_PATH)

				.queryParam("filter.tenantId", query.getFilter()
						.getTenantId())

				.queryParam("filter.name", query.getFilter()
						.getName())

				.queryParam("staffCode", query.getFilter()
						.getStaffCode())

				.queryParam("sortField", "name")

				.queryParam("sortDirection", "asc")

				.queryParam("page", 0)

				.queryParam("size", 10)

				.toUriString();

		// Act && Assert
		mockMvc.perform(get(uri).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(response)));
	}

	@Test
	void shouldReturnSuccess_whenGetStaff() throws Exception {
		// Arrange
		StaffResponse staffResponse = StaffResponse.builder()
				.staffId(1L)
				.tenantId(1L)
				.name("staff1")
				.status(StaffStatus.ACTIVE)
				.staffCode("xyz123")
				.cardId("1111111111")
				.type(StaffType.CASHIER)
				.build();
		when(staffDomainService.getStaff(any(GetStaffQuery.class))).thenReturn(staffResponse);
		// Act
		mockMvc.perform(get(STAFF_PATH + "/{staffId}", STAFF_ID).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(staffResponse)));
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(REQUEST_ID_HEADER, Long.toString(new Random().nextLong()));
		map.put(TENANT_ID_HEADER, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}
}
