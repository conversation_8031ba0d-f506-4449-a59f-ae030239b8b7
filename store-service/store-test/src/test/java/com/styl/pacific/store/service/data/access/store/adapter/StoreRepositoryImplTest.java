/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.store.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.domain.valueobject.Address;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.config.KafkaConsumerConfig;
import com.styl.pacific.store.service.config.IntegrationTestConfiguration;
import com.styl.pacific.store.service.domain.dto.store.FindStoresQuery;
import com.styl.pacific.store.service.domain.dto.store.StoresFilterQuery;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 */
@MockBeans({ @MockBean(KafkaConsumerConfig.class) })
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class StoreRepositoryImplTest extends BaseDataJpaTest {

	@Autowired
	StoreRepository storeRepository;

	private final Long STORE_ID = 1L;

	private final Long TENANT_ID = 100L;

	@Test
	void testSaveStoreShouldOK() {
		// Arrange
		Store store = Store.builder()
				.email("<EMAIL>")
				.name("name store")
				.id(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.status(StoreStatus.ACTIVE)
				.phoneNumber("123456789")
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		// Act
		Store storeResponse = storeRepository.save(store);
		// Assert
		assertEquals(store.getName(), storeResponse.getName());
		assertEquals(store.getEmail(), storeResponse.getEmail());
		assertEquals(store.getStatus(), storeResponse.getStatus());
		assertEquals(store.getPhoneNumber(), storeResponse.getPhoneNumber());
		assertEquals(store.getAddress()
				.getCity(), storeResponse.getAddress()
						.getCity());
		assertEquals(store.getAddress()
				.getCountry(), storeResponse.getAddress()
						.getCountry());
		assertEquals(store.getAddress()
				.getPostalCode(), storeResponse.getAddress()
						.getPostalCode());
		assertEquals(store.getAddress()
				.getAddressLine1(), storeResponse.getAddress()
						.getAddressLine1());
		assertEquals(store.getAddress()
				.getAddressLine2(), storeResponse.getAddress()
						.getAddressLine2());
	}

	@Test
	void shouldReturnStore_whenGetStore() {
		// Arrange
		Store store = Store.builder()
				.email("<EMAIL>")
				.name("name store")
				.id(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.status(StoreStatus.ACTIVE)
				.phoneNumber("123456789")
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		storeRepository.save(store);
		// Act
		Optional<Store> storeResponse = storeRepository.getStore(GetStoreQuery.builder()
				.tenantId(100L)
				.storeId(1L)
				.build());
		// Assert
		assertTrue(storeResponse.isPresent());

		assertEquals(store.getId(), storeResponse.get()
				.getId());
		assertEquals(store.getTenantId(), storeResponse.get()
				.getTenantId());
		assertEquals(store.getName(), storeResponse.get()
				.getName());
		assertEquals(store.getEmail(), storeResponse.get()
				.getEmail());
		assertEquals(store.getStatus(), storeResponse.get()
				.getStatus());
		assertEquals(store.getPhoneNumber(), storeResponse.get()
				.getPhoneNumber());
		assertEquals(store.getAddress()
				.getAddressLine1(), storeResponse.get()
						.getAddress()
						.getAddressLine1());
		assertEquals(store.getAddress()
				.getAddressLine2(), storeResponse.get()
						.getAddress()
						.getAddressLine2());
		assertEquals(store.getAddress()
				.getCity(), storeResponse.get()
						.getAddress()
						.getCity());
		assertEquals(store.getAddress()
				.getCountry(), storeResponse.get()
						.getAddress()
						.getCountry());
		assertEquals(store.getAddress()
				.getPostalCode(), storeResponse.get()
						.getAddress()
						.getPostalCode());
	}

	@Test
	void shouldReturnEmpty_whenGetStore() {
		// Arrange
		Store store = Store.builder()
				.email("<EMAIL>")
				.name("name store")
				.id(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.phoneNumber("123456789")
				.status(StoreStatus.ACTIVE)
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		storeRepository.save(store);
		// Act
		Optional<Store> storeResponse = storeRepository.getStore(GetStoreQuery.builder()
				.tenantId(TENANT_ID)
				.storeId(9999L)
				.build());
		// Assert
		assertTrue(storeResponse.isEmpty());
	}

	@Test
	void testUpdateStoreShouldOK() {
		Store store = Store.builder()
				.email("<EMAIL>")
				.name("name store")
				.id(new StoreId(STORE_ID))
				.status(StoreStatus.PENDING)
				.tenantId(new TenantId(TENANT_ID))
				.phoneNumber("123456789")
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		storeRepository.save(store);

		// act
		Store newStore = Store.builder()
				.email("<EMAIL>")
				.name("new name store")
				.id(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.phoneNumber("46374736437")
				.status(StoreStatus.ACTIVE)
				.address(Address.builder()
						.addressLine1("newline1")
						.addressLine2("newline2")
						.city("NEWCITY")
						.country("US")
						.postalCode("99")
						.build())
				.build();

		Store updated = storeRepository.save(newStore);

		// assert
		assertNotNull(updated);
		assertEquals(newStore.getId(), updated.getId());
		assertEquals(newStore.getTenantId(), updated.getTenantId());
		assertEquals(newStore.getName(), updated.getName());
		assertEquals(newStore.getEmail(), updated.getEmail());
		assertEquals(newStore.getStatus(), updated.getStatus());
		assertEquals(newStore.getPhoneNumber(), updated.getPhoneNumber());
		assertEquals(newStore.getAddress()
				.getAddressLine1(), updated.getAddress()
						.getAddressLine1());
		assertEquals(newStore.getAddress()
				.getAddressLine2(), updated.getAddress()
						.getAddressLine2());
		assertEquals(newStore.getAddress()
				.getCity(), updated.getAddress()
						.getCity());
		assertEquals(newStore.getAddress()
				.getCountry(), updated.getAddress()
						.getCountry());
		assertEquals(newStore.getAddress()
				.getPostalCode(), updated.getAddress()
						.getPostalCode());
	}

	@Test
	void testPagingStoresShouldOK() {
		// create 2 tenants
		Store store1 = Store.builder()
				.email("<EMAIL>")
				.name("store other name")
				.id(new StoreId(STORE_ID))
				.tenantId(new TenantId(TENANT_ID))
				.status(StoreStatus.PENDING)
				.phoneNumber("123456789")
				.address(Address.builder()
						.addressLine1("line1")
						.addressLine2("line2")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		storeRepository.save(store1);
		Store store2 = Store.builder()
				.email("<EMAIL>")
				.name("store other name")
				.id(new StoreId(2L))
				.tenantId(new TenantId(TENANT_ID))
				.status(StoreStatus.PENDING)
				.phoneNumber("343434343")
				.address(Address.builder()
						.addressLine1("line3")
						.addressLine2("line4")
						.city("HCM")
						.country("VN")
						.postalCode("84")
						.build())
				.createdAt(Instant.now())
				.build();
		storeRepository.save(store2);

		// Create 100 tenants
		for (int i = 0; i < 100; i++) {
			Store store = Store.builder()
					.email("<EMAIL>")
					.name("store other name")
					.id(new StoreId(i + 8L))
					.tenantId(new TenantId(TENANT_ID))
					.status(StoreStatus.ACTIVE)
					.phoneNumber("123456789")
					.address(Address.builder()
							.addressLine1("line1")
							.addressLine2("line2")
							.city("HCM")
							.country("VN")
							.postalCode("84")
							.build())
					.createdAt(Instant.now())
					.build();
			storeRepository.save(store);
		}

		StoresFilterQuery filterQuery1 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.ACTIVE))
				.build();

		// act
		Paging<Store> result1 = storeRepository.findStores(new FindStoresQuery(filterQuery1, 10, 0, "ASC", List.of(
				"id")));

		// assert
		assertNotNull(result1);
		assertEquals(10, result1.getContent()
				.size());
		assertEquals(100, result1.getTotalElements());
		assertEquals(10, result1.getTotalPages());
		assertEquals(0, result1.getPage());
		assertEquals(1, result1.getSort()
				.size());
		for (int i = 0; i < 9; i++) {
			assertTrue(result1.getContent()
					.get(i)
					.getId()
					.getValue() < result1.getContent()
							.get(i + 1)
							.getId()
							.getValue());
		}

		StoresFilterQuery filterQuery2 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.ACTIVE))
				.build();
		// act
		Paging<Store> result2 = storeRepository.findStores(new FindStoresQuery(filterQuery2, 10, 1, "DESC", List.of(
				"id")));
		assertNotNull(result2);
		assertEquals(10, result2.getContent()
				.size());
		assertEquals(100, result2.getTotalElements());
		assertEquals(10, result2.getTotalPages());
		assertEquals(1, result2.getPage());
		assertEquals(1, result2.getSort()
				.size());

		StoresFilterQuery filterQuery3 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.ACTIVE))
				.build();
		// act
		Paging<Store> result3 = storeRepository.findStores(new FindStoresQuery(filterQuery3, 10, 2, "DESC", List.of(
				"id")));
		assertNotNull(result3);
		assertEquals(10, result3.getContent()
				.size());
		assertEquals(100, result3.getTotalElements());
		assertEquals(10, result3.getTotalPages());
		for (int i = 0; i < 9; i++) {
			assertTrue(result3.getContent()
					.get(i)
					.getId()
					.getValue() > result3.getContent()
							.get(i + 1)
							.getId()
							.getValue());
		}

		StoresFilterQuery filterQuery4 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.ACTIVE))
				.build();
		// act
		Paging<Store> result4 = storeRepository.findStores(new FindStoresQuery(filterQuery4, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result4);
		assertEquals(10, result4.getContent()
				.size());
		assertEquals(100, result4.getTotalElements());
		assertEquals(10, result4.getTotalPages());
		for (int i = 0; i < 9; i++) {
			assertTrue(result4.getContent()
					.get(i)
					.getId()
					.getValue() > result4.getContent()
							.get(i + 1)
							.getId()
							.getValue());
		}

		StoresFilterQuery filterQuery5 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.PENDING))
				.name("store other name")
				.build();
		// act
		Paging<Store> result5 = storeRepository.findStores(new FindStoresQuery(filterQuery5, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result5);
		assertEquals(2, result5.getContent()
				.size());

		StoresFilterQuery filterQuery6 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.statuses(List.of(StoreStatus.PENDING))
				.build();
		// act
		Paging<Store> result6 = storeRepository.findStores(new FindStoresQuery(filterQuery6, 10, 1, "DESC", List.of(
				"id")));
		assertNotNull(result6);
		assertEquals(0, result6.getContent()
				.size());

		StoresFilterQuery filterQuery7 = StoresFilterQuery.builder()
				.tenantId(TENANT_ID)
				.name("test")
				.build();
		// act
		Paging<Store> result7 = storeRepository.findStores(new FindStoresQuery(filterQuery7, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result7);
		assertEquals(0, result7.getContent()
				.size());
	}

}
