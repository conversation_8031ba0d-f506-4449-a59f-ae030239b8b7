/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.application;

import com.styl.pacific.store.service.domain.CashFloatDomainCore;
import com.styl.pacific.store.service.domain.CashFloatDomainCoreImpl;
import com.styl.pacific.store.service.domain.DeviceDomainCore;
import com.styl.pacific.store.service.domain.DeviceDomainCoreImpl;
import com.styl.pacific.store.service.domain.StaffDomainCore;
import com.styl.pacific.store.service.domain.StaffDomainCoreImpl;
import com.styl.pacific.store.service.domain.StoreDomainCore;
import com.styl.pacific.store.service.domain.StoreDomainCoreImpl;
import com.styl.pacific.store.service.domain.entity.CashFloatIdGenerator;
import com.styl.pacific.store.service.domain.entity.DeviceSettingIdGenerator;
import com.styl.pacific.store.service.domain.entity.StaffAssignmentIdGenerator;
import com.styl.pacific.store.service.domain.entity.StaffIdGenerator;
import com.styl.pacific.store.service.domain.entity.StoreIdGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *
 */
@Configuration
public class BeanConfiguration {

	@Bean
	public StoreIdGenerator storeIdGenerator() {
		return new StoreIdGeneratorImpl();
	}

	@Bean
	public StaffIdGenerator staffIdGenerator() {
		return new StaffIdGeneratorImpl();
	}

	@Bean
	public CashFloatIdGenerator cashFloatIdGenerator() {
		return new CashFloatIdGeneratorImpl();
	}

	@Bean
	public StaffAssignmentIdGenerator staffAssignmentIdGenerator() {
		return new StaffAssignmentIdGeneratorImpl();
	}

	@Bean
	public DeviceSettingIdGenerator deviceSettingIdGenerator() {
		return new DeviceSettingIdGeneratorImpl();
	}

	@Bean
	public StoreDomainCore storeDomainCore() {
		return new StoreDomainCoreImpl(storeIdGenerator());
	}

	@Bean
	public DeviceDomainCore deviceDomainCore() {
		return new DeviceDomainCoreImpl();
	}

	@Bean
	public StaffDomainCore staffDomainCore() {
		return new StaffDomainCoreImpl(staffIdGenerator());
	}

	@Bean
	public CashFloatDomainCore cashFloatDomainCore() {
		return new CashFloatDomainCoreImpl(cashFloatIdGenerator());
	}

}