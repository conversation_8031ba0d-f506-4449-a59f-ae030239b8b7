/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.port.input.service;

import com.styl.pacific.store.service.domain.dto.staff.ActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ArchiveStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.CreateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.FindStaffsQuery;
import com.styl.pacific.store.service.domain.dto.staff.InActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ResetPinCodeCommand;
import com.styl.pacific.store.service.domain.dto.staff.UpdateStaffCommand;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import com.styl.pacific.store.shared.http.responses.staff.ListStaffResponse;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 *
 */
public interface StaffDomainService {

	StaffResponse createStaff(@Valid CreateStaffCommand command);

	StaffResponse getStaff(GetStaffQuery query);

	ListStaffResponse findStaff(@Valid FindStaffsQuery query);

	StaffResponse inActivateStaff(InActivateStaffCommand inActivateStaffCommand);

	StaffResponse activateStaff(ActivateStaffCommand activateStaffCommand);

	void archiveStaff(ArchiveStaffCommand archiveStaffCommand);

	StaffResponse updateStaff(UpdateStaffCommand updateStaffCommand);

	void resetPinCode(ResetPinCodeCommand resetPinCodeCommand);

	StaffResponse verifyStaff(Long tenantId, Long storeId, String staffCode, String pinCode);

	StaffResponse verifyStaffCard(Long tenantId, Long storeId, String cardId);
}
