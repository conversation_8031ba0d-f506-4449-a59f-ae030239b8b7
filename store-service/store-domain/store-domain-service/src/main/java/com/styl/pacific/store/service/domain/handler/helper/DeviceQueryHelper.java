/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.store.service.domain.configuration.DMSConfiguration;
import com.styl.pacific.store.service.domain.dto.device.FindDevicesQuery;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.entity.DeviceSession;
import com.styl.pacific.store.service.domain.entity.DeviceSetting;
import com.styl.pacific.store.service.domain.exception.DeviceDMSInvalidException;
import com.styl.pacific.store.service.domain.exception.DeviceNotFoundException;
import com.styl.pacific.store.service.domain.exception.DeviceSessionException;
import com.styl.pacific.store.service.domain.exception.DeviceSessionNotFoundException;
import com.styl.pacific.store.service.domain.mapper.DeviceDataMapper;
import com.styl.pacific.store.service.domain.output.repository.DMSRepository;
import com.styl.pacific.store.service.domain.output.repository.DeviceRepository;
import com.styl.pacific.store.service.domain.utils.SHACryptographic;
import com.styl.pacific.store.shared.http.enums.SessionStatus;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceQuery;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceSettingQuery;
import com.styl.pacific.store.shared.http.responses.device.DMSDevice;
import com.styl.pacific.store.shared.http.responses.device.ListDeviceDMSResponse;
import com.styl.pacific.store.shared.http.responses.device.ListDevicesResponse;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class DeviceQueryHelper {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final DeviceRepository deviceRepository;

	private final DMSRepository dmsRepository;

	private final DMSConfiguration dmsConfiguration;

	public Device getDevice(GetDeviceQuery query) {
		Optional<Device> deviceResult = deviceRepository.getDevice(query);
		if (deviceResult.isEmpty()) {
			throw new DeviceNotFoundException("Device not found with id: " + query.getDeviceId());
		}
		logger.info("Get Device with id {}", query.getDeviceId());
		return deviceResult.get();
	}

	public DeviceSetting getDeviceSetting(GetDeviceSettingQuery query) {
		Optional<DeviceSetting> deviceResult = deviceRepository.getDeviceSetting(query);
		return deviceResult.orElseGet(() -> DeviceSetting.builder()
				.build());
	}

	public ListDevicesResponse findDevices(FindDevicesQuery query) {
		Paging<Device> result = deviceRepository.findDevices(query);
		return new ListDevicesResponse(result.getContent()
				.stream()
				.map(DeviceDataMapper.INSTANCE::deviceToDeviceResponse)
				.toList(), result.getTotalElements(), result.getTotalPages(), result.getPage(), result.getSort());
	}

	public Device findDeviceById(String deviceId) {
		return deviceRepository.findById(deviceId)
				.orElse(null);
	}

	public DMSDevice getDeviceFomDMS(String deviceId) {
		Map<String, String> headers = new HashMap<>();
		String queryParams = "id=" + deviceId;
		String nonce = String.format("%s#%s", Instant.now()
				.toEpochMilli(), UUID.randomUUID());
		String signatureData = Stream.of(null, queryParams, nonce)
				.filter(Objects::nonNull)
				.collect(Collectors.joining());

		if (StringUtils.isAnyBlank(dmsConfiguration.getApiKey(), dmsConfiguration.getPrivateKey())) {
			throw new DeviceDMSInvalidException("Unable to authenticate DMS. API Key or Private Key is missing");
		}

		String signature = SHACryptographic.getSignatureHMAC256(dmsConfiguration.getPrivateKey(), signatureData);
		headers.put("DMS-ApiKey", dmsConfiguration.getApiKey());
		headers.put("DMS-Signature", signature);
		headers.put("DMS-Nonce", nonce);
		ListDeviceDMSResponse listDeviceDMSResponse = dmsRepository.getDevice(headers, deviceId);
		if (CollectionUtils.isEmpty(listDeviceDMSResponse.getData())) {
			return null;
		}
		if (listDeviceDMSResponse.getData()
				.size() == 1) {
			return listDeviceDMSResponse.getData()
					.getFirst();
		}
		throw new DeviceDMSInvalidException("Device DMS is invalid with more 1 result");
	}

	public DeviceSession getDeviceSession(String sessionId, Long tenantId) {
		Optional<DeviceSession> deviceSession = deviceRepository.getDeviceSession(sessionId, tenantId);
		if (deviceSession.isEmpty()) {
			throw new DeviceSessionNotFoundException("Device Session is not found");
		}
		if (Objects.equals(SessionStatus.INACTIVE, deviceSession.get()
				.getStatus())) {
			throw new DeviceSessionException("Session is inactive with sessionId: " + sessionId);
		}
		return deviceSession.get();
	}
}
