/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.payment.sessions;

import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;

public interface PaymentSessionService {
	PaymentSessionResponse createPaymentSession(CreatePaymentSessionRequest request);

	PaymentSessionResponse getPaymentSession(String sessionId);

	PaymentSessionResponse cancelPaymentSession(String sessionId);

	PaymentTransactionResponse settlePaymentSession(String sessionId, SettlePaymentSessionRequest request);
}
