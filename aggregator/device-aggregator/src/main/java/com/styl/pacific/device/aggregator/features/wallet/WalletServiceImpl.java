/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.wallet;

import com.styl.pacific.device.aggregator.exception.DeviceAggregatorException;
import com.styl.pacific.device.aggregator.features.users.clients.UserCardClient;
import com.styl.pacific.device.aggregator.features.users.clients.UserProfileClient;
import com.styl.pacific.device.aggregator.features.users.dtos.UserDto;
import com.styl.pacific.device.aggregator.features.users.mapper.UserMapper;
import com.styl.pacific.device.aggregator.features.wallet.clients.WalletClient;
import com.styl.pacific.device.aggregator.features.wallet.clients.WalletSettingClient;
import com.styl.pacific.device.aggregator.features.wallet.dtos.CardDto;
import com.styl.pacific.device.aggregator.features.wallet.dtos.GetWalletRequest;
import com.styl.pacific.device.aggregator.features.wallet.dtos.UserWalletCombineResponse;
import com.styl.pacific.device.aggregator.features.wallet.dtos.WalletDto;
import com.styl.pacific.device.aggregator.features.wallet.mapper.WalletMapper;
import com.styl.pacific.user.shared.http.cards.response.FullUserCardResponse;
import com.styl.pacific.user.shared.http.users.request.FetchRuleRequest;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.wallet.service.requests.wallet.CreditMultiWalletRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreditRequest;
import com.styl.pacific.wallet.service.requests.wallet.IndividualWalletsFilterRequest;
import com.styl.pacific.wallet.service.requests.wallet.WalletsEntryExpireFilterRequest;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletEntryExpireResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletResponse;
import com.styl.pacific.wallet.service.responses.walletsetting.WalletSettingResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WalletServiceImpl implements WalletService {

	private final WalletClient walletClient;

	private final UserCardClient userCardClient;

	private final UserProfileClient userProfileClient;

	private final WalletSettingClient walletSettingClient;

	@Override
	public WalletResponse getWallet(long id) {
		return walletClient.getWallet(id);
	}

	@Override
	public WalletTransactionResponse pay(long id, CreditRequest request) {
		return walletClient.pay(id, request);
	}

	@Override
	public UserWalletCombineResponse getIndividualWallets(GetWalletRequest query) {
		if (!StringUtils.hasText(query.getCardId()) && !StringUtils.hasText(query.getCustomerId())) {
			throw new DeviceAggregatorException("must not empty cardId or customerId");
		}
		UserResponse userResponse = null;
		CardDto cardDto = null;
		if (StringUtils.hasText(query.getCardId())) {
			// get by card
			FullUserCardResponse userCardResponse = userCardClient.getUserCardByCardId(query.getCardId());
			userResponse = userCardResponse.getUser();
			cardDto = new CardDto(userCardResponse.getCardId(), userCardResponse.getCardAlias(), userCardResponse
					.getStatus());
		} else {
			// get by customerId
			userResponse = userProfileClient.getUserProfile(Long.parseLong(query.getCustomerId()), FetchRuleRequest
					.builder()
					.fetchPermissions(false)
					.countSponsors(true)
					.countSubAccounts(true)
					.build());
		}
		List<WalletDto> walletResponses = walletClient.queryIndividualWallets(IndividualWalletsFilterRequest.builder()
				.customerId(userResponse.getId())
				.types(query.getTypes())
				.build())
				.stream()
				.map(WalletMapper.INSTANCE::toWalletDto)
				.toList();
		UserDto userDto = UserMapper.INSTANCE.toUserDto(userResponse);
		return new UserWalletCombineResponse(walletResponses, userDto, cardDto);
	}

	@Override
	public WalletSettingResponse getWalletSetting() {
		return walletSettingClient.getWalletSetting();
	}

	@Override
	public WalletTransactionResponse pay(CreditMultiWalletRequest request) {
		return walletClient.pay(request);
	}

	@Override
	public WalletEntryExpireResponse getExpiringAmount(long id, WalletsEntryExpireFilterRequest request) {
		return walletClient.getExpiringAmount(id, request);
	}
}
