/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.order.apis;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.MealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.PaginationMealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface MealTimeApi {
	@GetMapping("/api/order/mealtimes/page")
	@ResponseStatus(HttpStatus.OK)
	Paging<MealTimeResponse> findAllPaging(@SpringQueryMap @ModelAttribute PaginationMealTimeQueryRequest query);

	@GetMapping("/api/order/mealtimes")
	@ResponseStatus(HttpStatus.OK)
	Content<MealTimeResponse> findAll(@SpringQueryMap @ModelAttribute MealTimeQueryRequest query);
}
