/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.wallet;

import com.styl.pacific.device.aggregator.features.wallet.dtos.GetWalletRequest;
import com.styl.pacific.device.aggregator.features.wallet.dtos.UserWalletCombineResponse;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.wallet.service.requests.wallet.CreditMultiWalletRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreditRequest;
import com.styl.pacific.wallet.service.requests.wallet.WalletsEntryExpireFilterRequest;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletEntryExpireResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletResponse;
import com.styl.pacific.wallet.service.responses.walletsetting.WalletSettingResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class WalletController {

	private final WalletService walletService;

	@GetMapping(path = "/wallets/{id}")
	@ResponseStatus(HttpStatus.OK)
	WalletResponse getWallet(@PathVariable long id) {
		return walletService.getWallet(id);
	}

	@PostMapping(path = "/wallets/{id}/pay")
	@ResponseStatus(HttpStatus.OK)
	WalletTransactionResponse pay(@PathVariable long id, @RequestBody @Valid CreditRequest request) {
		return walletService.pay(id, request);
	}

	@GetMapping(path = "/wallets")
	@ResponseStatus(HttpStatus.OK)
	UserWalletCombineResponse getIndividualWallets(@SpringQueryMap @ModelAttribute GetWalletRequest query) {
		return walletService.getIndividualWallets(query);
	}

	@GetMapping(path = "/wallet/settings")
	@ResponseStatus(HttpStatus.OK)
	WalletSettingResponse getWalletSetting() {
		return walletService.getWalletSetting();
	}

	@PostMapping(path = "/wallets/pay")
	@ResponseStatus(HttpStatus.OK)
	WalletTransactionResponse pay(@RequestBody @Valid CreditMultiWalletRequest request) {
		return walletService.pay(request);
	}

	@GetMapping(path = "/wallets/{id}/expiring")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	WalletEntryExpireResponse getExpiringAmount(@PathVariable long id,
			@SpringQueryMap @ModelAttribute @Valid WalletsEntryExpireFilterRequest request) {
		return walletService.getExpiringAmount(id, request);
	}
}
