/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.wallet;

import com.styl.pacific.device.aggregator.features.wallet.dtos.GetWalletRequest;
import com.styl.pacific.device.aggregator.features.wallet.dtos.UserWalletCombineResponse;
import com.styl.pacific.wallet.service.requests.wallet.CreditMultiWalletRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreditRequest;
import com.styl.pacific.wallet.service.requests.wallet.WalletsEntryExpireFilterRequest;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletEntryExpireResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletResponse;
import com.styl.pacific.wallet.service.responses.walletsetting.WalletSettingResponse;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 */
public interface WalletService {
	WalletResponse getWallet(long id);

	WalletTransactionResponse pay(long id, CreditRequest request);

	UserWalletCombineResponse getIndividualWallets(GetWalletRequest query);

	WalletSettingResponse getWalletSetting();

	WalletTransactionResponse pay(CreditMultiWalletRequest request);

	WalletEntryExpireResponse getExpiringAmount(long id, @Valid WalletsEntryExpireFilterRequest request);
}
