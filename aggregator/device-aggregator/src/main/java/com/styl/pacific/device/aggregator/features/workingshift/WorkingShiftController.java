/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.workingshift;

import com.styl.pacific.store.shared.http.requests.cashfloat.CreateCashFloatRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.UpdateCashFloatRequest;
import com.styl.pacific.store.shared.http.responses.cashfloat.CashFloatResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class WorkingShiftController {
	private final CashFloatService cashFloatService;

	@PostMapping(path = "/cashfloat")
	@ResponseStatus(HttpStatus.OK)
	CashFloatResponse createCashFloat(@RequestBody CreateCashFloatRequest request) {
		return cashFloatService.createCashFloat(request);
	}

	@PutMapping(path = "/cashfloat/{id}")
	@ResponseStatus(HttpStatus.OK)
	CashFloatResponse updateCashFloat(@PathVariable long id, @RequestBody UpdateCashFloatRequest request) {
		return cashFloatService.updateCashFloat(id, request);
	}
}
