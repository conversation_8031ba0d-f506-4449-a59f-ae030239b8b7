/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.payment.apis;

import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface PaymentSessionApi {

	@PostMapping(path = "/api/payment/sessions")
	@ResponseStatus(HttpStatus.CREATED)
	PaymentSessionResponse createPaymentSession(@RequestBody @Valid CreatePaymentSessionRequest request);

	@GetMapping(path = "/api/payment/sessions/{sessionId}")
	@ResponseStatus(HttpStatus.OK)
	PaymentSessionResponse getPaymentSession(@PathVariable("sessionId") String sessionId);

	@PostMapping(path = "/api/payment/sessions/{sessionId}/cancel")
	@ResponseStatus(HttpStatus.OK)
	PaymentSessionResponse cancelPaymentSession(@PathVariable("sessionId") String sessionId);

	@PostMapping(path = "/api/payment/sessions/{paymentSessionId}/settle")
	@ResponseStatus(HttpStatus.OK)
	PaymentTransactionResponse settlePaymentSession(@PathVariable("paymentSessionId") String paymentSessionId,
			@RequestBody @Valid SettlePaymentSessionRequest request);
}
