/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.aws.dynamodb.repository;

import java.util.Optional;
import org.springframework.lang.NonNull;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.ScanEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

/**
 * <AUTHOR>
 */
public abstract class DynamoDbRepository<T, I> {

	protected final DynamoDbClient dynamoDbClient;

	protected final DynamoDbEnhancedClient dynamoDbEnhancedClient;

	protected DynamoDbRepository(DynamoDbClient dynamoDbClient, DynamoDbEnhancedClient dynamoDbEnhancedClient) {
		this.dynamoDbClient = dynamoDbClient;
		this.dynamoDbEnhancedClient = dynamoDbEnhancedClient;
	}

	protected abstract @NonNull Class<T> getClassType();

	protected @NonNull String getTableName() {
		return getClass().getSimpleName();
	}

	protected DynamoDbTable<T> getMappedTable(Class<T> type) {
		return dynamoDbEnhancedClient.table(getTableName(), TableSchema.fromBean(type));
	}

	private Key getKey(I id) {
		Key.Builder keyBuilder = Key.builder();
		switch (id) {
		case String stringId -> keyBuilder.partitionValue(stringId);
		case Number numberId -> keyBuilder.partitionValue(numberId);
		case SdkBytes byteId -> keyBuilder.partitionValue(byteId);
		default -> throw new IllegalArgumentException("Unsupported id type " + id.getClass());
		}
		return keyBuilder.build();
	}

	public Optional<T> findById(I id) {
		return Optional.ofNullable(getMappedTable(getClassType()).getItem(getKey(id)));
	}

	public Optional<T> findById(Key key) {
		return Optional.ofNullable(getMappedTable(getClassType()).getItem(key));
	}

	public void save(T item) {
		getMappedTable(getClassType()).putItem(item);
	}

	public T update(T item) {
		return getMappedTable(getClassType()).updateItem(item);
	}

	public void deleteById(Key key) {
		getMappedTable(getClassType()).deleteItem(key);
	}

	public void delete(T item) {
		getMappedTable(getClassType()).deleteItem(item);
	}

	public PageIterable<T> query(QueryEnhancedRequest request) {
		return getMappedTable(getClassType()).query(request);
	}

	public PageIterable<T> query(QueryConditional request) {
		return getMappedTable(getClassType()).query(request);
	}

	public PageIterable<T> scan() {
		return getMappedTable(getClassType()).scan();
	}

	public PageIterable<T> scan(ScanEnhancedRequest request) {
		return getMappedTable(getClassType()).scan(request);
	}

}
