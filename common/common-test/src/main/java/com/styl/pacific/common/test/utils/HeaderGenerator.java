/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.test.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.constant.PacificRestConstants;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class HeaderGenerator {

	public static HttpHeaders generateHttpHeaders(GenerateHttpHeader template) {
		final var map = new HashMap<String, List<String>>();

		Optional.ofNullable(template.userTokenClaim())
				.ifPresent(userTokenClaim -> {
					try {
						final var mapper = Optional.ofNullable(template.objectMapper())
								.orElseGet(ObjectMapper::new);
						final var basicAuth = String.join(".", "header", Base64.getUrlEncoder()
								.withoutPadding()
								.encodeToString(mapper.writeValueAsString(userTokenClaim)
										.getBytes(StandardCharsets.UTF_8)), "signature");
						map.put(HttpHeaders.AUTHORIZATION, List.of(basicAuth));
					} catch (JsonProcessingException e) {
						log.error(e.getMessage(), e);
						throw new RuntimeException(e);
					}
				});

		Optional.ofNullable(template.requestId())
				.ifPresent(id -> map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, List.of(String
						.valueOf(id))));
		Optional.ofNullable(template.tenantId())
				.ifPresent(id -> map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, List.of(String.valueOf(
						id))));

		return Optional.ofNullable(template.header())
				.map(header -> {
					header.addAll(new LinkedMultiValueMap<>(map));
					return header;
				})
				.orElseGet(() -> new HttpHeaders(new LinkedMultiValueMap<>(map)));
	}

	public static void generateStripeHeader(HttpHeaders header, String stripeSignature) {
		final var map = new HashMap<String, List<String>>();
		map.put("Stripe-Signature", List.of(stripeSignature));

		header.addAll(new LinkedMultiValueMap<>(map));
	}
}
