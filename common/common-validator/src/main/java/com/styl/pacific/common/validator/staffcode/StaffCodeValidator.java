/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.validator.staffcode;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */

public class StaffCodeValidator implements ConstraintValidator<StaffCode, String> {

	private static final String STAFF_CODE_REGEX = "^((?!-)[-a-zA-Z0-9]{0,10})[a-zA-Z0-9]+$";

	private Pattern pattern;

	@Override
	public void initialize(StaffCode constraintAnnotation) {
		pattern = Pattern.compile(STAFF_CODE_REGEX);
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if (value == null) {
			return true;
		}

		return pattern.matcher(value)
				.matches();
	}
}