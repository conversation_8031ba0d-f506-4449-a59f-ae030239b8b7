/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.domain.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *         <p>
 *         Error codes range:
 *         <p>
 *         0xxxx: Common error codes
 *         <p>
 *         1xxxx: Tenant error codes
 *         <p>
 *         2xxxx: User error codes
 *         <p>
 *         3xxxx: Catalog error codes
 *         <p>
 *         4xxxx: Store error codes
 *         <p>
 *         5xxxx: Order error codes
 *         <p>
 *         6xxxx: Payment error codes
 *         <p>
 *         7xxxx: Notification error codes
 *         <p>
 *         8xxxx: Utility error codes
 *         <p>
 *         9xxxx: Authorization error codes
 *         <p>
 *         1xxxxx: Device Aggregator error codes
 *         <p>
 *         11xxxx: Wallet error codes
 *         <p>
 *         12xxxx: Gateway error codes
 */
@Getter
@AllArgsConstructor
public enum GlobalErrorCode {
	UNAUTHORIZED(401, "The requested was not authenticated."),
	FORBIDDEN(403, "Access is forbidden. Please contact Admin for permissions access"),
	NOT_FOUND_ERROR(404, "The requested resource was not found."),
	TOO_MANY_REQUESTS(429, "Too many requests."),
	VALIDATION_ERROR(1000, "Validation error occurred. Please check the input data and try again."),
	FEIGN_CLIENT_ERROR(9000, "An error occurred while calling an internal service."),
	UNIMPLEMENTED_EXCEPTION(9900, "This functionality is not implemented yet."),
	UNKNOWN_ERROR(9999, "An unknown error occurred."),

	TENANT_SERVICE_GENERIC_ERROR(10000, Constants.GENERIC_ERROR_MESSAGE),
	TENANT_NOT_FOUND(10001, "Tenant is not found"),
	TENANT_CHECKLIST_NOT_COMPLETED(10002, "The checklists are not completed"),
	TAX_NOT_FOUND(10003, "Tax is not found"),
	TENANT_DOMAIN_NAME_IN_USED(10004, "Domain name is in used"),

	USER_SERVICE_GENERIC_ERROR(20000, Constants.GENERIC_ERROR_MESSAGE),
	USER_UPDATE_ERROR(20001, "User update is error"),
	USER_GROUP_REACHING_LIMIT(20002, "Reaching limit for maximum user group level"),
	USER_HAS_EXISTED(20003, "User has been existed"),
	USER_SUB_ACCOUNT_NOT_FOUND(20004, "Sub Account has not found"),
	USER_SUB_ACCOUNT_INVALID_SUB_USER_AS_SUB(20005, "User has been a sub-user in other sponsor"),
	USER_SUB_ACCOUNT_INVALID_SPONSOR_ID(20006, "User has been already a sub account in other sponsor"),
	USER_INVITATION_CREATE_FAILURE(20007, "Unable to send an invitation"),
	USER_CARD_FAILURE(20008, "User card failure occurred."),
	USER_CARD_NOT_FOUND(20009, "User card not found"),
	USER_GROUP_HANDLING_ERROR(200010, "Handling user group operation error"),
	USER_NOT_FOUND(200011, "User not found"),
	USER_ALLERGEN_FAILURE(200012, "User Allergen failure occurred."),
	USER_ALLERGEN_NOT_FOUND(200013, "User Allergen not found"),
	USER_PREFERENCE_MISMATCHED_KEY(200014, "User preference key is mismatched"),
	USER_PREFERENCE_EXISTED(200015, "User preference key has already existed"),
	USER_PREFERENCE_NOT_FOUND(200016, "User preference has not found"),
	USER_ROLE_CHANGE_NOT_ALLOW(200017, "Cannot assign Tenant Owner role due not to Admin"),
	USER_ROLE_NOT_FOUND(200018, "User role not found"),
	USER_INVITATION_RESEND_FAILURE(200019, "Invitation was sent. Please retry after a minute."),
	USER_BACK_OFFICE_ASSIGN_FAILURE(200020,
			"Cannot assign to tenant because the current user is not as BackOffice. Please try with another"),
	USER_ALREADY_IN_TENANT(200021, "User has already in Tenant"),
	USER_ACTIVATION_REQUIRED(200022, "User activation is required"),
	USER_EMAIL_ALREADY_EXISTING(200023, "Email address already in use"),
	USER_UNIQUE_EXTERNAL_ID_DUPLICATED(200024, "External Identification code has been existed"),
	USER_CARD_ALREADY_EXISTING(200025, "Card ID has been existed"),
	KEYCLOAK_SERVICE_ACCOUNT_FORBIDDEN(200026,
			"Keycloak Service Account access has been forbidden. Please checkout, and grant permission access in Admin Keycloak Management"),
	USER_SUB_ACCOUNT_INVALID_SUB_USER_AS_SPONSOR(200027, "User has been a sponsor in other"),
	USER_SUB_USER_NOT_FOUND(200028, "We had not found the existing user based on your conditions. Please try again."),
	USER_BANNED_ITEM_FAILURE(200029, "Banned Item failure occurred."),
	USER_MIGRATION_ID_DUPLICATED(200030, "MigrationId has been existed"),
	USER_ONLY_ADMIN_ASSIGN_TENANT_OWNER(200031, "Only Admin can assign Tenant Owner role."),
	USER_TENANT_OWNER_EXISTED(200032, "Owner has been existed. Please un-assign Tenant Owner role first."),
	USER_SUB_ACCOUNT_INVALID_SELF_AS_SUB(200033, "You cannot add yourself as a sub-account."),
	USER_SUB_ACCOUNT_INVALID_HAS_BEEN_SUB_ACCOUNT(200034, "Customer has been a subaccount"),
	ALTERNATIVE_USER_ACCESS_NOT_FOUND(200035, "Alternative user access not found"),

	CATALOG_SERVICE_GENERIC_ERROR(30000, Constants.GENERIC_ERROR_MESSAGE),
	CATEGORY_NOT_FOUND(30001, "Category is not found"),
	PRODUCT_NOT_FOUND(30002, "Product is not found"),
	PRODUCT_OPTION_NOT_FOUND(30003, "Product option is not found"),
	HEALTHIER_CHOICE_NOT_FOUND(30004, "Healthier choice is not found"),
	NUTRITION_NOT_FOUND(30005, "Nutrition is not found"),
	NUTRITION_IN_USE(30006, "Nutrition is in use"),
	ALLERGEN_NOT_FOUND(30006, "Allergen is not found"),
	SKU_ALREADY_EXISTS(30007, "SKU already exists"),
	ALLERGEN_ALREADY_EXISTED(30008, "Allergen is existed"),

	STORE_SERVICE_GENERIC_ERROR(40000, Constants.GENERIC_ERROR_MESSAGE),
	STORE_NOT_FOUND(40001, "Store is not found"),
	STORE_NOT_ACTIVATE(40002, "Store must be active"),
	DEVICE_NOT_FOUND(40011, "Device is not found"),
	DEVICE_DMS_NOT_FOUND(40012, "Device DMS is not found"),
	DEVICE_DMS_INVALID(40013, "Device DMS is invalid"),
	DEVICE_UNASSIGN(40014, "Device is unassign"),
	DEVICE_DMS_UNASSIGN(40015, "Device DMS is unassign"),
	DEVICE_UPDATE_FAIL(40016, "Device is updated fail"),
	DEVICE_SESSION_NOT_FOUND(40017, "Device Session is not found"),
	DEVICE_SESSION_INVALID(40018, "Device Session is invalid"),
	DEVICE_STATUS_INVALID(40019, "Device Status is invalid"),
	DEVICE_ASSIGN(40020, "Device is assign"),
	STAFF_NOT_FOUND(40021, "Staff is not found"),
	STAFF_STATUS_INVALID(40022, "Staff Status is invalid"),
	STAFF_VERIFY_FAIL(40023, "Verify staff fail"),
	STAFF_EXISTED(40024, "Staff is existed"),
	CASH_FLOAT_NOT_FOUND(40031, "Cash Float is not found"),

	ORDER_SERVICE_GENERIC_ERROR(50000, Constants.GENERIC_ERROR_MESSAGE),
	ORDER_NOT_FOUND(50001, "Order is not found"),
	INVALID_ORDER(50002, "An error occurred while processing the order."),
	INVALID_ORDER_LINE_ITEM_INFO(50003, "Invalid order line item information"),
	ORDER_CANCEL_NOT_ALLOWED(50004, "Order cancel is not allowed"),
	ORDER_RETRYABLE(50005, "There is a conflict with the current state of the resource. Please try again later."),
	INVALID_ORDER_LINE_ITEM_INPUT(50006, "Invalid order line item input"),
	PRODUCT_EXCEEDED_MAX_QUANTITY(50007, "Product have exceeded maximum quantity allowed to purchase"),
	ORDER_PAYMENT_METHOD_REQUIRED(50008, "Payment method is required"),
	INVALID_ORDER_AMOUNT(50009, "Invalid order amount"),
	PRODUCT_BELOW_MIN_QUANTITY(50009, "Product quantity is below minimum quantity allowed to purchase"),
	INVALID_ORDER_LINE_ITEM_AMOUNT(50010, "Invalid order line item amount input"),

	MENU_NOT_FOUND(50011, "Menu is not found"),
	MENU_ITEM_NOT_FOUND(50012, "Menu Item is not found"),
	MENU_ITEM_UNAVAILABLE(50013, "Menu Item is unavailable"),
	MENU_ITEM_PRODUCT_ALREADY_ASSIGN(50014, "Product is already assigned to the menu"),
	MEAL_TIME_NOT_FOUND(50021, "Meal Time is not found"),
	TIME_OF_MEAL_TIME_INVALID(50022, "Time of Meal Time is invalid"),
	PRE_ORDER_MENU_NOT_FOUND(50031, "Pre order menu is not found"),
	PRE_ORDER_MENU_ITEM_NOT_FOUND(50032, "Pre order menu item is not found"),
	PRE_ORDER_MENU_ITEM_UNAVAILABLE(50033, "Pre order menu item is unavailable"),
	INSUFFICIENT_INVENTORY_QUANTITY(50041,
			"Some items in your order are no longer available. Please review your selection and update your cart."),
	PRE_ORDER_CUTOFF_TIME_EXCEEDED(50043,
			"Some items in your order are no longer available. Please review your selection and update your cart."),
	PRE_ORDER_CAPACITY_NOT_SUFFICIENT(50044,
			"Some items in your order are no longer available. Please review your selection and update your cart."),
	ORDER_CANCELLED_EXPIRED(50045, "Order has been expired and cannot be cancelled"),
	ORDER_COLLECT_FAILED(50046, "Order collect failed"),
	ORDER_PAYMENT_FAILED(50047, "Order payment failed"),
	ORDER_EXPIRED(50048, "Order has been expired"),
	PRE_ORDER_MENU_NOT_AVAILABLE_FOR_ORDER(50049, "Pre order menu is not available for order"),
	PRE_ORDER_CANCELLED_EXPIRED(50050, "Pre order has been expired and cannot be cancelled"),
	PRE_ORDER_NOT_FOUND(50051, "Pre order is not found"),
	PRE_ORDER_EXPIRED(50052, "Pre order has been expired"),
	PRE_ORDER_ITEM_EXCEEDED_MAX_QUANTITY(50053, "Pre order item have exceeded maximum quantity allowed to purchase"),
	INVALID_PRE_ORDER_ITEM_INFO(50054, "Invalid pre order item information"),
	PRE_ORDER_ITEM_UNAVAILABLE(50055, "Pre order item is unavailable"),
	INVALID_PRE_ORDER(50056, "Invalid pre order"),
	INVALID_PRE_ORDER_AMOUNT(50057, "Invalid pre order amount"),
	LABEL_CONFIG_NOT_FOUND(50061, "Label config is not found"),
	MENU_BOARD_NOT_FOUND(50070, "Menu board is not found"),
	MENU_BOARD_SESSION_NOT_FOUND(50071, "Menu board session is not found"),
	MENU_BOARD_CODE_INVALID(50072, "Menu board code is invalid"),
	SERVICE_CHARGE_NOT_FOUND(50080, "Service charge is not found"),
	INVALID_ORDER_SERVICE_CHARGE(50081, "Invalid order service charge"),

	PAYMENT_SERVICE_GENERIC_ERROR(60000, Constants.GENERIC_ERROR_MESSAGE),
	PAYMENT_ACCESS_FORBIDDEN(60001, "Forbidden access due to permission"),
	PAYMENT_METHOD_INVALID_CREDENTIALS(60002, "Secret Key is mandatory"),
	PAYMENT_TENANT_NOT_MATCHED(60003, "Tenant is not matched"),
	PAYMENT_PROCESSOR_STRIPE_UNABLE_CREATE_SESSION(60004, "Unable to create Stripe session"),
	PAYMENT_METHOD_NOT_FOUND(60005, "Payment method not found"),
	PAYMENT_PROCESSOR_NOT_FOUND(60006, "Payment processor not found"),
	PAYMENT_PROCESSOR_MISMATCHED(60007, "Payment processor mismatched"),
	PAYMENT_SESSION_NOT_FOUND(60008, "Payment session not found"),
	PAYMENT_SESSION_CALLING(60009, "Payment session calling exception"),
	PAYMENT_PROCESSOR_STRIPE_UNABLE_CANCEL_SESSION(60010, "Unable to cancel Stripe session"),
	PAYMENT_SESSION_UPDATE_FAILED(60011, "Unable to update payment session. Please try again."),
	PAYMENT_SETTLEMENT_FAILED(60012, "Unable to settle payment session. Please try again"),
	PAYMENT_TRANSACTION_NOT_FOUND(60013, "Payment transaction not found"),
	PAYMENT_METHOD_CONFIG_REQUIRED(60014, "Payment method configuration is required"),
	PAYMENT_METHOD_NOT_ACTIVE(60015, "Payment method is not active"),
	PAYMENT_METHOD_UNABLE_CREATE(60016, "Unable to create payment method"),
	PAYMENT_METHOD_UNABLE_SETTLEMENT_STRIPE(60017, "Unable to settle Stripe payment method"),
	PAYMENT_WEBHOOK_NOT_FOUND(60018, "Stripe webhook not found"),
	PAYMENT_WEBHOOK_SIGNATURE_VERIFY_FAILED(60019, "Bad Request in not match event type"),
	PAYMENT_WEBHOOK_EVENT_UNSUPPORTED(60020, "Webhook Event is unsupported"),
	PAYMENT_TENANT_INVALID(60021, "Tenant is invalid"),
	PAYMENT_WEBHOOK_UNABLE_CREATE_WEBHOOK(60022, "Unable to create webhook endpoint"),
	PAYMENT_SESSION_EXPIRED(60023, "Payment session has expired"),
	PAYMENT_WEBHOOK_UNABLE_UPDATE_WEBHOOK(60024, "Unable to update webhook endpoint"),
	PAYMENT_SETTLEMENT_EWALLET_FAILED(60025, "Unable to settle E-Wallet session due to not ready. Try again latter."),
	PAYMENT_SETTLEMENT_EWALLET_MISMATCHED(60026,
			"Unable to settle E-Wallet session due to not matched expected amount with wallet transaction"),
	PAYMENT_TRANSACTION_REQUIRED(60027, "Payment transaction is required"),
	PAYMENT_REVERSAL_INVALID(60028, "Payment reversal is invalid"),
	PAYMENT_REVERSAL_NOT_FOUND(60029, "Payment reversal has been not found"),
	PAYMENT_CONNECTED_ACCOUNT_PROCESSOR_INVALID(60030, "Payment method processor is invalid"),
	PAYMENT_CONNECTED_ACCOUNT_ALREADY_ACTIVE(60031, "Payment account has been active"),
	PAYMENT_CONNECTED_ACCOUNT_UNSUPPORTED(60032, "Payment connected account is unsupported"),
	PAYMENT_CONNECTED_ACCOUNT_NOT_FOUND(60033, "Payment connected account has been not found"),
	PAYMENT_STRIPE_UNABLE_CREATE_CONNECTED_ACCOUNT(60035, "Unable to create connected account"),
	PAYMENT_STRIPE_CONNECT_UNABLE_CREATE_SESSION(60036, "Unable to create Stripe Connect session"),
	PAYMENT_STRIPE_UNABLE_GET_SESSION(60037, "Unable to get Stripe checkout session"),
	PAYMENT_WEBHOOK_ENDPOINT_NOT_FOUND(60038, "Payment webhook endpoint not found"),
	PAYMENT_CONNECTED_ACCOUNT_ACTIVATE_FAILED_NOT_FOUND(60039,
			"Unable to activate connected account due to pending client"),
	PAYMENT_STRIPE_CONNECT_UNABLE_GET_SESSION(60040, "Unable to get Stripe Connect session"),
	PAYMENT_OFFLINE_PROCESSOR_NOT_MATCH(60041, "Payment processor is not match"),

	NOTIFICATION_SERVICE_GENERIC_ERROR(70000, Constants.GENERIC_ERROR_MESSAGE),
	NOTIFICATION_CHANNEL_EMPTY(70001, "Notification channels is empty"),
	NOTIFICATION_TEMPLATE_DUPLICATE(70002, "Template is duplicated"),
	NOTIFICATION_TEMPLATE_NOT_FOUND(70003, "Template is not found"),
	UTILITY_SERVICE_GENERIC_ERROR(80000, Constants.GENERIC_ERROR_MESSAGE),
	UTILITY_SERVICE_IMPORT_HEADER_INVALID(80001, "Import header is invalid"),
	AUTHORIZATION_SERVICE_GENERIC_ERROR(90000, Constants.GENERIC_ERROR_MESSAGE),
	AUTHORIZATION_USER_ROLE_NOT_FOUND(90001, "User role is not found"),
	AUTHORIZATION_USER_ROLE_DELETION_NOT_ALLOW(90002, "Cannot delete default system role"),
	AUTHORIZATION_USER_ROLE_DELETION_FAILURE(90003,
			"Cannot delete user role because the role is linking with the available users"),
	AUTHORIZATION_FEATURE_NODE_NOT_FOUND(90004, "Feature node has not found"),
	AUTHORIZATION_USER_ROLE_UPDATE_INVALID(90005, "Updating the admin user role has been forbidden"),

	DEVICE_AGGREGATOR_SERVICE_GENERIC_ERROR(100000, Constants.GENERIC_ERROR_MESSAGE),
	CORRUPTED_MENU_DATA(100001, "Corrupted menu data"),

	WALLET_SERVICE_GENERIC_ERROR(110000, Constants.GENERIC_ERROR_MESSAGE),
	FUND_SOURCE_NOT_FOUND(110001, "FundSource is not found"),
	FUND_SOURCE_DISTRIBUTION_NOT_FOUND(110011, "FundSourceDistribution is not found"),
	FUND_SOURCE_DISTRIBUTION_DUPLICATED(110012, "FundSourceDistribution is duplicated"),
	FUND_SOURCE_INVALID_STATUS(110003, "FundSource is invalid status"),
	FUND_SOURCE_TOPUP_SCHEDULER_NOT_FUND(110004, "FundSource Scheduler is not found"),
	WALLET_NOT_FOUND(110021, "Wallet is not found"),
	WALLET_DUPLICATED(110022, "Wallet is duplicated"),
	WALLET_INSUFFICIENT_BALANCE(110023, "Balance is insufficient"),
	WALLET_INSUFFICIENT_AMOUNT_ENTRY(110024, "Wallet balance is insufficient"),
	WALLET_TRANSACTION_NOT_FOUND(110026, "Wallet Transaction not found"),
	WALLET_DELEGATE_SETTING_NOT_FOUND(110027, "Wallet Delegate Setting not found"),
	WALLET_DELEGATE_SETTING_DUPLICATE(110028, "Wallet Delegate Setting duplicate"),
	WALLET_SPENDING_LIMIT(110029, "Exceeded spending limit"),
	CUSTOMER_EXISTING(110030, "Customer is existing"),
	BALANCE_NOT_ZERO(110031, "Balance must be zero"),
	SUB_ACCOUNT_NOT_FOUND(110032, "SubAccount not found"),
	TOKEN_OWNER_MISMATCH(110033, "Token owner is mismatch"),
	TOPUP_SESSION_NOT_FOUND(110050, "Topup is not found"),
	TOPUP_SESSION_INVALID_STATUS(110052, "Topup Session is invalid status"),
	TOPUP_SESSION_INCORRECT_AMOUNT(110053, "Topup Session is incorrect amount"),

	GATEWAY_SERVICE_GENERIC_ERROR(120000, Constants.GENERIC_ERROR_MESSAGE),
	GATEWAY_SERVICE_FORBIDDEN_ACCESS(120001, "Forbidden Access");

	private final int value;
	private final String message;

}

class Constants {
	public static final String GENERIC_ERROR_MESSAGE = "System error occurred while processing the request";

	private Constants() {
	}
}
