/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.domain.permissions;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.FeatureNodeId;
import java.util.Optional;
import java.util.Set;
import lombok.Getter;

@Getter
public enum PacificApiPermissionKey {
	TENANT_MGMT(1_1001L, "root.tenant", FeatureNodeType.MODULE, "Tenant Management", 0, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	TENANT_MGMT_VIEW(1_1002L, "root.tenant.view", FeatureNodeType.API, "View Tenant", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	TENANT_MGMT_NEW_TENANT(1_1003L, "root.tenant.add", FeatureNodeType.API, "Onboard New Tenant", 2,
			UserType.SYSTEM_ADMIN),
	TENANT_MGMT_UPDATE(1_1004L, "root.tenant.update", FeatureNodeType.API, "Update Tenant", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	TENANT_MGMT_DELETE(1_1005L, "root.tenant.delete", FeatureNodeType.API, "Delete Tenant", 4, UserType.SYSTEM_ADMIN),

	TENANT_MGMT_SETTING_UPDATE(1_2001L, "root.tenant.setting.update", FeatureNodeType.API, "Update Tenant Settings", 5,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	TENANT_DOMAIN_MGMT_VIEW(1_3001L, "root.tenant.domain.view", FeatureNodeType.API, "View tenant domain", 6,
			UserType.SYSTEM_ADMIN),
	TENANT_DOMAIN_MGMT_ACTIVATE(1_3002L, "root.tenant.domain.activate", FeatureNodeType.API, "Activate tenant domain",
			5, UserType.SYSTEM_ADMIN),
	TENANT_DOMAIN_MGMT_DELETE(1_3003L, "root.tenant.domain.delete", FeatureNodeType.API, "Delete tenant domain", 7,
			UserType.SYSTEM_ADMIN),

	TENANT_ONBOARD_MGMT_ACTIVATE(1_4001L, "root.tenant.activate", FeatureNodeType.API, "Activate Tenant", 8,
			UserType.SYSTEM_ADMIN),
	TENANT_MGMT_CHECKLIST_UPDATE(1_4002L, "root.tenant.checklist.update", FeatureNodeType.API, "Update CheckList", 9,
			UserType.SYSTEM_ADMIN),
	TENANT_MGMT_BUSINESS_FEATURE_UPDATE(1_4003L, "root.tenant.feature.update", FeatureNodeType.API,
			"Submit Business features", 10, UserType.SYSTEM_ADMIN),

	TENANT_MGMT_TERM_CONDITION_VIEW(1_5001L, "root.tenant.terms.view", FeatureNodeType.API,
			"View Agreement Term Conditions", 11, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	TENANT_MGMT_TERM_CONDITION_UPDATE(1_5002L, "root.tenant.terms.update", FeatureNodeType.API,
			"Update Agreement Term Conditions", 12, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	TENANT_MGMT_FEEDBACK_EMAIL_VIEW(1_6001L, "root.tenant.email.view", FeatureNodeType.API,
			"View feedback email setting", 13, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	TENANT_MGMT_FEEDBACK_EMAIL_UPDATE(1_6002L, "root.tenant.email.update", FeatureNodeType.API,
			"Update feedback email setting", 14, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	USER_MGMT(2_1001L, "root.user", FeatureNodeType.MODULE, "User Management", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	USER_MGMT_VIEW(2_1002L, "root.user.view", FeatureNodeType.API, "View User List", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	USER_MGMT_ADD(2_1003L, "root.user.add", FeatureNodeType.API, "Add new user", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	USER_MGMT_DELETE(2_1004L, "root.user.delete", FeatureNodeType.API, "Delete User", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	USER_MGMT_UPDATE(2_1005L, "root.user.update", FeatureNodeType.API, "Update User", 4, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),

	CUSTOMER_MGMT(2_2001L, "root.customer", FeatureNodeType.MODULE, "Customer Management", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	CUSTOMER_MGMT_VIEW(2_2003L, "root.customer.view", FeatureNodeType.API, "View Customer List", 6,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_MGMT_ADD(2_2004L, "root.customer.add", FeatureNodeType.API, "Add Customer", 7, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	CUSTOMER_MGMT_UPDATE(2_2005L, "root.customer.update", FeatureNodeType.API, "Update Customer", 8,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_MGMT_DELETE(2_2006L, "root.customer.delete", FeatureNodeType.API, "Delete Customer", 9,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_USER_CARD_MGMT_UPDATE(2_2007L, "root.customer.card.update", FeatureNodeType.API,
			"Customer Card Management", 10, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_SUBACCOUNT_MGMT_UPDATE(2_2008L, "root.customer.subaccount.update", FeatureNodeType.API,
			"Update Sub-account", 11, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_ALLERGEN_MGMT_UPDATE(2_2010L, "root.customer.allergen.update", FeatureNodeType.API,
			"Update Customer Allergens", 12, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	GROUP_MGMT_UPDATE(2_2011L, "root.customer.group.update", FeatureNodeType.API, "Add/Update Group", 13,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	GROUP_MGMT_DELETE(2_2012L, "root.customer.group.delete", FeatureNodeType.API, "Delete Group", 14,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	GROUP_MGMT_ASSIGN(2_2013L, "root.customer.group.assign", FeatureNodeType.API, "Assign/Unassign Group", 15,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CUSTOMER_BANNED_ITEM_MGMT_UPDATE(2_2014L, "root.customer.banneditem.update", FeatureNodeType.API,
			"Update Banned Item", 16, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	ROLE_MGMT(9_1001L, "root.role", FeatureNodeType.MODULE, "Role Management", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	ROLE_MGMT_VIEW(9_1002L, "root.role.view", FeatureNodeType.API, "View Role List", 0, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	ROLE_MGMT_ADD(9_1003L, "root.role.add", FeatureNodeType.API, "Add Role", 1, UserType.SYSTEM_ADMIN),
	ROLE_MGMT_UPDATE(9_1004L, "root.role.update", FeatureNodeType.API, "Update Role", 2, UserType.SYSTEM_ADMIN),
	ROLE_MGMT_DELETE(9_1005L, "root.role.delete", FeatureNodeType.API, "Delete Role", 3, UserType.SYSTEM_ADMIN),

	//--PRODUCT--
	PRODUCT_MGMT(3_1001L, "root.catalog.product", FeatureNodeType.MODULE, "Product Management", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_VIEW(3_1002L, "root.catalog.product.view", FeatureNodeType.API, "View Product List", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_ADD(3_1003L, "root.catalog.product.add", FeatureNodeType.API, "Add Product", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	PRODUCT_MGMT_UPDATE(3_1004L, "root.catalog.product.update", FeatureNodeType.API, "Update Product", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_DELETE(3_1005L, "root.catalog.product.delete", FeatureNodeType.API, "Delete Product", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--PRODUCT CATEGORY--
	PRODUCT_MGMT_CATEGORY_VIEW(3_2001L, "root.catalog.product.category.view", FeatureNodeType.API, "View Category", 0,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_CATEGORY_ADD(3_2002L, "root.catalog.product.category.add", FeatureNodeType.API, "Add Category", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_CATEGORY_UPDATE(3_2003L, "root.catalog.product.category.update", FeatureNodeType.API,
			"Update Category", 2, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_CATEGORY_DELETE(3_2004L, "root.catalog.product.category.delete", FeatureNodeType.API,
			"Delete Category", 3, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_NUTRITION(3_2005L, "root.catalog.product.nutrition", FeatureNodeType.API, "Nutrition Management", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_HEALTHIER_CHOICE(3_2006L, "root.catalog.product.healthier.choice", FeatureNodeType.API,
			"Healthier Choice Symbol Management", 5, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRODUCT_MGMT_ALLERGEN(3_2007L, "root.catalog.product.allergen", FeatureNodeType.API, "Allergen Management", 6,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--STORE--
	STORE_MGMT(4_1001L, "root.store", FeatureNodeType.MODULE, "Store Management", 5, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STORE_MGMT_VIEW(4_1002L, "root.store.view", FeatureNodeType.API, "View Store", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STORE_MGMT_ADD(4_1003L, "root.store.add", FeatureNodeType.API, "Add Store", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STORE_MGMT_UPDATE(4_1004L, "root.store.update", FeatureNodeType.API, "Update Store", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STORE_MGMT_DELETE(4_1005L, "root.store.delete", FeatureNodeType.API, "Delete Store", 4, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),

	//--DEVICE--
	DEVICE_MGMT(4_2001L, "root.device", FeatureNodeType.MODULE, "Device Management", 6, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	DEVICE_MGMT_VIEW(4_2002L, "root.device.view", FeatureNodeType.API, "View Device", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	DEVICE_MGMT_UPDATE(4_2003L, "root.device.update", FeatureNodeType.API, "Update Device", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	DEVICE_MGMT_ASSIGN(4_2004L, "root.device.assign", FeatureNodeType.API, "Assign/Unassign Device", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--STAFF--
	STAFF_MGMT(4_3001L, "root.staff", FeatureNodeType.MODULE, "Staff Management", 7, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STAFF_MGMT_VIEW(4_3002L, "root.staff.view", FeatureNodeType.API, "View Staff", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STAFF_MGMT_ADD(4_3003L, "root.staff.add", FeatureNodeType.API, "Add Staff", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STAFF_MGMT_UPDATE(4_3004L, "root.staff.update", FeatureNodeType.API, "Update Staff", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	STAFF_MGMT_ASSIGN(4_3005L, "root.staff.assign", FeatureNodeType.API, "Assign Staff", 4, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	// 	API Not Supported Yet
	//	STAFF_MGMT_DELETE(4_3005L, "root.staff.delete", FeatureNodeType.API, "Delete Staff", 0, UserType.SYSTEM_ADMIN,
	//			UserType.BACK_OFFICE),

	//--CASH FLOAT--
	CASH_FLOAT_MGMT(4_4001L, "root.cash.float", FeatureNodeType.MODULE, "Cash Float Management", 8,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CASH_FLOAT_MGMT_VIEW(4_4002L, "root.cash.float.view", FeatureNodeType.API, "View Cash Float", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--MENU--
	MENU_MGMT(5_1001L, "root.menu", FeatureNodeType.MODULE, "Menu Management", 9, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_MGMT_VIEW(5_1002L, "root.menu.view", FeatureNodeType.API, "View Menu", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_MGMT_ADD(5_1003L, "root.menu.add", FeatureNodeType.API, "Add Menu", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_MGMT_UPDATE(5_1004L, "root.menu.update", FeatureNodeType.API, "Update Menu", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_MGMT_DELETE(5_1005L, "root.menu.delete", FeatureNodeType.API, "Delete Menu", 4, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),

	//--PRE-ORDER MENU--
	PRE_ORDER_MENU_MGMT(5_2001L, "root.pre.order.menu", FeatureNodeType.MODULE, "Pre-Order Menu Management", 10,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_VIEW(5_2002L, "root.pre.order.menu.view", FeatureNodeType.API, "View Pre-Order Menu", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_ADD(5_2003L, "root.pre.order.menu.add", FeatureNodeType.API, "Add Pre-Order Menu", 2,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_UPDATE(5_2004L, "root.pre.order.menu.update", FeatureNodeType.API, "Update Pre-Order Menu", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_DELETE(5_2005L, "root.pre.order.menu.delete", FeatureNodeType.API, "Delete Pre-Order Menu", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_ASSIGNMENT_ASSIGN(5_2006L, "root.pre.order.menu.assignment.assign", FeatureNodeType.API,
			"Assign Pre-Order Menu to Group", 5, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	PRE_ORDER_MENU_MGMT_MEALTIME(5_2008L, "root.pre.order.menu.mealtime", FeatureNodeType.API, "Mealtime Management", 7,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--ORDER--
	ORDER_MGMT(5_3001L, "root.order", FeatureNodeType.MODULE, "Order Management", 11, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	ORDER_MGMT_VIEW(5_3002L, "root.order.view", FeatureNodeType.API, "View Order", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	ORDER_MGMT_CANCEL(5_3003L, "root.order.cancel", FeatureNodeType.API, "Cancel Order", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),

	//--MENU BOARD--
	MENU_BOARD_MGMT(5_4001L, "root.mnboard", FeatureNodeType.MODULE, "Menu Board", 12, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_VIEW(5_4002L, "root.mnboard.view", FeatureNodeType.API, "View Menu Board", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_ADD(5_4003L, "root.mnboard.add", FeatureNodeType.API, "Add Menu Board", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_UPDATE(5_4004L, "root.mnboard.update", FeatureNodeType.API, "Update Menu Board", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_AUTHORIZE(5_4005L, "root.mnboard.authorize", FeatureNodeType.API, "Authorize Menu Board", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_DELETE(5_4006L, "root.mnboard.delete", FeatureNodeType.API, "Delete Menu Board", 5,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_REVOKE(5_4007L, "root.mnboard.sessions.revoke", FeatureNodeType.API, "Revoke Menu Board Session", 6,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	MENU_BOARD_MGMT_LIST_SESSIONS(5_4008L, "root.mnboard.sessions.list", FeatureNodeType.API, "List Menu Board Session",
			7, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--LABEL TEMPLATE--
	LABEL_TEMPLATE_MGMT(5_5001L, "root.label.template", FeatureNodeType.MODULE, "Label Template Management", 13,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	LABEL_TEMPLATE_MGMT_VIEW(5_5002L, "root.label.template.view", FeatureNodeType.API, "View Label Template", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	LABEL_TEMPLATE_MGMT_ADD(5_5003L, "root.label.template.add", FeatureNodeType.API, "Add Label Template", 2,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	LABEL_TEMPLATE_MGMT_UPDATE(5_5004L, "root.label.template.update", FeatureNodeType.API, "Update Label Template", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	LABEL_TEMPLATE_MGMT_DELETE(5_5005L, "root.label.template.delete", FeatureNodeType.API, "Delete Label Template", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--SERVICE CHARGE--
	SERVICE_CHARGE_MGMT(5_6001L, "root.service.charge", FeatureNodeType.MODULE, "Service Charge Management", 13,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	SERVICE_CHARGE_MGMT_VIEW(5_6002L, "root.service.charge.view", FeatureNodeType.API, "View Service Charge", 1,
			UserType.CUSTOMER, UserType.BACK_OFFICE, UserType.SYSTEM_ADMIN),
	SERVICE_CHARGE_MGMT_ADD(5_6003L, "root.service.charge.add", FeatureNodeType.API, "Add Service Charge", 2,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	SERVICE_CHARGE_MGMT_UPDATE(5_6004L, "root.service.charge.update", FeatureNodeType.API, "Update Service Charge", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	SERVICE_CHARGE_MGMT_DELETE(5_6005L, "root.service.charge.delete", FeatureNodeType.API, "Delete Service Charge", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--WALLET--
	WALLET_MGMT(11_1001L, "root.wallet", FeatureNodeType.MODULE, "Wallet Management", 12, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	WALLET_MGMT_VIEW(11_1008L, "root.wallet.view", FeatureNodeType.API, "View Wallet", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	WALLET_MGMT_ADD(11_1002L, "root.wallet.add", FeatureNodeType.API, "Add Wallet", 1, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	WALLET_MGMT_UPDATE(11_1003L, "root.wallet.update", FeatureNodeType.API, "Update Wallet", 2, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	WALLET_MGMT_DELETE(11_1004L, "root.wallet.delete", FeatureNodeType.API, "Delete Wallet", 3, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	WALLET_MGMT_ADD_CREDIT(11_1005L, "root.wallet.credit.add", FeatureNodeType.API, "Add Credit", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	WALLET_MGMT_DEDUCT_CREDIT(11_1006L, "root.wallet.credit.deduct", FeatureNodeType.API, "Deduct Credit", 5,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	WALLET_MGMT_TRANSACTION_VIEW(11_1007L, "root.wallet.transaction.view", FeatureNodeType.API, "View Transaction", 6,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--FundSource
	FUND_SOURCE_MGMT(11_2001L, "root.fundSource", FeatureNodeType.MODULE, "FundSource Management", 16,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_VIEW(11_2002L, "root.fundSource.view", FeatureNodeType.API, "View FundSource", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_ADD(11_2003L, "root.fundSource.add", FeatureNodeType.API, "Add FundSource", 2,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_UPDATE(11_2004L, "root.fundSource.update", FeatureNodeType.API, "Update FundSource", 3,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_DELETE(11_2005L, "root.fundSource.delete", FeatureNodeType.API, "Delete FundSource", 4,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_ADD_BENEFICIARY(11_2006L, "root.fundSource.beneficiary.add", FeatureNodeType.API,
			"Add Beneficiary", 5, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	FUND_SOURCE_MGMT_DELETE_BENEFICIARY(11_2007L, "root.fundSource.beneficiary.delete", FeatureNodeType.API,
			"Delete Beneficiary", 6, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--PAYMENT MANAGEMENT--
	PAYMENT_MGMT(6_1001L, "root.payment.mgmt", FeatureNodeType.MODULE, "Payment Management", 13, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	PAYMENT_MGMT_VIEW(6_1002L, "root.payment.mgmt.transactions.view", FeatureNodeType.API, "View Payment Transaction",
			0, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--DASHBOARD & REPORT--
	ANALYTICS(8_1001L, "root.analytics", FeatureNodeType.MODULE, "Analytics Management", 14, UserType.SYSTEM_ADMIN,
			UserType.BACK_OFFICE),
	ANALYTICS_DASHBOARD_VIEW(8_1002L, "root.analytics.dashboard.view", FeatureNodeType.API, "View Dashboard", 1,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	ANALYTICS_REPORT_VIEW(8_1003L, "root.analytics.report.view", FeatureNodeType.API, "View Report", 2,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	//--CONFIGURATION--
	CONFIGURATION_MGMT(99_1001L, "root.configuration", FeatureNodeType.MODULE, "Configuration Management", 15,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	CONFIGURATION_MGMT_PAYMENT_METHOD_VIEW(99_1002L, "root.configuration.payment.method.view", FeatureNodeType.API,
			"View Payment Method", 1, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_MGMT_PAYMENT_METHOD_UPDATE(99_1003L, "root.configuration.payment.method.update", FeatureNodeType.API,
			"Update Payment Method", 2, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_MGMT_PAYMENT_METHOD_ADD(99_1004L, "root.configuration.payment.method.add", FeatureNodeType.API,
			"Add Payment Method", 3, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_MGMT_PAYMENT_METHOD_DELETE(99_1005L, "root.configuration.payment.method.delete", FeatureNodeType.API,
			"Delete Payment Method", 4, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	CONFIGURATION_MGMT_DASHBOARD_VIEW(99_2001L, "root.configuration.dashboard.view", FeatureNodeType.API,
			"View Dashboard", 5, UserType.SYSTEM_ADMIN),
	CONFIGURATION_MGMT_DASHBOARD_UPDATE(99_2002L, "root.configuration.dashboard.update", FeatureNodeType.API,
			"Update Dashboard", 6, UserType.SYSTEM_ADMIN),

	CONFIGURATION_MGMT_WALLET_SETTING_VIEW(99_3001L, "root.configuration.wallet.view", FeatureNodeType.API,
			"View Wallet Setting", 7, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_MGMT_WALLET_SETTING_UPDATE(99_3002L, "root.configuration.wallet.update", FeatureNodeType.API,
			"Update Wallet Setting", 0, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),

	CONFIGURATION_MGMT_TAX_VIEW(99_4001L, "root.configuration.tax.view", FeatureNodeType.API, "View Tax", 9,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_MGMT_TAX_UPDATE(99_4002L, "root.configuration.tax.update", FeatureNodeType.API, "Update Tax", 10,
			UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE),
	CONFIGURATION_DATA_SYNC_VIEW(99_5002L, "root.configuration.datasync.view", FeatureNodeType.API, "View Data Sync",
			11, UserType.SYSTEM_ADMIN, UserType.BACK_OFFICE);

	//--END--

	;

	private final FeatureNodeId id;
	private final String nodeTitle;
	private final String nodePath;
	private final FeatureNodeType nodeType;
	private final int displayOrder;
	private final Set<UserType> allowedUserTypes;

	PacificApiPermissionKey(Long id, String nodePath, FeatureNodeType nodeType, String nodeTitle, int displayOrder,
			UserType... allowedUserTypes) {
		this.id = new FeatureNodeId(id);
		this.nodePath = nodePath;
		this.nodeType = nodeType;
		this.nodeTitle = nodeTitle;
		this.displayOrder = displayOrder;
		this.allowedUserTypes = Optional.ofNullable(allowedUserTypes)
				.map(Set::of)
				.orElseGet(Set::of);
	}

}