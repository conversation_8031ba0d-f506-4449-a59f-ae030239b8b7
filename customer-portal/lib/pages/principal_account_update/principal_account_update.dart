import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/widgets/bread_crumbs/bread_crumbs.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_update/principal_account_update.controller.dart';
import 'package:pacific_2_customer_portal/routes/app.routes.dart';
import '../../common/allergens/allergens.controller.dart';
import '../../common/allergens/allergens.dart';
import '../../common/responsive/responsive.dart';
import '../../controllers/subaccount.controller.dart';
import '../../core/constants/system_const.dart';
import '../../models/allergen.model.dart';
import '../../models/sub_account.model.dart';
import '../../common/avatar_profile/avatar_view.dart';
import '../../common/widgets/dropdown_group/group_drop_down.dart';

class PrincipalAccountUpdate extends StatefulWidget {
  final SubAccount? subAccount;
  const PrincipalAccountUpdate({super.key, this.subAccount});

  @override
  State<PrincipalAccountUpdate> createState() => _PrincipalAccountUpdateState();
}

class _PrincipalAccountUpdateState extends State<PrincipalAccountUpdate> {
  late final SubAccount? _account = widget.subAccount;
  final route = SystemConst.ROUTE;
  late final SubAccountController _subAccountController = Get.find<SubAccountController>();
  final AllergensController _allergensController = AllergensController();

  final PrincipalAccountUpdateController _principalAccountUpdateController = PrincipalAccountUpdateController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      fetchData();
    });
    super.initState();
  }

  Future<void> fetchData() async {
    await _subAccountController.getAllergensCatalog();
    if (_account?.id != null) {
      await _subAccountController.getAllergensList(_account!.subUser!.id!);
      _allergensController.allergenSelected.value =
          List<AllergenModel>.from(_subAccountController.allergenUserSelectedList.toList());
    }
    setState(() {});
  }

  @override
  void dispose() {
    _allergensController.hasChangeAllergens = false;
    _subAccountController.clearAllergenList();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Responsive(
          mobile: _renderMobile(context),
          desktop: _renderDesktop(context),
        ));
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
        padding: const EdgeInsets.fromLTRB(32, 12, 32, 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTitleView(theme),
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 0),
                    child: SizedBox(
                      width: 343,
                      child: _buildBottomButtons(theme),
                    )),
              ],
            ),
            const SizedBox(height: 4),
            Expanded(
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                //padding: const EdgeInsets.symmetric(horizontal: 16),
                child: _buildBodyDesktop(theme),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter, // 🔥 Ensures it stays at the bottom
              child: IntrinsicWidth(
                // 🔥 Ensures the container wraps content
                child: _buildStatusHeader(theme),
              ),
            ),
          ],
        ));
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: _buildTitleView(theme),
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildBody(theme),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: _buildBottomButtons(theme),
        ),
      ],
    );
  }

  Widget _buildTitleView(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BreadCrumbs(path: [
          FlutterI18n.translate(context, 'principalAccount'),
          FlutterI18n.translate(context, 'addNew'),
          FlutterI18n.translate(context, 'update'),
        ]),
        const SizedBox(height: 8),
        TitleRow(
          title: FlutterI18n.translate(context, 'updateInfo'),
          onBackPressed: () => NavigationService.pop(
            context,
            route: '/${SystemConst.ROUTE['SUB_ACCOUNT']!['UPDATE']!}',
          ),
        ),
      ],
    );
  }

  Widget _buildBody(ThemeData theme) {
    return Column(
      children: [
        _buildStatusHeader(theme),
        const SizedBox(height: 12),
        _buildOtherInformation(theme),
        const SizedBox(height: 12),
        if (_subAccountController.allergenList.toList().isNotEmpty)
          Allergens(
            allergensController: _allergensController,
          ),
      ],
    );
  }

  Widget _buildBodyDesktop(ThemeData theme) {
    double gridItemWidth = MediaQuery.of(context).size.width - 64;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 437 * gridItemWidth / 1352,
          child: _buildOtherInformation(theme),
        ),
        const SizedBox(width: 16),
        if (_subAccountController.allergenList.toList().isNotEmpty)
          Expanded(child: Allergens(allergensController: _allergensController)),
      ],
    );
  }

  Widget _buildStatusHeader(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xffD8FBDE),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Color(0xff0A5554), size: 16),
          const SizedBox(width: 8.0),
          Text(
            FlutterI18n.translate(context, 'accountCreated'),
            style: const TextStyle(color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherInformation(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            FlutterI18n.translate(context, 'otherInfo'),
            style: theme.textTheme.headlineSmall?.copyWith(color: theme.colorScheme.onSurface),
          ),
          const SizedBox(height: 12.0),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AvatarView(
                  onAvatarSelected: (fileName, imageBytes, imageFile) {
                    _principalAccountUpdateController.fileName = fileName;
                    _principalAccountUpdateController.imageBytes = imageBytes;
                    _principalAccountUpdateController.imageFile = imageFile;
                  },
                ),
                // TODO: Later phases
                // const SizedBox(height: 8.0),
                // Text(FlutterI18n.translate(context, 'group'),
                //     style: theme.textTheme.bodyMedium
                //         ?.copyWith(color: theme.colorScheme.onSurface)),
                // const SizedBox(height: 8.0),
                // GestureDetector(
                //   onTap: () {
                //     onSelectGroupPress();
                //   },
                //   child: Container(
                //     padding: const EdgeInsets.all(12.0),
                //     decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(8),
                //       border: Border.all(
                //           color: theme.colorScheme.outline.withValues(alpha: 0.48)),
                //     ),
                //     height: 56,
                //     child: Row(
                //       children: [
                //         Expanded(
                //             child: Text(_principalAccountUpdateController
                //                     .selectedGroup?.groupName ??
                //                 _account?.subUser?.userGroup?.groupName ??
                //                 "")),
                //         SizedBox(
                //           width: 16, // Set the desired width
                //           height: 16, // Set the desired height
                //           child: Icon(
                //             Icons.keyboard_arrow_down,
                //             color: arrowDownColor,
                //             size: 16,
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

// coverage:ignore-start
  Widget _buildBottomButtons(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: MainOutlinedButton(
            text: FlutterI18n.translate(context, 'skip'),
            onPressed: onSkipPress,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: MainButton(
            text: FlutterI18n.translate(context, 'saveUpdate'),
            onPressed: () => onSaveUpdatePress(context),
          ),
        ),
      ],
    );
  }

  void onSkipPress() {
    context.go('/${route['SUB_ACCOUNT']!['MAIN']!}');
  }

  Future<void> onSaveUpdatePress(BuildContext context) async {
    bool isUpdateSuccess = false;
    if (_allergensController.hasChangeAllergens) {
      bool allegesUpdated = await _allergensController.updateAllergens(_account!.subUser!.id!);
      isUpdateSuccess = allegesUpdated;
    }
    await _principalAccountUpdateController.updateAvatar();
    bool updateSuccess = await _principalAccountUpdateController.callUpdateUserProfileApi(
        _account!.id!,
        _account.subUser?.firstName,
        _account.subUser?.lastName,
        _account.subUser?.avatar?.path,
        _account.subUser?.userGroup?.id);

    if (updateSuccess) {
      isUpdateSuccess = true;
    }
    if (context.mounted && isUpdateSuccess) {
      context.go('/${route['SUB_ACCOUNT']!['MAIN']!}');
    }
  }

  void onChangeAvatarPress() {}

  Future<void> onSelectGroupPress() async {
    final result = await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (builder) {
          return GroupedDropdown(
              selectedUsergroup: _principalAccountUpdateController.selectedGroup ?? _account?.subUser?.userGroup,
              onItemSelected: (value) {
                Navigator.pop(context, value); // Close modal and return the result
              });
        });
    if (result != null) {
      setState(() {
        _principalAccountUpdateController.selectedGroup = result;
      });
    }
  }
// coverage:ignore-end
}
