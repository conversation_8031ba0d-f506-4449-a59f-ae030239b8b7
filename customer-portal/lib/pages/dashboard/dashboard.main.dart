import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/layout/app_layout.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/dashboard_navigation_item.dart';

class DashBoard extends StatelessWidget {
  const DashBoard({super.key});

  final route = SystemConst.ROUTE;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
            image: AssetImage(
              Responsive.isMobile(context)
                  ? 'assets/images/background-dashboard.png'
                  : 'assets/images/background-dashboard-desktop.png',
            ),
            fit: BoxFit.cover),
      ),
      child: AppLayout(
        backgroundColor: Colors.transparent,
        child: Responsive(
          mobile: _renderNavigationGrid(
            context,
            crossAxisCount: 2,
            padding: const EdgeInsets.all(16),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: (MediaQuery.of(context).size.width - 48) / (2 * 140),
          ),
          desktop: _renderNavigationGrid(
            context,
            crossAxisCount: 4,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            crossAxisSpacing: 24,
            childAspectRatio: (MediaQuery.of(context).size.width - 136) / (4 * 140),
            imagePathSuffix: '-desktop',
          ),
        ),
      ),
    );
  }

  Widget _renderNavigationGrid(
    BuildContext context, {
    required int crossAxisCount,
    required EdgeInsetsGeometry padding,
    required double crossAxisSpacing,
    double mainAxisSpacing = 0,
    required double childAspectRatio,
    String imagePathSuffix = '',
  }) {
    ThemeData theme = Theme.of(context);
    return GridView.count(
      physics: const AlwaysScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      primary: false,
      padding: padding,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      childAspectRatio: childAspectRatio,
      children: [
        DashboardNavigationItem(
          title: FlutterI18n.translate(context, 'principalAccount'),
          backgroundColor: theme.colorScheme.primary,
          gradientColor: const Color(0xff00308B),
          imagePath: 'assets/images/bg-dashboard-account$imagePathSuffix.png',
          route: '/${route['SUB_ACCOUNT']!['MAIN']!}',
        ),
        DashboardNavigationItem(
          title: FlutterI18n.translate(context, 'wallet'),
          backgroundColor: const Color(0xFF6EE5FF),
          gradientColor: const Color(0xff0067A0),
          imagePath: 'assets/images/bg-dashboard-wallet$imagePathSuffix.png',
          route: '/${route['WALLET']!['MAIN']!}',
        ),
        DashboardNavigationItem(
          title: FlutterI18n.translate(context, 'mealPreorder'),
          backgroundColor: theme.colorScheme.secondary,
          gradientColor: const Color(0xff420084),
          imagePath: 'assets/images/bg-dashboard-order$imagePathSuffix.png',
          route: '/${route['PRE_ORDER']!['MAIN']!}',
        ),
        DashboardNavigationItem(
          title: FlutterI18n.translate(context, 'orderHistory'),
          backgroundColor: const Color(0xFFFFD666),
          gradientColor: const Color(0xffC3790B),
          imagePath: 'assets/images/bg-dashboard-history$imagePathSuffix.png',
          route: '/${route['ORDER']!['HISTORY']!}',
        ),
      ],
    );
  }
}
