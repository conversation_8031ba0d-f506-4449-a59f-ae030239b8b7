import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/models/wallet.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class FundedWalletItem extends StatelessWidget {
  const FundedWalletItem({
    super.key,
    required this.wallet,
    this.margin = EdgeInsets.zero,
    this.padding = const EdgeInsets.all(12),
  });

  final Wallet wallet;
  final EdgeInsetsGeometry margin;
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    final expiration = wallet.walletExpiration;

    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SelectionArea(
                child: Text('#${wallet.walletId!}', style: theme.textTheme.titleSmall),
              ),
              Text('${wallet.name}',
                  style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
            ],
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: 12, bottom: expiration != null ? 12 : 0),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              formatPrice(wallet.balance!),
              style: theme.textTheme.headlineSmall!.copyWith(color: theme.colorScheme.primary),
            ),
          ),
          if (expiration?.time != null)
            RichText(
              text: TextSpan(
                text: formatPrice(expiration!.amount!),
                style: theme.textTheme.titleLarge!.copyWith(color: theme.colorScheme.error),
                children: [
                  TextSpan(
                    text: FlutterI18n.translate(context, 'willExpireOn',
                        translationParams: {'date': formatDate(expiration.expirationDate)}),
                    style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
