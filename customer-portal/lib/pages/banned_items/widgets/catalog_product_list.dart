import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/checkbox/my_checkbox.dart';
import 'package:pacific_2_customer_portal/common/widgets/text_underline/text_underline.dart';
import 'package:pacific_2_customer_portal/models/catalog_product.model.dart';

class CatalogProductList extends StatefulWidget {
  const CatalogProductList({
    super.key,
    required this.isLoading,
    required this.productList,
    required this.getProductList,
    required this.selectedProducts,
    required this.onSelectProduct,
    required this.searchText,
  });

  final bool isLoading;
  final List<CatalogProduct> productList;
  final Function() getProductList;
  final List<String> selectedProducts;
  final Function(String value, bool checked) onSelectProduct;
  final String searchText;

  @override
  State<CatalogProductList> createState() => _CatalogProductListState();
}

class _CatalogProductListState extends State<CatalogProductList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (_scrollController.position.extentAfter < 500) {
      widget.getProductList();
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    final isEmpty = widget.productList.isEmpty && !widget.isLoading;
    final searchText = widget.searchText.trim();
    final showAdd = searchText.isNotEmpty && !widget.selectedProducts.contains(searchText);

    return Container(
      width: Responsive.isDesktop(context) ? 428 : MediaQuery.of(context).size.width - 32,
      constraints: const BoxConstraints(maxHeight: 300),
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(color: theme.colorScheme.outline, blurRadius: 2),
          BoxShadow(
            color: theme.colorScheme.outline,
            offset: const Offset(-20, 20),
            blurRadius: 40,
            spreadRadius: -4,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  FlutterI18n.translate(context, 'result'),
                  style: theme.textTheme.titleMedium,
                ),
              ),
              if (widget.selectedProducts.isNotEmpty)
                Text(
                  FlutterI18n.translate(
                    context,
                    'numberSelected',
                    translationParams: {'number': widget.selectedProducts.length.toString()},
                  ),
                  style: theme.textTheme.bodySmall,
                )
            ],
          ),
          const SizedBox(height: 8),
          if (isEmpty) ...[
            Text(
              FlutterI18n.translate(context, 'noResult'),
              style: theme.textTheme.bodySmall,
            ),
            if (showAdd)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: theme.colorScheme.outline)),
                ),
                child: Row(
                  children: [
                    Text(
                      FlutterI18n.translate(context, 'add'),
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        searchText,
                        style: theme.textTheme.titleSmall,
                      ),
                    ),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () => widget.onSelectProduct(searchText, true),
                        child: TextUnderline(
                          FlutterI18n.translate(context, 'add'),
                          style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.primary),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ] else
            Flexible(
              child: ListView.builder(
                controller: _scrollController,
                shrinkWrap: true,
                itemCount: widget.productList.length + 1,
                itemBuilder: (ctx, index) {
                  if (index == widget.productList.length) {
                    return widget.isLoading
                        ? const Center(
                            child: CircularProgressIndicator(),
                          )
                        : const SizedBox.shrink();
                  }

                  final item = widget.productList[index];
                  final checked = widget.selectedProducts.contains(item.name);

                  return _renderProductRow(context, item, checked);
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _renderProductRow(BuildContext context, CatalogProduct item, bool checked) {
    ThemeData theme = Theme.of(context);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onSelectProduct(item.name!, !checked),
        child: Ink(
          height: 44,
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: theme.colorScheme.outline)),
          ),
          child: Row(
            children: [
              Expanded(child: Text(item.name ?? '-')),
              MyCheckBox(
                checked: checked,
                onChanged: (_) => widget.onSelectProduct(item.name!, !checked),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
