// coverage:ignore-file
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/controllers/auth.controller.dart';
import 'package:pacific_2_customer_portal/core/services/toast.service.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/my_account_settings.controller.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/date_time_format_settings.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/noti_settings.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/widgets/security_settings.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/services/utility.service.dart';
import 'package:universal_html/html.dart' as html;

class MyAccountSettings extends StatefulWidget {
  const MyAccountSettings({super.key});

  @override
  State<MyAccountSettings> createState() => _MyAccountSettingsState();
}

class _MyAccountSettingsState extends State<MyAccountSettings> {
  final MyAccountSettingsController _myAccountSettingsController =
      Get.put(MyAccountSettingsController(UserService(), UtilityService()));
  final AuthController _authController = Get.find<AuthController>();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _myAccountSettingsController.getMetaData();
      _myAccountSettingsController.getUserPreference();
      _authController.getChangePasswordUrl();
    });
    super.initState();
  }

  Future<void> onSaveDateTimeFormat(BuildContext context) async {
    final success = await _myAccountSettingsController.saveDateTimeFormat();
    if (context.mounted && success) {
      ToastService(context).success(FlutterI18n.translate(context, 'settingsSaved'));
    }
  }

  void changePassword() {
    String url = _authController.getChangePasswordUrl();
    if (kIsWeb) {
      html.window.location.href = url;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: Responsive.isDesktop(context) ? 0 : 16),
        children: [
          DateTimeFormatSettings(
            pref: _myAccountSettingsController.dateTimeFormatPreference.value,
            onSavePref: () {
              onSaveDateTimeFormat(context);
            },
            onRefresh: () {
              _myAccountSettingsController.dateTimeFormatPreference.refresh();
            },
          ),
          const SizedBox(height: 16),
          NotiSettings(
            paymentSettings: _myAccountSettingsController.paymentNotiPreference.value,
            orderSettings: _myAccountSettingsController.orderNotiPreference.value,
            walletSettings: _myAccountSettingsController.walletNotiPreference.value,
            onSavePref: _myAccountSettingsController.saveNotiSettings,
          ),
          const SizedBox(height: 16),
          SecuritySettings(
            onChangePassword: changePassword,
            mfaPref: _myAccountSettingsController.mfaPreference.value,
            onSavePref: _myAccountSettingsController.saveMFASettings,
          )
        ],
      ),
    );
  }
}
