import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/widgets/status_badge/status_badge.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';

class OrderHistoryTableRow extends StatelessWidget {
  const OrderHistoryTableRow({
    super.key,
    required this.item,
    required this.onPressDetails,
    required this.columnFlexWidths,
  });

  final OrderHistoryItem item;
  final Function(OrderHistoryItem item) onPressDetails;
  final List<int> columnFlexWidths;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    int itemCount = item.lineItems!.fold(0, (prev, cur) => prev + (cur.quantity ?? 0));

    return Container(
      height: 68,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: theme.colorScheme.outline)),
      ),
      child: Row(
        children: [
          Expanded(
            flex: columnFlexWidths[0],
            child: Padding(
              padding: const EdgeInsets.only(left: 16),
              child: SelectionArea(
                child: Text(
                  item.id ?? '-',
                  style: theme.textTheme.titleSmall,
                ),
              ),
            ),
          ),
          Expanded(
            flex: columnFlexWidths[1],
            child: Text(
              item.customerName ?? '-',
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: columnFlexWidths[2],
            child: Text(
              FlutterI18n.translate(context, OrderType.fromString(item.type).labelKey),
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: columnFlexWidths[3],
            child: Row(children: [
              StatusBadge(status: OrderStatus.fromString(item.status)),
            ]),
          ),
          Expanded(
            flex: columnFlexWidths[4],
            child: Text(
              item.collectionDate ?? '-',
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: columnFlexWidths[5],
            child: Text(
              itemCount.toString(),
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            flex: columnFlexWidths[6],
            child: Row(children: [
              StatusBadge(status: PaymentStatus.fromString(item.paymentStatus)),
            ]),
          ),
          Expanded(
            flex: columnFlexWidths[7],
            child: Center(
              child: IconButton(
                onPressed: () => onPressDetails(item),
                icon: SvgPicture.asset('assets/images/ic-info-button.svg'),
                padding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
