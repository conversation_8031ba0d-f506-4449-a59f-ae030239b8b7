// coverage:ignore-file
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/bread_crumbs/bread_crumbs.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/filter/filter_dialog.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/cart.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/controllers/route.controller.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/common/widgets/product_options/product_options.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/cart_account_item_single_checkout.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/cart_account_list.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/cart_account_summary.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/empty_view.dart';
import 'package:pacific_2_customer_portal/routes/app.routes.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';
import 'package:pacific_2_customer_portal/utils/order_utils.dart';

class PreOrderCart extends StatefulWidget {
  const PreOrderCart({super.key});

  @override
  State<PreOrderCart> createState() => _PreOrderCartState();
}

class _PreOrderCartState extends State<PreOrderCart> {
  late final _cartController = Get.find<CartController>();
  late final _loadingController = Get.find<LoadingController>();
  late final _appController = Get.find<AppController>();
  late final _routeController = Get.find<RouteController>();

  StreamSubscription? activeRouteListener;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cartController.selectFirstAccount();
      waitForAuth(_appController, () {
        refreshCart();
      });

      activeRouteListener = _routeController.activeRoute.listen((route) {
        if (route == '/${SystemConst.ROUTE['PRE_ORDER']!['CART']!}') {
          refreshCart();
        }
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    _cartController.clearCheckedAccount();
    _cartController.selectedAccount.value = null;
    activeRouteListener?.cancel();
    super.dispose();
  }

  void refreshCart() {
    if (_cartController.cart.value.isNotEmpty) {
      _cartController.refreshCart();
    }
  }

  Future<void> updateOptions(BuildContext context, CartItem cartItem) async {
    final catalogProduct = await _cartController.getCatalogProduct(cartItem.item.product?.id ?? '');

    if (context.mounted) {
      showFilterDialog(
        context,
        child: ProductOptions(
          item: cartItem.item,
          catalogProduct: catalogProduct,
          currentCartItem: cartItem,
          onAddToCart: (newCartItem) {
            _cartController.updateOptions(cartItem, newCartItem);
          },
          onSaveNote: (newCartItem) {
            _cartController.updateOptions(cartItem, newCartItem);
          },
          onClose: () => Navigator.of(context).pop(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Responsive(
        mobile: _renderMobile(context),
        desktop: _renderDesktop(context),
      ),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._renderTitleRow(context),
        Expanded(
          child: _cartController.selectedAccount.value == null
              ? const EmptyCartView()
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: SingleChildScrollView(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: theme.colorScheme.outline),
                            ),
                            clipBehavior: Clip.hardEdge,
                            child: _renderCartAccountDetails(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 24),
                      Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: theme.colorScheme.outline),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _renderCartAccountList(),
                                _renderSummary(context),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ],
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._renderTitleRow(context),
        _renderCartAccountList(),
        if (_cartController.selectedAccount.value == null)
          const Expanded(child: EmptyCartView())
        else ...[
          _renderSummary(context),
          Divider(
            height: 12,
            thickness: 12,
            color: theme.colorScheme.surface,
          ),
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: _renderCartAccountDetails(),
            ),
          ),
        ],
        // Container(
        //   padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
        //   decoration: BoxDecoration(
        //     border: Border(top: BorderSide(color: theme.colorScheme.outline)),
        //   ),
        //   child: _renderBottomContent(context),
        // ),
      ],
    );
  }

  Widget _renderClearCartButton(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return MainButton(
      text: FlutterI18n.translate(context, 'clearCart'),
      onPressed: () {
        _loadingController.showAlertDialog(
          context,
          title: FlutterI18n.translate(context, 'clearCart'),
          message: FlutterI18n.translate(context, 'clearCartMessage'),
          positiveText: 'Yes',
          onPositivePress: () {
            _cartController.clearCart();
          },
          onNegativePress: () {},
        );
      },
      width: 84,
      height: 32,
      textStyle: theme.textTheme.labelSmall,
      textColor: theme.colorScheme.onErrorContainer,
      color: theme.colorScheme.error.withValues(alpha: 0.08),
      disabled: _cartController.cart.value.isEmpty,
    );
  }

  List<Widget> _renderTitleRow(BuildContext context) {
    final double paddingHor = Responsive.isDesktop(context) ? 32 : 16;
    final double paddingBot = Responsive.isDesktop(context) ? 22 : 0;
    return [
      Padding(
        padding: EdgeInsets.fromLTRB(paddingHor, 12, paddingHor, 4),
        child: BreadCrumbs(path: [
          FlutterI18n.translate(context, 'createPreorder'),
          FlutterI18n.translate(context, 'cart'),
        ]),
      ),
      Padding(
        padding: EdgeInsets.symmetric(horizontal: paddingHor),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TitleRow(
              title: FlutterI18n.translate(context, 'cart'),
              onBackPressed: () => NavigationService.pop(
                context,
                route: '/${SystemConst.ROUTE['PRE_ORDER']!['MAIN']!}',
              ),
            ),
            _renderClearCartButton(context),
          ],
        ),
      ),
      SizedBox(height: paddingBot),
    ];
  }

  Widget _renderCartAccountList() {
    return CartAccountList(
      data: _cartController.cart.value.keys.toList(),
      selectedCartAccount: _cartController.selectedAccount.value,
      onSelectCartAccount: _cartController.onSelectAccount,
    );
  }

  Widget _renderSummary(BuildContext context) {
    return CartAccountSummary(
      totalItems: getTotalItemCountByAccount(_cartController.cart.value[_cartController.selectedAccount.value]!),
      subTotal: getCartSubTotal(
          {_cartController.selectedAccount.value!: _cartController.cart.value[_cartController.selectedAccount.value]!}),
      canCheckout: !_cartController.isCartAccountUnavailable(_cartController.selectedAccount.value!),
      showDelete: _cartController.cart.value.length > 1,
      onProceedPayment: () {
        context.push(
          '/${SystemConst.ROUTE['PRE_ORDER']!['CHECKOUT']!}',
          extra: [_cartController.selectedAccount.value!.toJson()],
        );
      },
      onDeleteAccount: _cartController.deleteSelectedAccount,
    );
  }

  Widget _renderCartAccountDetails() {
    return CartAccountItemSingleCheckout(
      account: _cartController.selectedAccount.value!,
      data: _cartController.cart.value[_cartController.selectedAccount.value]!,
      onRemoveItem: _cartController.removeFromCart,
      onUpdateOptions: (cItem) => updateOptions(context, cItem),
      isCartDateUnavailable: (cartDate) =>
          _cartController.isCartDateUnavailable(_cartController.selectedAccount.value!, cartDate),
    );
  }
}
