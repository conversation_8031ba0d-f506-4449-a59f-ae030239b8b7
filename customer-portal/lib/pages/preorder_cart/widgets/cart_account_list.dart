import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';

class CartAccountList extends StatelessWidget {
  const CartAccountList({
    super.key,
    required this.data,
    required this.selectedCartAccount,
    required this.onSelectCartAccount,
  });

  final List<CartAccount> data;
  final CartAccount? selectedCartAccount;
  final Function(CartAccount value) onSelectCartAccount;

  Widget _renderCartAccount(ThemeData theme, CartAccount cAccount, bool isSelected) {
    return InkWell(
      onTap: () => onSelectCartAccount(cAccount),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: isSelected ? 9 : 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: isSelected ? null : Border.all(color: theme.colorScheme.outline),
          color: isSelected ? theme.colorScheme.primary.withValues(alpha: 0.16) : theme.colorScheme.surfaceContainer,
        ),
        child: Center(
          child: Text(
            cAccount.profile.getFullName(),
            style: theme.textTheme.bodyLarge!.copyWith(
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 58,
      child: ListView.separated(
        itemBuilder: (context, index) {
          CartAccount cAccount = data[index];
          bool isSelected = selectedCartAccount == cAccount;
          return _renderCartAccount(theme, cAccount, isSelected);
        },
        separatorBuilder: (_, __) => const SizedBox(width: 16),
        itemCount: data.length,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}
