import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';

class CategoryList extends StatelessWidget {
  const CategoryList({
    super.key,
    required this.data,
    required this.selectedCategory,
    required this.onSelectCategory,
    required this.onSelectAll,
  });

  final List<Category> data;
  final List<Category> selectedCategory;
  final Function(Category value) onSelectCategory;
  final Function() onSelectAll;

  Widget _renderCategory(ThemeData theme, Category category, bool isSelected) {
    return InkWell(
      onTap: () => onSelectCategory(category),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: isSelected ? 9 : 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: isSelected ? null : Border.all(color: theme.colorScheme.outline),
          color: isSelected ? theme.colorScheme.primary.withValues(alpha: 0.16) : theme.colorScheme.surface,
        ),
        child: Center(
          child: Text(
            '${category.name}',
            style: theme.textTheme.bodyMedium!.copyWith(
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
            ),
          ),
        ),
      ),
    );
  }

  Widget _renderAll(BuildContext context, ThemeData theme, bool isSelected) {
    return InkWell(
      onTap: () => onSelectAll(),
      borderRadius: BorderRadius.circular(50),
      child: Container(
        height: 30,
        padding: EdgeInsets.symmetric(horizontal: isSelected ? 9 : 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: isSelected ? null : Border.all(color: theme.colorScheme.primary),
          color: isSelected ? theme.colorScheme.primary.withValues(alpha: 0.16) : theme.colorScheme.surface,
        ),
        child: Center(
          child: Text(
            FlutterI18n.translate(context, 'allItem'),
            style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.primary),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      width: double.infinity,
      height: 54,
      color: theme.colorScheme.surface,
      child: ListView.separated(
        itemBuilder: (context, index) {
          if (index == 0) {
            return _renderAll(context, theme, selectedCategory.isEmpty);
          }

          Category category = data[index - 1];
          bool isSelected = selectedCategory.contains(category);
          return _renderCategory(theme, category, isSelected);
        },
        separatorBuilder: (_, __) => const SizedBox(width: 12),
        itemCount: data.length + 1,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}
