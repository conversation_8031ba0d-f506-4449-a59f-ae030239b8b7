import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/status_badge/status_badge.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';

class PrincipalAccountItem extends StatefulWidget {
  const PrincipalAccountItem({
    super.key,
    required this.item,
    required this.onPressDelete,
    required this.onPressDetails,
    required this.onPressEdit,
    required this.onPressOrder,
  });

  final SubAccount item;
  final Function() onPressDelete;
  final Function() onPressDetails;
  final Function() onPressEdit;
  final Function() onPressOrder;

  @override
  State<PrincipalAccountItem> createState() => _PrincipalAccountItemState();
}

class _PrincipalAccountItemState extends State<PrincipalAccountItem> with AutomaticKeepAliveClientMixin {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    ThemeData theme = Theme.of(context);

    final isPending = widget.item.subAccountStatus == SubAccountStatus.pending.status;

    return InkWell(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Ink(
          color: isExpanded ? theme.colorScheme.surface : Colors.white,
          child: Column(
            children: [
              ListTile(
                contentPadding: const EdgeInsets.only(left: 6, right: 12),
                leading: IconButton(
                  icon: SizedBox(
                      width: 24, // Set the desired width
                      height: 24, // Set the desired height
                      child: isExpanded
                          ? SvgPicture.asset('assets/images/circle-up.svg')
                          : SvgPicture.asset('assets/images/circle-down.svg')),
                  onPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                ),
                title: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Opacity(
                      opacity: isPending ? 0.4 : 1,
                      child: Row(
                        children: [
                          UserAvatar(url: widget.item.subUser?.avatar?.url, size: 40),
                          const SizedBox(width: 8),
                          Expanded(
                              child:
                                  Text(widget.item.subUser?.getFullName() ?? "-", style: theme.textTheme.bodyMedium)),
                        ],
                      ),
                    ),
                    if (isPending) ...[
                      const SizedBox(height: 2),
                      Text(
                        FlutterI18n.translate(context, 'awaitLinkAccountAccept'),
                        style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.error),
                      ),
                    ],
                  ],
                ),
                trailing: StatusBadge(
                  status: SubAccountStatus.fromString(widget.item.subAccountStatus),
                ),
              ),
              if (isExpanded)
                Column(children: [
                  Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.0), color: Colors.white),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(FlutterI18n.translate(context, 'group'),
                                style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                            const SizedBox(height: 2),
                            Text(widget.item.subUser?.userGroup?.groupName ?? "-", style: theme.textTheme.bodyMedium),
                            const SizedBox(height: 12),
                            Text(FlutterI18n.translate(context, 'externalID'),
                                style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                            const SizedBox(height: 2),
                            Text(widget.item.subUser?.externalId ?? "-", style: theme.textTheme.bodyMedium),
                          ],
                        ),
                      )),
                  Container(
                    padding: const EdgeInsets.only(left: 12, right: 12, top: 0, bottom: 12),
                    child: Row(
                      children: [
                        if (!isPending)
                          MainButton(
                            height: 32,
                            width: 118,
                            color: theme.colorScheme.primary.withValues(alpha: 0.08),
                            text: FlutterI18n.translate(context, 'viewPreorder'),
                            textColor: theme.colorScheme.primary,
                            textStyle: theme.textTheme.labelSmall,
                            onPressed: widget.onPressOrder,
                          ),
                        const Expanded(child: SizedBox.shrink()),
                        IconButton(
                          onPressed: widget.onPressDelete,
                          icon: SvgPicture.asset('assets/images/ic-delete.svg'),
                          padding: EdgeInsets.zero,
                        ),
                        if (!isPending) ...[
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: widget.onPressDetails,
                            icon: SvgPicture.asset('assets/images/ic-info-account.svg'),
                            padding: EdgeInsets.zero,
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: widget.onPressEdit,
                            icon: SvgPicture.asset('assets/images/ic-edit.svg'),
                            padding: EdgeInsets.zero,
                          ),
                        ]
                      ],
                    ),
                  )
                ]),
              Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12), // Left spacing of 10 pixels
                  height: 1,
                  color: theme.colorScheme.outline.withValues(alpha: 0.24) // Color of the line
                  )
            ],
          )),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
