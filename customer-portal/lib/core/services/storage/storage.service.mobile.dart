// coverage:ignore-file
import "dart:convert";

import "package:flutter_secure_storage/flutter_secure_storage.dart";
import "package:pacific_2_customer_portal/core/services/storage/storage_key.dart";
import "package:pacific_2_customer_portal/models/cart.model.dart";

class StorageService {
  late FlutterSecureStorage storage;

  StorageService() {
    storage = const FlutterSecureStorage(aOptions: AndroidOptions(encryptedSharedPreferences: true));
  }

  Future<void> clearAll() async {
    await storage.deleteAll();
  }

  Future<void> setToken(dynamic token) async {
    await storage.write(key: StorageKey.keycloakToken.key, value: jsonEncode(token));
  }

  Future<dynamic> getToken() async {
    var token = await storage.read(key: StorageKey.keycloakToken.key);
    return token != null ? jsonDecode(token) : null;
  }

  Future<void> setCart(CartData cart) async {}

  Future<CartData?> getCart() async => null;

  void listenToken(Function(dynamic token) callback) {
    // Not implemented for mobile
  }
}
