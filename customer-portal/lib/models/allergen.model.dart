// coverage:ignore-file
class AllergenModel {
  String? id;
  String? allergenId;
  String? tenantId;
  String? name;
  String? description;
  bool? restricted;

  AllergenModel(
      {this.id,
      this.allergenId,
      this.tenantId,
      this.name,
      this.description,
      this.restricted});

  AllergenModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    allergenId = json['allergenId'];
    tenantId = json['tenantId'];
    name = json['name'];
    description = json['description'];
    restricted = json['restricted'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['allergenId'] = allergenId;
    data['tenantId'] = tenantId;
    data['name'] = name;
    data['description'] = description;
    data['restricted'] = restricted;
    return data;
  }
}
