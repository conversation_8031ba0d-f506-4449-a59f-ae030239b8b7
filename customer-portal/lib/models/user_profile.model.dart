// coverage:ignore-file
import 'package:pacific_2_customer_portal/models/common.model.dart';

class UserProfile {
  String? id;
  String? alternativeId;
  String? ssoId;
  String? externalId;
  String? firstName;
  String? lastName;
  String? email;
  String? userType;
  String? realmId;
  Image? avatar;
  String? userStatus;
  String? phoneNumber;
  int? totalSubAccounts;
  int? totalSponsors;
  UserGroup? userGroup;
  List<String>? userNonCompletedActions;
  String? defaultDateFormat;
  String? defaultTimeFormat;
  DefaultTimezone? defaultTimezone;
  List<Permissions>? permissions;
  int? createdAt;
  int? updatedAt;
  bool? isTrustedSourceAccess;
  bool? isAlternativeUser;

  UserProfile({
    this.id,
    this.alternativeId,
    this.ssoId,
    this.externalId,
    this.firstName,
    this.lastName,
    this.email,
    this.userType,
    this.realmId,
    this.avatar,
    this.userStatus,
    this.phoneNumber,
    this.totalSubAccounts,
    this.totalSponsors,
    this.userGroup,
    this.userNonCompletedActions,
    this.defaultDateFormat,
    this.defaultTimeFormat,
    this.defaultTimezone,
    this.permissions,
    this.createdAt,
    this.updatedAt,
    this.isTrustedSourceAccess,
    this.isAlternativeUser,
  });

  @override
  bool operator ==(other) {
    if (other is! UserProfile) {
      return false;
    }
    return id == other.id;
  }

  @override
  int get hashCode => id.hashCode;

  UserProfile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    alternativeId = json['id'];
    ssoId = json['ssoId'];
    externalId = json['externalId'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    email = json['email'];
    userType = json['userType'];
    realmId = json['realmId'];
    avatar = json['avatar'] != null ? Image.fromJson(json['avatar']) : null;
    userStatus = json['userStatus'];
    phoneNumber = json['phoneNumber'];
    totalSubAccounts = json['totalSubAccounts'];
    totalSponsors = json['totalSponsors'];
    userGroup = json['userGroup'] != null ? UserGroup.fromJson(json['userGroup']) : null;
    userNonCompletedActions = json['userNonCompletedActions']?.cast<String>();
    defaultDateFormat = json['defaultDateFormat'];
    defaultTimeFormat = json['defaultTimeFormat'];
    defaultTimezone = json['defaultTimezone'] != null ? DefaultTimezone.fromJson(json['defaultTimezone']) : null;
    if (json['permissions'] != null) {
      permissions = <Permissions>[];
      json['permissions'].forEach((v) {
        permissions!.add(Permissions.fromJson(v));
      });
    }
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    isTrustedSourceAccess = json['isTrustedSourceAccess'];
    isAlternativeUser = json['isAlternativeUser'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['alternativeId'] = alternativeId;
    data['ssoId'] = ssoId;
    data['externalId'] = externalId;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['email'] = email;
    data['userType'] = userType;
    data['realmId'] = realmId;
    if (avatar != null) {
      data['avatar'] = avatar!.toJson();
    }
    data['userStatus'] = userStatus;
    data['phoneNumber'] = phoneNumber;
    data['totalSubAccounts'] = totalSubAccounts;
    data['totalSponsors'] = totalSponsors;
    if (userGroup != null) {
      data['userGroup'] = userGroup!.toJson();
    }
    data['userNonCompletedActions'] = userNonCompletedActions;
    data['defaultDateFormat'] = defaultDateFormat;
    data['defaultTimeFormat'] = defaultTimeFormat;
    if (defaultTimezone != null) {
      data['defaultTimezone'] = defaultTimezone!.toJson();
    }
    if (permissions != null) {
      data['permissions'] = permissions!.map((v) => v.toJson()).toList();
    }
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['isTrustedSourceAccess'] = isTrustedSourceAccess;
    data['isAlternativeUser'] = isAlternativeUser;
    return data;
  }

  String getFullName() {
    if (firstName == null && lastName == null) return '-';
    return '${firstName ?? ''} ${lastName ?? ''}';
  }
}

class UserGroup {
  String? id;
  String? tenantId;
  String? path;
  String? groupName;
  UserGroup? parent;
  String? description;
  List<UserGroup>? children;
  int? createdAt;
  int? updatedAt;

  UserGroup(
      {this.id,
      this.tenantId,
      this.path,
      this.groupName,
      this.parent,
      this.description,
      this.children,
      this.createdAt,
      this.updatedAt});

  UserGroup.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    path = json['path'];
    groupName = json['groupName'];
    parent = json['parent'] != null ? UserGroup.fromJson(json['parent']) : null;
    description = json['description'];
    children = (json['children'] as List<dynamic>?)?.map((child) => UserGroup.fromJson(child)).toList();
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    data['path'] = path;
    data['groupName'] = groupName;
    if (parent != null) {
      data['parent'] = parent!.toJson();
    }
    data['description'] = description;
    if (children != null) {
      data['children'] = children!.map((v) => v.toJson()).toList();
    }
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class DefaultTimezone {
  String? zoneId;
  String? gtmOffset;
  String? displayName;

  DefaultTimezone({this.zoneId, this.gtmOffset, this.displayName});

  DefaultTimezone.fromJson(Map<String, dynamic> json) {
    zoneId = json['zoneId'];
    gtmOffset = json['gtmOffset'];
    displayName = json['displayName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['zoneId'] = zoneId;
    data['gtmOffset'] = gtmOffset;
    data['displayName'] = displayName;
    return data;
  }
}

class Permissions {
  String? tenantId;
  String? userId;
  String? userRoleId;
  String? permissionStatus;

  Permissions({this.tenantId, this.userId, this.userRoleId, this.permissionStatus});

  Permissions.fromJson(Map<String, dynamic> json) {
    tenantId = json['tenantId'];
    userId = json['userId'];
    userRoleId = json['userRoleId'];
    permissionStatus = json['permissionStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tenantId'] = tenantId;
    data['userId'] = userId;
    data['userRoleId'] = userRoleId;
    data['permissionStatus'] = permissionStatus;
    return data;
  }
}
