// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';

import '../../../controllers/subaccount.controller.dart';

class GroupedDropdown extends StatefulWidget {
  final UserGroup? selectedUsergroup;
  final ValueChanged<UserGroup> onItemSelected;
  const GroupedDropdown({super.key, required this.onItemSelected, this.selectedUsergroup});

  @override
  State<GroupedDropdown> createState() => _GroupedDropdownState();
}

class _GroupedDropdownState extends State<GroupedDropdown> {
  final SubAccountController _subAccountController = Get.find<SubAccountController>();
  late UserGroup? selectedValue = widget.selectedUsergroup;
  RxList<UserGroup> groupList = <UserGroup>[].obs;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      fetchData();
    });
  }

  Future<void> fetchData() async {
    try {
      if (_subAccountController.groupList.isEmpty) {
        await _subAccountController.getGroup();
      }

      // Ensure widget is still mounted before calling setState
      if (mounted) {
        setState(() {
          groupList = _subAccountController.groupList;
        });
      }
    } catch (e) {
      print('Error fetching data: $e'); // Handle errors gracefully
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    double mWidth = MediaQuery.of(context).size.width;
    double mHeight = MediaQuery.of(context).size.height;
    return Obx(() {
      return Container(
        width: mWidth,
        height: mHeight * 2 / 3,
        decoration: const BoxDecoration(
            color: Colors.white, // Background color
            borderRadius: BorderRadius.all(Radius.circular(8.0))),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
          child: Column(
            children: [
              Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  height: 34,
                  child: TextField(
                    onChanged: _filterList,
                    decoration: InputDecoration(
                      prefixIcon: SizedBox(
                        width: 16, // Set the desired width
                        height: 16, // Set the desired height
                        child: SvgPicture.asset(
                          'assets/images/ic_search.svg',
                          fit: BoxFit.none, // Ensure the icon doesn't scale
                        ),
                      ),
                      hintText: FlutterI18n.translate(context, 'groupSearchPlaceholder'),
                      hintStyle: theme.textTheme.bodyMedium?.copyWith(color: const Color(0xffA1A9B8)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  )),
              const SizedBox(height: 6),
              // Display the grouped list below the dropdown
              Expanded(
                  child: groupList.isEmpty
                      ? const Center(child: CircularProgressIndicator())
                      : SingleChildScrollView(child: groupListWidget(context, theme))),
            ],
          ),
        ),
      );
    });
  }

  Widget groupListWidget(BuildContext context, ThemeData theme) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(groupList.length, (index) {
          return ListTile(
            title: Column(
              children: [
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Container(
                      height: 26,
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      color: selectedValue == groupList[index] ? theme.colorScheme.outline : theme.colorScheme.primary,
                      child: Text(
                        groupList[index].groupName ?? "",
                        style: theme.textTheme.titleSmall?.copyWith(color: Colors.white),
                      ),
                    )),
                if (groupList[index].children != null)
                  Column(crossAxisAlignment: CrossAxisAlignment.start, children: _buildGroup(context, index))
              ],
            ),
            onTap: () {
              setState(() {
                selectedValue = groupList[index];
                widget.onItemSelected(groupList[index]);
              });
            },
          );
        }));
  }

  List<Widget> _buildGroup(BuildContext context, int index) {
    ThemeData theme = Theme.of(context);
    return List.generate(groupList[index].children!.length, (index1) {
      return ListTile(
          title: Column(
            children: [
              Container(
                width: double.infinity,
                height: 26,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                color: selectedValue == groupList[index].children?[index1]
                    ? theme.colorScheme.outline
                    : theme.colorScheme.onSurface,
                child: Text(
                  groupList[index].children?[index1].groupName ?? "",
                  style: theme.textTheme.titleSmall?.copyWith(color: Colors.white),
                ),
              ),
              if (groupList[index].children?[index1].children != null)
                Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(groupList[index].children![index1].children!.length, (index2) {
                      return ListTile(
                        title: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                            height: 34,
                            width: double.infinity,
                            color: selectedValue == groupList[index].children![index1].children![index2]
                                ? theme.colorScheme.outline
                                : Colors.white,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 3, // Adjust the width of the dot
                                  height: 3, // Adjust the height of the dot
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: theme.colorScheme.onSurface, // Same color as text
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(groupList[index].children![index1].children![index2].groupName ?? "",
                                    style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface))
                              ],
                            )),
                        onTap: () {
                          setState(() {
                            selectedValue = groupList[index].children![index1].children![index2];
                            widget.onItemSelected(groupList[index].children![index1].children![index2]);
                          });
                        },
                      );
                    }))
            ],
          ),
          onTap: () {
            setState(() {
              selectedValue = groupList[index].children![index1];
              widget.onItemSelected(groupList[index].children![index1]);
            });
          });
    });
  }

  void _filterList(String query) {
    if (query.isNotEmpty) {
      setState(() {
        groupList.value = filterUserGroups(_subAccountController.groupList, query);
      });
    } else {
      setState(() {
        groupList = _subAccountController.groupList;
      });
    }
  }

  List<UserGroup> filterUserGroups(List<UserGroup> groups, String query) {
    query = query.toLowerCase();

    // Recursively filter groups and their children
    List<UserGroup> filteredGroups = groups.where((group) {
      // Check if the group matches the query
      final isGroupMatch = (group.groupName?.toLowerCase().contains(query) ?? false) ||
          (group.id?.toLowerCase().contains(query) ?? false);

      // Recursively filter the children of this group
      final filteredChildren = filterUserGroups(group.children ?? [], query);

      // Keep the group if it matches OR if it has matching children
      if (isGroupMatch || filteredChildren.isNotEmpty) {
        // Update the group's children to include only the filtered ones
        group.children = filteredChildren;
        return true;
      }
      return false;
    }).toList();

    return filteredGroups;
  }
}
