import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pacific_2_customer_portal/models/notification.model.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';
import 'package:timeago/timeago.dart' as timeago;

class NotificationItemView extends StatelessWidget {
  final NotificationItem data;
  final Function(String id) onMarkNotificationRead;
  final Function(String id) onDeleteNotification;

  NotificationItemView(
      {super.key, required this.data, required this.onMarkNotificationRead, required this.onDeleteNotification}) {
    final isRead = data.read ?? false;
    if (!isRead) {
      _typeList.add(NotificationItemActionType.read);
    }
    _typeList.add(NotificationItemActionType.delete);
  }

  final List<NotificationItemActionType> _typeList = [];

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    final isRead = data.read ?? false;

    return Opacity(
      opacity: isRead ? 0.4 : 1,
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainer,
          border: Border(bottom: BorderSide(color: theme.colorScheme.outline)),
        ),
        padding: const EdgeInsets.fromLTRB(16, 16, 8, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(
                  isRead ? data.actionType!.type.icon : data.actionType!.type.iconDot,
                  width: 32,
                ),
                const SizedBox(width: 8),
                Text(FlutterI18n.translate(context, data.actionType?.key ?? '-'), style: theme.textTheme.labelSmall),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _timeAgo(data),
                    style: theme.textTheme.bodySmall!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  ),
                ),
                PopupMenuButton(
                  icon: const Icon(Icons.more_vert, size: 20),
                  color: theme.colorScheme.onPrimaryContainer,
                  position: PopupMenuPosition.under,
                  onSelected: (value) {
                    if (value == NotificationItemActionType.read) {
                      onMarkNotificationRead(data.id ?? "");
                    } else if (value == NotificationItemActionType.delete) {
                      onDeleteNotification(data.id ?? "");
                    }
                  },
                  itemBuilder: (context) => _typeList.map((item) {
                    return PopupMenuItem(
                      value: item,
                      child: ListTile(
                        leading: Icon(item.iconData, size: 24),
                        title: Text(
                          FlutterI18n.translate(context, item.titleKey),
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(data.title ?? '-', style: theme.textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Text(
                    data.content ?? '-',
                    style: theme.textTheme.bodyMedium!.copyWith(color: theme.colorScheme.onSurfaceVariant),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _timeAgo(NotificationItem item) {
    if (item.createdAt == null) return "-";
    final now = DateTime.now();
    final createdAt = DateTime.fromMillisecondsSinceEpoch(item.createdAt!);
    final difference = now.difference(createdAt);
    return difference.inHours < 24 ? timeago.format(createdAt) : formatDate(createdAt);
  }
}
