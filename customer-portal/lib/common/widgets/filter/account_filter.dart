import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pacific_2_customer_portal/common/widgets/account_list/account_list_filter.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';

class AccountFilter extends StatelessWidget {
  const AccountFilter({
    super.key,
    this.controllerTag,
    required this.selectedAccounts,
    required this.onChanged,
  });

  final String? controllerTag;
  final List<UserProfile> selectedAccounts;
  final Function(List<UserProfile> accounts) onChanged;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(FlutterI18n.translate(context, 'account'), style: theme.textTheme.titleMedium),
        const SizedBox(height: 8),
        Text(FlutterI18n.translate(context, 'selectAccount'), style: theme.textTheme.bodyMedium),
        const SizedBox(height: 4),
        Material(
          color: theme.colorScheme.surfaceContainer,
          child: InkWell(
            onTap: () {
              showMyBottomSheet(
                context,
                () => AccountListFilter(
                  controllerTag: controllerTag,
                  selectedAccounts: selectedAccounts,
                  onSelectAccounts: onChanged,
                ),
              );
            },
            borderRadius: BorderRadius.circular(8),
            child: Ink(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.outline),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      selectedAccounts.map((e) => e.getFullName()).join(', '),
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                  SvgPicture.asset('assets/images/ic-arrow-down.svg', width: 16),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
