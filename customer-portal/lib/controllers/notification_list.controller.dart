import 'dart:async';

import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/models/notification.model.dart';
import 'package:pacific_2_customer_portal/services/notification.service.dart';

class NotificationListController extends GetxController {
  final NotificationService _notificationService;

  NotificationListController(this._notificationService);

  Timer? _notificationTimer;

  // Get - List
  final int kSize = 100;
  final RxBool isLoading = false.obs;
  bool _canLoadMore = true;
  final RxList<NotificationItem> notificationList = <NotificationItem>[].obs;
  final RxInt unreadNotificationCount = 0.obs;
  LastEvaluatedKey? _lastEvaluatedKey;

  // Get - Tracking
  final RxBool _hasNewNotification = false.obs;
  RxBool getHasNewNotification() => _hasNewNotification;
  void setHasNewNotification(bool value) {
    _hasNewNotification.value = value;
  }

  // Get - List
  Future<void> getList({bool isRefresh = false}) async {
    if (isLoading.value) return;
    if (isRefresh) clearData();
    if (!_canLoadMore) return;

    isLoading.value = true;
    try {
      Map<String, dynamic> params = {'size': kSize};
      if (_lastEvaluatedKey != null) {
        String valueSortS = _lastEvaluatedKey!.id?.s ?? '';
        String valueSortN = _lastEvaluatedKey!.id?.n ?? '';
        if (valueSortS.isNotEmpty || valueSortN.isNotEmpty) {
          params['filter.valueSort.name'] = 'id';
          params['filter.valueSort.s'] = valueSortS;
          params['filter.valueSort.n'] = valueSortN;
        }
        String valueHashS = _lastEvaluatedKey!.partition?.s ?? '';
        String valueHashN = _lastEvaluatedKey!.partition?.n ?? '';
        if (valueHashS.isNotEmpty || valueHashN.isNotEmpty) {
          params['filter.valueHash.name'] = 'partition_key';
          params['filter.valueHash.s'] = valueHashS;
          params['filter.valueHash.n'] = valueHashN;
        }
      }
      final response = await _notificationService.getNotificationList(params);

      final Map<String, dynamic> data = Map.from(response);
      final NotificationList notificationListResponse = NotificationList.fromJson(data);
      final List<NotificationItem> list = notificationListResponse.content ?? [];
      notificationList.addAll(list);
      unreadNotificationCount.value = list.where((element) => element.read == null || element.read == false).length;
      LastEvaluatedKey? newLastEvaluatedKey = notificationListResponse.lastEvaluatedKey;

      _canLoadMore = newLastEvaluatedKey != null && (newLastEvaluatedKey.isEqual(_lastEvaluatedKey) == false);
      _lastEvaluatedKey = newLastEvaluatedKey;
    } catch (e) {/** */}
    isLoading.value = false;
  }

  void clearData() {
    _canLoadMore = true;
    isLoading.value = false;
    _lastEvaluatedKey = null;
    notificationList.value = [];
  }

  /* ------ #region Tracking ------ */
  Future<void> getTracking() async {
    try {
      final response = await _notificationService.getNotificationTracking();
      if (response == null) setHasNewNotification(false);
      final Map<String, dynamic> data = Map.from(response);
      NotificationTracking tracking = NotificationTracking.fromJson(data);
      setHasNewNotification(tracking.hasNewNotification ?? false);
    } catch (e) {/** */}
  }

  Future<void> updateTracking() async {
    if (_hasNewNotification.value) {
      try {
        await _notificationService.updateNotificationTracking();
        setHasNewNotification(false);
      } catch (e) {/** */}
    }
  }
  /* ------ #endregion Tracking  ------ */

  Future<void> markNotificationRead(String id) async {
    try {
      await _notificationService.markNotificationRead({
        'ids': [id]
      });
      notificationList.firstWhere((element) => element.id == id).read = true;
      unreadNotificationCount.value =
          notificationList.where((element) => element.read == null || element.read == false).length;
    } catch (e) {/** */}
  }

  Future<void> markAllNotificationRead() async {
    List<String> ids = notificationList
        .where((e) => e.read == null || e.read == false)
        .map((e) => e.id ?? "")
        .where((e) => e.isNotEmpty)
        .toList();
    if (ids.isEmpty) return;
    try {
      await _notificationService.markNotificationRead({'ids': ids});
      for (var e in notificationList) {
        e.read = true;
      }
      unreadNotificationCount.value = 0;
    } catch (e) {/** */}
  }

  Future<void> deleteNotification(String id) async {
    try {
      await _notificationService.deleteNotification({
        'ids': [id]
      });
      notificationList.removeWhere((element) => element.id == id);
      unreadNotificationCount.value =
          notificationList.where((element) => element.read == null || element.read == false).length;
    } catch (e) {/** */}
  }

  void startNotificationTimer({duration = const Duration(seconds: 120)}) {
    getTracking();
    _notificationTimer = Timer.periodic(duration, (timer) {
      if (_hasNewNotification.value) return;
      getTracking();
    });
  }

  void removeNotificationTimer() {
    _notificationTimer?.cancel();
    _notificationTimer = null;
  }
}
