import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart' hide Response;
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:dio/dio.dart';

class LoadingController extends GetxController {
  RxInt loadingCount = 0.obs;
  RxString errorApiUrl = ''.obs;
  RxInt errorInternalCode = 0.obs;
  bool showingUnAuthError = false;

  void showLoading(bool loading) {
    if (loading) {
      loadingCount.value++;
    } else if (loadingCount.value > 0) {
      loadingCount.value--;
    }
  }

  bool isLoading() {
    return loadingCount.value > 0;
  }

  void showAlertDialog(
    BuildContext context, {
    String? title,
    bool showTitle = true,
    String message = '',
    Widget? content,
    String positiveText = 'OK',
    Function()? onPositivePress,
    String negativeText = 'Cancel',
    Function()? onNegativePress,
  }) {
    var dialogTitle = title ?? FlutterI18n.translate(context, 'error');
    loadingCount.value = 0;
    ThemeData theme = Theme.of(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        title: showTitle ? Text(dialogTitle, textAlign: TextAlign.center, style: theme.textTheme.headlineSmall) : null,
        content: content ?? Text(message, textAlign: TextAlign.center, style: theme.textTheme.bodyLarge),
        actions: [
          Row(
            children: [
              if (onNegativePress != null)
                Expanded(
                  child: MainOutlinedButton(
                    height: 40,
                    text: negativeText,
                    textStyle: theme.textTheme.labelMedium,
                    onPressed: () {
                      Navigator.of(context).pop();
                      onNegativePress();
                    },
                  ),
                ),
              if (onNegativePress != null) const SizedBox(width: 24),
              Expanded(
                child: MainButton(
                  height: 40,
                  text: positiveText,
                  textStyle: theme.textTheme.labelMedium,
                  onPressed: () {
                    Navigator.of(context).pop();
                    onPositivePress?.call();
                  },
                ),
              )
            ],
          ),
        ],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        actionsPadding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
        titlePadding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
        backgroundColor: theme.colorScheme.surfaceContainer,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  void showApiErrorDialog(BuildContext context, Response response) {
    showAlertDialog(
      context,
      message: response.data['message'] ??
          FlutterI18n.translate(context, 'commonErrorMessage',
              translationParams: {"errorCode": '${response.data['code']}'}),
      onPositivePress: () {
        errorApiUrl.value = response.realUri.toString();
        errorInternalCode.value = response.data['code'];
      },
    );
  }

  void clearApiError() {
    errorApiUrl.value = '';
    errorInternalCode.value = 0;
  }

  void showUnAuthError(BuildContext context, Function()? onPositivePress) {
    if (showingUnAuthError) return;

    showingUnAuthError = true;
    showAlertDialog(
      context,
      message: FlutterI18n.translate(context, 'tokenExpiredMessage'),
      onPositivePress: onPositivePress,
    );
  }
}
