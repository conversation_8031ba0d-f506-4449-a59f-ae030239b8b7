// coverage:ignore-file
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/core/services/http/http.service.dart';

class OrderService {
  final api = SystemConst.API;
  final HttpService _httpService = HttpService();

  Future<dynamic> getMenuItems(params) {
    return _httpService.get(api['PRE_ORDER']!['MENU_ITEMS']!, params: params);
  }

  Future<dynamic> getMealTimes() {
    return _httpService.get(api['PRE_ORDER']!['MEAL_TIMES']!);
  }

  Future<dynamic> getCalendarStatus(dynamic params) {
    return _httpService.get(api['PRE_ORDER']!['CALENDAR']!, params: params);
  }

  Future<dynamic> getPreOrderSummary(dynamic params) {
    return _httpService.get(api['PRE_ORDER']!['SUMMARY']!, params: params);
  }

  Future<dynamic> checkoutPreOrder(params) {
    return _httpService.post(api['PRE_ORDER']!['CHECKOUT']!, params);
  }

  Future<dynamic> getPreOrderHistory(dynamic params) {
    return _httpService.get(api['PRE_ORDER']!['HISTORY']!, params: params);
  }

  Future<dynamic> cancelPreorder(String id, dynamic params) {
    return _httpService.patch(
        api['PRE_ORDER']!['CANCEL']!.replaceFirst('{id}', id), params);
  }

  Future<dynamic> getPreOrderMenuItems(String menuId, dynamic params) {
    return _httpService.get(
        api['PRE_ORDER']!['PRE_ORDER_MENU_ITEMS']!
            .replaceFirst('{menuId}', menuId),
        params: params);
  }

  Future<dynamic> getOrderDetails(String id) {
    return _httpService.get(api['ORDER']!['DETAILS']!.replaceFirst('{id}', id));
  }

  Future<dynamic> cancelOrder(dynamic body) {
    return _httpService.patch(api['ORDER']!['CANCEL']!, body);
  }

  Future<dynamic> getOrderList(dynamic params) {
    return _httpService.get(api['ORDER']!['LIST']!, params: params);
  }

  Future<dynamic> getServiceCharges(dynamic params) {
    return _httpService.get(api['ORDER']!['SERVICE_CHARGE']!, params: params);
  }
}
