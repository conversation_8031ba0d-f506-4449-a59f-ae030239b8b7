// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/loading_overlay/loading_overlay.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/auth.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/action.model.dart';
import 'package:pacific_2_customer_portal/pages/not_found/not_found.dart';
import 'package:pacific_2_customer_portal/routes/app.routes.dart';
import "package:universal_html/html.dart" as html;

class App extends StatefulWidget {
  const App({super.key, required this.child});

  final Widget? child;

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  final _authController = Get.find<AuthController>();
  final _appController = Get.find<AppController>();
  final _loadingController = Get.find<LoadingController>();

  @override
  void initState() {
    super.initState();
    _appController.requiredActions.listen((actions) {
      if (actions.isNotEmpty) {
        for (var action in actions) {
          showInvitationDialog(context, action);
        }
        _appController.resetRequiredActions();
      }
    });
  }

  void showInvitationDialog(BuildContext context, RequiredAction action) {
    ThemeData theme = Theme.of(context);

    _loadingController.showAlertDialog(
      navigatorKey.currentContext!,
      title: FlutterI18n.translate(context, 'invitationTitle'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              text: FlutterI18n.translate(context, 'invitationMessage1'),
              style: theme.textTheme.bodyLarge,
              children: [
                TextSpan(
                  text: '${action.metadata?.sponsorFullName ?? '-'} (${action.metadata?.sponsorEmail ?? '-'})',
                  style: theme.textTheme.bodyLarge!.copyWith(color: theme.colorScheme.primary),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            FlutterI18n.translate(context, 'invitationMessage2'),
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
      positiveText: FlutterI18n.translate(context, 'accept'),
      onPositivePress: () => _appController.replyLinkingInvitation(action, true),
      negativeText: FlutterI18n.translate(context, 'decline'),
      onNegativePress: () => _appController.replyLinkingInvitation(action, false),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_authController.hasInitError.value) {
      return const NotFound();
    }

    return Obx(
      () => RefreshIndicator(
        onRefresh: () async {
          html.window.location.reload();
        },
        child: PortalTarget(
          visible: _loadingController.loadingCount.value > 0,
          portalFollower: LoadingOverlay(child: const SizedBox.shrink()),
          child: FToastBuilder()(context, widget.child),
        ),
      ),
    );
  }
}
