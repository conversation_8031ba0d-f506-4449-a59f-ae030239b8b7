import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/banned_items/widgets/selected_product.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  testWidgets("SelectedProduct render correctly", (tester) async {
    String removedItem = '';
    await tester.pumpWidget(
      renderLocalizedWidget(SelectedProduct(
        selectedProducts: ['item 1', 'item 2'],
        onRemoveProduct: (item) => removedItem = item,
      )),
    );
    await tester.pumpAndSettle();

    expect(find.text('item 1'), findsOne);
    expect(find.text('item 2'), findsOne);

    await tester.tap(find.byType(IconButton).first);
    expect(removedItem, 'item 1');
  });
}
