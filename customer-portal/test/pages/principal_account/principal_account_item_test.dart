import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/principal_account/widgets/principal_account_item.dart';

import '../../mocks/localized_widget.dart';
import '../../mocks/responses/user_profile.response.mock.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();

  testWidgets("PrincipalAccountItem render correctly active", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    bool pressedOrder = false;
    bool pressedDelete = false;
    bool pressedDetails = false;
    bool pressedEdit = false;
    UserProfile profile = UserProfile.fromJson(mockUserProfileResponse);
    profile.avatar?.url = null;
    await tester.pumpWidget(
      renderLocalizedWidget(
        PrincipalAccountItem(
          item: SubAccount(subUser: profile, subAccountStatus: 'ACTIVE'),
          onPressOrder: () {
            pressedOrder = true;
          },
          onPressDelete: () {
            pressedDelete = true;
          },
          onPressDetails: () {
            pressedDetails = true;
          },
          onPressEdit: () {
            pressedEdit = true;
          },
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byType(IconButton), findsOneWidget);
    expect(find.byType(UserAvatar), findsOneWidget);
    expect(
        find.text(
            '${mockUserProfileResponse['firstName']} ${mockUserProfileResponse['lastName']}'),
        findsOne);
    await tester.tap(find.byType(InkWell).last);
    await tester.pumpAndSettle();
    expect(find.byType(IconButton), findsNWidgets(4));
    expect(find.text('Group:'), findsOne);
    expect(find.text(mockUserProfileResponse['userGroup']['groupName'] ?? "-"),
        findsAny);
    expect(find.text('External ID'), findsOne);
    expect(find.text(mockUserProfileResponse['externalId'] ?? "-"), findsAny);

    await tester.tap(find
        .text('View Pre-order')); // Assuming translated text is "View Preorder"
    await tester.pumpAndSettle();

    // Assert button action
    expect(pressedOrder, isTrue);

    await tester.tap(find.byType(IconButton).at(1));
    await tester.pumpAndSettle();

    // Assert button action
    expect(pressedDelete, isTrue);

    await tester.tap(find.byType(IconButton).at(2));
    await tester.pumpAndSettle();

    // Assert button action
    expect(pressedDetails, isTrue);

    await tester.tap(find.byType(IconButton).at(3));
    await tester.pumpAndSettle();

    // Assert button action
    expect(pressedEdit, isTrue);
  });

  testWidgets("PrincipalAccountItem render correctly pending", (tester) async {
    FlutterError.onError = ignoreOverflowErrors;
    bool pressedDelete = false;
    UserProfile profile = UserProfile.fromJson(mockUserProfileResponse);
    profile.avatar?.url = null;
    await tester.pumpWidget(
      renderLocalizedWidget(
        PrincipalAccountItem(
          item: SubAccount(subUser: profile, subAccountStatus: 'PENDING'),
          onPressOrder: () {},
          onPressDelete: () => pressedDelete = true,
          onPressDetails: () {},
          onPressEdit: () {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byType(IconButton), findsOneWidget);
    expect(find.byType(UserAvatar), findsOneWidget);
    expect(
        find.text(
            '${mockUserProfileResponse['firstName']} ${mockUserProfileResponse['lastName']}'),
        findsOne);
    expect(find.text('Awaiting link account acceptance'), findsOne);

    await tester.tap(find.byType(InkWell).last);
    await tester.pumpAndSettle();

    expect(find.byType(IconButton), findsNWidgets(2));

    expect(find.text('View Pre-order'), findsNothing);

    await tester.tap(find.byType(IconButton).at(1));
    await tester.pumpAndSettle();
    expect(pressedDelete, isTrue);
  });
}
