import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/dashboard.main.dart';

import '../../mocks/localized_widget.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets('DashBoard render correctly', (tester) async {
    await tester.pumpWidget(renderLocalizedWidget(const DashBoard()));
    await tester.pumpAndSettle();

    expect(find.text('Principal Account'), findsOne);
    expect(find.text('Wallet'), findsOne);
    expect(find.text('Meal Pre-order'), findsOne);
    expect(find.text('Order History'), findsOne);
  });
}
