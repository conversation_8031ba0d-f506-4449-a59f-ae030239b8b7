import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/dashboard/dashboard_navigation_item.dart';

import '../../mocks/localized_widget.dart';

void main() {
  testWidgets('DashboardNavigationItem render correctly', (tester) async {
    await tester.pumpWidget(renderLocalizedWidget(const DashboardNavigationItem(
      title: 'test',
      backgroundColor: Colors.white,
      gradientColor: Colors.black,
      imagePath: 'assets/images/background-dashboard.png',
      route: '',
    )));
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
    expect(find.byType(Ink), findsOne);
    expect(
      ((tester.firstWidget(find.byType(Ink)) as Ink).decoration
              as BoxDecoration)
          .color,
      Colors.white,
    );
  });

  testWidgets('DashboardNavigationItem render correctly desktop',
      (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        const DashboardNavigationItem(
          title: 'test',
          backgroundColor: Colors.white,
          gradientColor: Colors.black,
          imagePath: 'assets/images/background-dashboard.png',
          route: '',
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
    expect(find.byType(Ink), findsOne);
    expect(
      ((tester.firstWidget(find.byType(Ink)) as Ink).decoration
              as BoxDecoration)
          .color,
      Colors.white,
    );
  });
}
