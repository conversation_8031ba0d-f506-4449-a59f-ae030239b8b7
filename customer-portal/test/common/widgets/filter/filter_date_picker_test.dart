import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/common/widgets/filter/filter_date_picker.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  testWidgets("FilterDatePicker render correctly", (tester) async {
    DateTime now = DateTime.now();
    String currentMonth = DateFormat.yMMMM().format(now);
    await tester.pumpWidget(
      renderLocalizedWidget(
        FilterDatePicker(
          isStartDate: true,
          selectedDate: null,
          onSelectDate: (value) {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(currentMonth), findsOne);
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byType(MainButton), findsOne);
    expect(
      (tester.firstWidget(find.byType(MainButton)) as MainButton).disabled,
      isTrue,
    );
  });

  testWidgets("FilterDatePicker render correctly desktop", (tester) async {
    DateTime now = DateTime.now();
    String currentMonth = DateFormat.yMMMM().format(now);

    bool selectDate = false;

    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        FilterDatePicker(
          isStartDate: true,
          selectedDate: null,
          onSelectDate: (_) => selectDate = true,
          isDesktop: true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(currentMonth), findsOne);
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byType(MainButton), findsNothing);

    await tester.tap(find.text('1').first);
    expect(selectDate, true);
  });

  testWidgets("FilterDatePicker should change month when clicked on arrow",
      (tester) async {
    String currentMonth = DateFormat.yMMMM().format(DateTime.now());
    String nextMonth =
        DateFormat.yMMMM().format(Jiffy.now().add(months: 1).dateTime);

    await tester.pumpWidget(
      renderLocalizedWidget(
        FilterDatePicker(
          isStartDate: true,
          selectedDate: null,
          onSelectDate: (value) {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byKey(ValueKey(nextMonth)), findsNothing);
    await tester.tap(find.byIcon(Icons.arrow_forward_ios));
    await tester.pumpAndSettle();
    expect(find.byKey(ValueKey(currentMonth)), findsNothing);
    expect(find.byKey(ValueKey(nextMonth)), findsOne);
    await tester.tap(find.byIcon(Icons.arrow_back_ios_new));
    await tester.pumpAndSettle();
    expect(find.byKey(ValueKey(currentMonth)), findsOne);
    expect(find.byKey(ValueKey(nextMonth)), findsNothing);
  });

  testWidgets("FilterDatePicker should select day correctly", (tester) async {
    DateTime day1 = DateTime.now().copyWith(day: 15);
    DateTime day2 = DateTime.now().copyWith(day: 16);

    await tester.pumpWidget(renderLocalizedWidget(Container()));
    await tester.pumpAndSettle();
    final BuildContext context = tester.element(find.byType(Container));

    showMyBottomSheet(
      context,
      () => FilterDatePicker(
        isStartDate: true,
        selectedDate: day1,
        onSelectDate: (value) {
          day1 = value;
        },
      ),
    );
    await tester.pumpAndSettle();

    await tester.tap(find.text(day2.day.toString()));
    await tester.pumpAndSettle();
    await tester.tap(find.byType(MainButton));
    await tester.pumpAndSettle();

    expect(day1.day, day2.day);
  });
}
