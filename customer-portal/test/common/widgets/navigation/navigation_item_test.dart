import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/navigation/navigation_item.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("NavigationItem render correctly", (tester) async {
    bool? pressed = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        NavigationItem(
          isSelected: true,
          labelKey: 'dashboard',
          onPressed: () => pressed = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Dashboard'), findsOne);
    await tester.tap(find.text('Dashboard'));
    expect(pressed, isTrue);
  });
}
