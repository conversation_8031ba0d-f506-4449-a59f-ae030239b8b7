import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/product_options/product_options.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/models/catalog_product.model.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/catalog_product.response.mock.dart';
import '../../../mocks/responses/menu_item_list.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  final preorderItem =
      PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]);
  final catalogProduct = CatalogProduct.fromJson(mockCatalogProductResponse);

  testWidgets("ProductOptions render correctly", (tester) async {
    CartItem? result;
    bool pressedClose = false;

    await tester.pumpWidget(
      renderLocalizedWidget(
        ProductOptions(
          item: preorderItem,
          catalogProduct: catalogProduct,
          onAddToCart: (cartItem) => result = cartItem,
          onClose: () => pressedClose = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Options'), findsOne);

    await tester.tap(find.byIcon(Icons.close));
    await tester.pumpAndSettle();
    expect(pressedClose, true);

    await tester.tap(find.text('item 1'));
    await tester.pumpAndSettle();
    await tester.tap(find.text('item 3'));
    await tester.pumpAndSettle();
    await tester.tap(find.byType(TextButton));
    await tester.pumpAndSettle();
    expect(result, null);

    await tester.tap(find.text('item 2'));
    await tester.pumpAndSettle();
    await tester.tap(find.text('item 3'));
    await tester.pumpAndSettle();
    await tester.tap(find.text('item 4'));
    await tester.pumpAndSettle();
    await tester.tap(find.byType(TextButton));
    await tester.pumpAndSettle();
    expect(result, isNotNull);
    expect(result?.options.length, 3);

    await tester.tap(find.byIcon(Icons.add_rounded));
    await tester.pumpAndSettle();
    await tester.tap(find.byIcon(Icons.add_rounded));
    await tester.pumpAndSettle();
    await tester.tap(find.byIcon(Icons.remove_rounded));
    await tester.pumpAndSettle();
    await tester.tap(find.byType(TextButton));
    await tester.pumpAndSettle();
    expect(result?.quantity, 2);

    await tester.tap(find.text('Add note'));
    await tester.pumpAndSettle();
    await tester.enterText(find.byType(TextField).first, "test");
    await tester.pumpAndSettle();
    await tester.tap(find.byType(MainButton));
    await tester.pumpAndSettle();
    await tester.tap(find.byType(TextButton));
    await tester.pumpAndSettle();
    expect(result?.notes, 'test');
  });
}
