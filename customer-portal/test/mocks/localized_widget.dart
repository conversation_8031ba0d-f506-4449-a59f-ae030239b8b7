import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_i18n/loaders/decoders/json_decode_strategy.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:pacific_2_customer_portal/themes/theme.dart';

void ignoreOverflowErrors(
  FlutterErrorDetails details, {
  bool forceReport = false,
}) {
  bool ifIsOverflowError = false;
  bool isGoRouterError = false;

  // Detect overflow error.
  var exception = details.exception;
  if (exception is FlutterError) {
    ifIsOverflowError = !exception.diagnostics.any(
        (e) => e.value.toString().startsWith("A RenderFlex overflowed by"));
  } else if (exception.toString().contains('No GoRouter found in context')) {
    isGoRouterError = true;
  }

  // Ignore if is overflow error.
  if (ifIsOverflowError) {
    print('Overflow error.');
  } else if (isGoRouterError) {
    print('Go Router navigation error.');
  } else {
    FlutterError.dumpErrorToConsole(details, forceReport: forceReport);
  }
}

Widget renderLocalizedWidget(Widget child,
    {Size size = const Size(1023, 1000)}) {
  return Directionality(
    textDirection: TextDirection.ltr,
    child: Portal(
      child: MaterialApp(
        theme: MaterialTheme().light(),
        localizationsDelegates: [
          FlutterI18nDelegate(
            translationLoader: FileTranslationLoader(
              useCountryCode: false,
              fallbackFile: 'en',
              basePath: 'assets/i18n',
              forcedLocale: const Locale('en'),
              decodeStrategies: [JsonDecodeStrategy()],
            ),
          ),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
        ],
        home: MediaQuery(
          data: MediaQueryData(size: size),
          child: Material(child: child),
        ),
      ),
    ),
  );
}

Widget renderLocalizedWidgetDesktop(Widget child) {
  return renderLocalizedWidget(child, size: const Size(2000, 1000));
}
