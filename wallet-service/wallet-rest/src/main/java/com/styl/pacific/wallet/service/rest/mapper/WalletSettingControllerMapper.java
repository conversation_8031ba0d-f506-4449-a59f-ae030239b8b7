/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.FindWalletSettingsQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletSettingCommand;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.WalletSettingsFilterQuery;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataCommonMapper;
import com.styl.pacific.wallet.service.entity.WalletSetting;
import com.styl.pacific.wallet.service.requests.walletsetting.FindWalletSettingsRequest;
import com.styl.pacific.wallet.service.requests.walletsetting.SaveWalletSettingRequest;
import com.styl.pacific.wallet.service.requests.walletsetting.WalletSettingsFilterRequest;
import com.styl.pacific.wallet.service.responses.walletsetting.WalletSettingResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface WalletSettingControllerMapper {

	WalletSettingControllerMapper INSTANCE = Mappers.getMapper(WalletSettingControllerMapper.class);

	SaveWalletSettingCommand toSaveWalletSettingCommand(SaveWalletSettingRequest request, Long tenantId);

	@Mapping(target = "id", source = "id", qualifiedByName = "walletSettingIdToString")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToString")
	@Mapping(target = "currency", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "minTopupAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "maxTopupAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "presetAmounts", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "maxDailyOfflineSpendPerDeviceAmount", qualifiedByName = "moneyToBigInteger")
	WalletSettingResponse toWalletSettingResponse(WalletSetting walletSetting);

	@Mapping(target = "filter", source = "filter")
	FindWalletSettingsQuery findsWalletSettingsRequestToFindsWalletSettingsQuery(FindWalletSettingsRequest request,
			WalletSettingsFilterQuery filter);

	@Mapping(target = "tenantId", expression = "java(java.util.Objects.nonNull(tenantId) ? tenantId : filter.getTenantId())")
	WalletSettingsFilterQuery walletSettingsFilterRequestToWalletSettingsFilterQuery(WalletSettingsFilterRequest filter,
			Long tenantId);
}
