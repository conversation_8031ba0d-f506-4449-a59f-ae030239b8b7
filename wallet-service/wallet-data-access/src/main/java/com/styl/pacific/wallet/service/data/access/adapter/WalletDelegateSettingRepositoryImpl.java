/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.adapter;

import com.styl.pacific.wallet.service.data.access.entity.WalletDelegateSettingEntity;
import com.styl.pacific.wallet.service.data.access.mapper.WalletDelegateSettingDataAccessMapper;
import com.styl.pacific.wallet.service.data.access.repository.WalletDelegateSettingJpaRepository;
import com.styl.pacific.wallet.service.data.access.specification.WalletDelegateSettingEntitySpecification;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.FindWalletDelegateSettingsQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletDelegateSettingQuery;
import com.styl.pacific.wallet.service.domain.output.repository.WalletDelegateSettingRepository;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class WalletDelegateSettingRepositoryImpl implements WalletDelegateSettingRepository {

	private final WalletDelegateSettingJpaRepository walletDelegateSettingJpaRepository;

	@Override
	public Optional<WalletDelegateSetting> getWalletDelegateSetting(GetWalletDelegateSettingQuery query) {
		WalletDelegateSettingEntitySpecification specs = WalletDelegateSettingDataAccessMapper.INSTANCE
				.toWalletDelegateSettingEntitySpecification(query);
		return walletDelegateSettingJpaRepository.findOne(specs)
				.map(WalletDelegateSettingDataAccessMapper.INSTANCE::toWalletDelegateSetting);
	}

	@Override
	public List<WalletDelegateSetting> findWalletDelegateSettings(FindWalletDelegateSettingsQuery query) {
		WalletDelegateSettingEntitySpecification specs = WalletDelegateSettingDataAccessMapper.INSTANCE
				.toWalletDelegateSettingEntitySpecification(query);
		return walletDelegateSettingJpaRepository.findAll(specs)
				.stream()
				.map(WalletDelegateSettingDataAccessMapper.INSTANCE::toWalletDelegateSetting)
				.toList();
	}

	@Override
	public WalletDelegateSetting save(WalletDelegateSetting walletDelegateSetting) {
		WalletDelegateSettingEntity walletDelegateSettingEntity = WalletDelegateSettingDataAccessMapper.INSTANCE
				.toWalletDelegateSettingEntity(walletDelegateSetting);
		WalletDelegateSettingEntity result = walletDelegateSettingJpaRepository.saveAndFlush(
				walletDelegateSettingEntity);
		return WalletDelegateSettingDataAccessMapper.INSTANCE.toWalletDelegateSetting(result);
	}

	@Override
	public void delete(WalletDelegateSetting setting) {
		walletDelegateSettingJpaRepository.deleteById(setting.getId()
				.getValue());
	}
}
