/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_wallet_delegate_setting")
public class WalletDelegateSettingEntity extends AuditableEntity {

	@Id
	private Long id;

	private Long tenantId;

	private Long parentWalletId;

	private Long parentCustomerId;

	private Long subCustomerId;

	private Long subWalletId;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof WalletDelegateSettingEntity that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(tenantId, that.tenantId) && Objects.equals(parentWalletId,
				that.parentWalletId) && Objects.equals(parentCustomerId, that.parentCustomerId) && Objects.equals(
						subCustomerId, that.subCustomerId) && Objects.equals(subWalletId, that.subWalletId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, tenantId, parentWalletId, parentCustomerId, subCustomerId,
				subWalletId);
	}
}
