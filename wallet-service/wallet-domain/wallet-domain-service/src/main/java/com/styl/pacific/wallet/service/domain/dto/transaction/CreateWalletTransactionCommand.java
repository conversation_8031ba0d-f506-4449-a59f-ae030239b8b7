/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.dto.transaction;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */

@Getter
@Builder
@AllArgsConstructor
public class CreateWalletTransactionCommand {

	private Long tenantId;

	private Long customerId;

	private Long walletId;

	private Long sourceWalletId;

	private Long destinationWalletId;

	private TransactionType transactionType;

	private TransactionCategory transactionCategory;

	private String paymentSessionId;

	private Long paymentTransactionId;

	private String deviceId;

	private String cardId;

	private String currency;

	private Money amount;

	private Money balance;

	private Money oldBalance;

	private String description;

	private String idempotencyKey;

	private Boolean isOffline;

	private String migrationId;
}
