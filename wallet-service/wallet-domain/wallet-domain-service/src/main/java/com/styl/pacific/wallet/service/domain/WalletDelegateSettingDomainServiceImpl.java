/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain;

import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.FindWalletDelegateSettingsQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletDelegateSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletDelegateSettingCommand;
import com.styl.pacific.wallet.service.domain.helper.UserHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletDelegateSettingHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletHelper;
import com.styl.pacific.wallet.service.domain.mapper.WalletDelegateSettingMapper;
import com.styl.pacific.wallet.service.domain.output.repository.WalletDelegateSettingRepository;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletDelegateSettingDomainService;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import com.styl.pacific.wallet.service.entity.id.WalletDelegateSettingIdGenerator;
import com.styl.pacific.wallet.service.enums.WalletType;
import com.styl.pacific.wallet.service.exception.WalletDelegateSettingNotFoundException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
@Service
public class WalletDelegateSettingDomainServiceImpl implements WalletDelegateSettingDomainService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletDelegateSettingRepository walletDelegateSettingRepository;

	private final WalletDelegateSettingIdGenerator walletDelegateSettingIdGenerator;

	private final WalletDelegateSettingHelper walletDelegateSettingHelper;

	private final UserHelper userHelper;

	private final WalletHelper walletHelper;

	@Override
	@Transactional
	public WalletDelegateSetting saveWalletDelegateSetting(SaveWalletDelegateSettingCommand command) {

		Wallet subWallet = walletHelper.getWalletOrThrowNotFound(GetWalletQuery.builder()
				.tenantId(command.getTenantId())
				.walletId(command.getSubWalletId())
				.type(WalletType.DEPOSIT)
				.build());

		Wallet parentWallet = walletHelper.getWalletOrThrowNotFound(GetWalletQuery.builder()
				.tenantId(command.getTenantId())
				.walletId(command.getParentWalletId())
				.type(WalletType.DEPOSIT)
				.build());

		userHelper.checkSubAccountExisting(parentWallet.getCustomerId()
				.getValue(), subWallet.getCustomerId()
						.getValue());

		WalletDelegateSetting setting = walletDelegateSettingHelper.getWalletDelegateSettingOrGetDefaultIfNotFound(
				GetWalletDelegateSettingQuery.builder()
						.tenantId(command.getTenantId())
						.parentWalletId(command.getParentWalletId())
						.subWalletId(command.getSubWalletId())
						.build());
		WalletDelegateSetting walletDelegateSetting = WalletDelegateSettingMapper.INSTANCE
				.saveWalletDelegateCommandToWalletDelegateSetting(command, setting, parentWallet.getCustomerId(),
						subWallet.getCustomerId());
		if (Objects.isNull(walletDelegateSetting.getId())) {
			walletDelegateSetting.setId(walletDelegateSettingIdGenerator.nextId());
		}
		return walletDelegateSettingRepository.save(walletDelegateSetting);
	}

	@Override
	@Transactional(readOnly = true)
	public List<WalletDelegateSetting> getWalletDelegateSettings(FindWalletDelegateSettingsQuery query) {
		return walletDelegateSettingRepository.findWalletDelegateSettings(query);
	}

	@Override
	@Transactional(readOnly = true)
	public WalletDelegateSetting getWalletDelegateSetting(GetWalletDelegateSettingQuery query) {
		return walletDelegateSettingHelper.getWalletDelegateSettingOrGetDefaultIfNotFound(query);
	}

	@Override
	@Transactional
	public void cancelWalletDelegateSetting(Long id, Long tenantId) {
		Optional<WalletDelegateSetting> setting = walletDelegateSettingRepository.getWalletDelegateSetting(
				GetWalletDelegateSettingQuery.builder()
						.id(id)
						.tenantId(tenantId)
						.build());
		if (setting.isEmpty()) {
			throw new WalletDelegateSettingNotFoundException("Cancel wallet delegate setting  not found with id :"
					+ id);
		}
		walletDelegateSettingRepository.delete(setting.get());
	}
}
