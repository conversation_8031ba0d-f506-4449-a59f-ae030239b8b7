/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.FundSourceTopupHistoryId;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupHistoryQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupSchedulerQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupTrackingQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourcesQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.GetFundSourceQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.GetFundSourceSchedulerQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.domain.helper.TenantHelper;
import com.styl.pacific.wallet.service.domain.helper.TopupProcessingJobHandler;
import com.styl.pacific.wallet.service.domain.mapper.FundSourceDataMapper;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceRepository;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceSchedulerRepository;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupHistoryRepository;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupTrackingRepository;
import com.styl.pacific.wallet.service.domain.port.input.service.FundSourceDomainService;
import com.styl.pacific.wallet.service.dto.TenantDto;
import com.styl.pacific.wallet.service.entity.FundSource;
import com.styl.pacific.wallet.service.entity.FundSourceTopupHistory;
import com.styl.pacific.wallet.service.entity.FundSourceTopupScheduler;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import com.styl.pacific.wallet.service.entity.id.FundSourceIdGenerator;
import com.styl.pacific.wallet.service.entity.id.FundSourceTopupHistoryIdGenerator;
import com.styl.pacific.wallet.service.entity.id.FundSourceTopupSchedulerIdGenerator;
import com.styl.pacific.wallet.service.enums.FundSourceStatus;
import com.styl.pacific.wallet.service.enums.FundSourceTopupStatus;
import com.styl.pacific.wallet.service.exception.FundSourceNotFoundException;
import com.styl.pacific.wallet.service.exception.FundSourceTopupSchedulerNotFoundException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
@Service
public class FundSourceDomainServiceImpl implements FundSourceDomainService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final FundSourceRepository fundSourceRepository;

	private final FundSourceIdGenerator fundSourceIdGenerator;

	private final FundSourceSchedulerRepository fundSourceSchedulerRepository;

	private final FundSourceTopupHistoryRepository fundSourceTopupHistoryRepository;

	private final FundSourceTopupHistoryIdGenerator fundSourceTopupHistoryIdGenerator;

	private final FundSourceTopupSchedulerIdGenerator fundSourceTopupSchedulerIdGenerator;

	private final TenantHelper tenantHelper;
	private final TopupProcessingJobHandler topupProcessingJobHandler;

	private final FundSourceTopupTrackingRepository fundSourceTopupTrackingRepository;

	@Override
	@Transactional
	public FundSource createFundSource(CreateFundSourceCommand command) {
		FundSource fundSource = FundSourceDataMapper.INSTANCE.createFundSourceCommandToFundSource(command,
				fundSourceIdGenerator.nextId());
		return fundSourceRepository.save(fundSource);
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<FundSource> findFundSources(FindFundSourcesQuery query) {
		return fundSourceRepository.findFundSources(query);
	}

	@Override
	@Transactional(readOnly = true)
	public FundSource getFundSource(GetFundSourceQuery query) {
		return getFundSourceOrThrowNotFound(query);
	}

	@Override
	@Transactional
	public FundSource updateFundSource(UpdateFundSourceCommand command) {
		GetFundSourceQuery query = GetFundSourceQuery.builder()
				.tenantId(command.getTenantId())
				.fundSourceId(command.getFundSourceId())
				.build();
		FundSource fundSourceExisting = getFundSourceOrThrowNotFound(query);
		FundSource fundSource = FundSourceDataMapper.INSTANCE.updateFundSourceCommandToFundSource(command,
				fundSourceExisting);
		return fundSourceRepository.save(fundSource);
	}

	@Override
	@Transactional
	public void deleteFundSource(List<Long> fundSourceIds, Long tenantId) {
		fundSourceIds.forEach(id -> deleteFundSourceById(id, tenantId));
	}

	@Override
	@Transactional
	public FundSource lockFundSource(Long fundSourceId, Long tenantId) {
		FundSource fundSource = getFundSourceOrThrowNotFound(GetFundSourceQuery.builder()
				.fundSourceId(fundSourceId)
				.tenantId(tenantId)
				.build());
		return fundSourceRepository.save(FundSourceDataMapper.INSTANCE.withStatusToFundSource(fundSource,
				FundSourceStatus.INACTIVE));
	}

	@Override
	@Transactional
	public FundSource activateFundSource(Long fundSourceId, Long tenantId) {
		FundSource fundSource = getFundSourceOrThrowNotFound(GetFundSourceQuery.builder()
				.fundSourceId(fundSourceId)
				.tenantId(tenantId)
				.build());
		return fundSourceRepository.save(FundSourceDataMapper.INSTANCE.withStatusToFundSource(fundSource,
				FundSourceStatus.ACTIVE));
	}

	@Override
	@Transactional
	public FundSourceTopupScheduler createFundSourceScheduler(CreateFundSourceTopupSchedulerCommand command) {
		getFundSourceOrThrowNotFound(GetFundSourceQuery.builder()
				.fundSourceId(command.getFundSourceId())
				.tenantId(command.getTenantId())
				.build());
		FundSourceTopupScheduler scheduler = FundSourceDataMapper.INSTANCE
				.createFundSourceTopupSchedulerCommandToFundSourceTopupScheduler(command,
						fundSourceTopupSchedulerIdGenerator.nextId());
		scheduler.initNextTopupDate(TimeZone.getTimeZone(tenantHelper.getTenantById(command.getTenantId())
				.zoneId()));
		return fundSourceSchedulerRepository.save(scheduler);
	}

	@Override
	@Transactional
	public FundSourceTopupScheduler updateFundSourceScheduler(UpdateFundSourceTopupSchedulerCommand command) {
		TenantDto tenantDto = tenantHelper.getTenantById(command.getTenantId());
		FundSourceTopupScheduler scheduler = getFundSourceTopupSchedulerOrThrowNotFound(GetFundSourceSchedulerQuery
				.builder()
				.fundSourceId(command.getFundSourceId())
				.id(command.getId())
				.tenantId(command.getTenantId())
				.build());

		// update next topupDate if startDate change
		FundSourceTopupScheduler newScheduler = FundSourceDataMapper.INSTANCE
				.updateFundSourceTopupSchedulerCommandToFundSourceTopupScheduler(command, scheduler);
		newScheduler.initNextTopupDate(TimeZone.getTimeZone(tenantDto.zoneId()));

		return fundSourceSchedulerRepository.save(newScheduler);
	}

	@Override
	public List<FundSourceTopupScheduler> findFundSourceTopupScheduler(FindFundSourceTopupSchedulerQuery query) {
		return fundSourceSchedulerRepository.findFundSourceTopupSchedulers(query);
	}

	@Override
	@Transactional
	public void deleteFundSourceScheduler(List<Long> ids, Long tenantId) {
		ids.forEach(id -> deleteFundSourceSchedulerById(id, tenantId));
	}

	@Override
	public FundSourceTopupScheduler getOldestScheduler(Instant cutOffTime) {
		return fundSourceSchedulerRepository.getOldestOrOverRunScheduler(cutOffTime)
				.orElse(null);
	}

	@Override
	public void processFundSourceTopup(FundSourceTopupScheduler scheduler) {
		FundSource fundSource = getFundSourceOrThrowNotFound(GetFundSourceQuery.builder()
				.fundSourceId(scheduler.getFundSourceId()
						.getValue())
				.tenantId(scheduler.getTenantId()
						.getValue())
				.build());

		scheduler.updateLastTopupDate();

		FundSourceTopupHistory history = initOrRetrieveFundSourceTopupHistory(fundSource, scheduler);
		if (!history.getId()
				.equals(scheduler.getLastSchedulerHistoryId())) {
			scheduler.setLastSchedulerHistoryId(history.getId()
					.getValue());
		}
		fundSourceSchedulerRepository.save(scheduler);

		logger.info("Scheduler history status: {}", history.getStatus());
		logger.info("Scheduler history scheduled topup date: {}", history.getScheduledTopupDate());
		logger.info("Scheduler next topup date: {}", scheduler.getNextTopupDate());
		// if the history is completed then check the scheduled topup date and the next topup date
		// history's scheduled topup date is the same as the scheduler's next topup date
		// means that the history of current topup is completed
		// skip processing and update the scheduler's next topup date
		// so only process if status is pending or scheduled Topup date
		try {
			if (!history.getStatus()
					.equals(FundSourceTopupStatus.SUCCESS) || !history.getScheduledTopupDate()
							.equals(scheduler.getNextTopupDate())) {
				// process wallet topup
				topupProcessingJobHandler.processTopupToBeneficiariesWallet(fundSource, history);
			} else {
				logger.info("FundSourceTopupHistory is already completed, skip processing");
			}

			completeFundSourceTopup(history, scheduler);
		} catch (Exception e) {
			logger.error("Error processing topup to beneficiaries wallet for fundSource: {} - Scheduler History: {}",
					fundSource.getId()
							.getValue(), history.getId(), e);
			history.fail();
			fundSourceTopupHistoryRepository.save(history);
		}
	}

	@Override
	public Paging<FundSourceTopupHistory> findFundSourceHistories(FindFundSourceTopupHistoryQuery query) {
		return fundSourceTopupHistoryRepository.findFundSourceTopupHistories(query);
	}

	@Override
	public Paging<FundSourceTopupTracking> findFundSourceTopupTracking(FindFundSourceTopupTrackingQuery query) {
		return fundSourceTopupTrackingRepository.findFundSourceTopupTracking(query);
	}

	private FundSourceTopupHistory initOrRetrieveFundSourceTopupHistory(FundSource fundSource,
			FundSourceTopupScheduler scheduler) {
		FundSourceTopupHistory history = Optional.ofNullable(scheduler.getLastSchedulerHistoryId())
				.map(fundSourceTopupHistoryRepository::findById)
				.filter(Optional::isPresent)
				.map(Optional::get)
				.filter(h -> h.getScheduledTopupDate()
						.equals(scheduler.getNextTopupDate()))
				.orElse(null);

		// 3. if the history is null then create a new one
		if (history == null) {
			Instant fundExpiry = calculateFundExpiry(fundSource, scheduler);

			history = FundSourceTopupHistory.builder()
					.id(new FundSourceTopupHistoryId(fundSourceTopupHistoryIdGenerator.nextId()))
					.tenantId(scheduler.getTenantId())
					.fundSourceId(scheduler.getFundSourceId())
					.fundSourceTopupSchedulerId(scheduler.getId())
					.topupFrequency(scheduler.getTopupFrequency())
					.amount(scheduler.getAmount())
					.currency(scheduler.getCurrency())
					.fundExpiresOn(fundExpiry)
					.scheduledTopupDate(scheduler.getNextTopupDate())
					.lastResumeDate(scheduler.getLastTopupDate())
					.status(FundSourceTopupStatus.PENDING)
					.createdAt(Instant.now())
					.updatedAt(Instant.now())
					.build();
		} else {
			history.resume(scheduler.getLastTopupDate());
		}

		fundSourceTopupHistoryRepository.save(history);

		return history;
	}

	private Instant calculateFundExpiry(FundSource fundSource, FundSourceTopupScheduler scheduler) {
		Instant fundExpiry = Optional.ofNullable(scheduler.getFundExpiresInMs())
				.map(fundExpiresInMs -> scheduler.getNextTopupDate()
						.plusMillis(fundExpiresInMs))
				.orElse(scheduler.getFundExpiresOn());

		if (fundExpiry != null) {
			return fundExpiry;
		}
		return Optional.ofNullable(fundSource.getFundExpiresInMs())
				.map(fundExpiresInMs -> scheduler.getNextTopupDate()
						.plusMillis(fundExpiresInMs))
				.orElse(fundSource.getFundExpiresOn());
	}

	private void completeFundSourceTopup(FundSourceTopupHistory history, FundSourceTopupScheduler scheduler) {
		// Complete topup, update topup history and save the new next topup date
		history.complete();
		fundSourceTopupHistoryRepository.save(history);

		// Calculate the next topup date and save
		TenantDto tenantDto = tenantHelper.getTenantById(scheduler.getTenantId()
				.getValue());
		scheduler.updateNextTopupDate(TimeZone.getTimeZone(tenantDto.zoneId()));
		fundSourceSchedulerRepository.save(scheduler);
	}

	private FundSource getFundSourceOrThrowNotFound(GetFundSourceQuery query) {
		Optional<FundSource> fundSource = fundSourceRepository.getFundSource(query);
		if (fundSource.isEmpty()) {
			throw new FundSourceNotFoundException("FundSource not found with id: " + query.getFundSourceId());
		}
		return fundSource.get();
	}

	private FundSourceTopupScheduler getFundSourceTopupSchedulerOrThrowNotFound(GetFundSourceSchedulerQuery query) {
		Optional<FundSourceTopupScheduler> scheduler = fundSourceSchedulerRepository.getFundSourceScheduler(query);
		if (scheduler.isEmpty()) {
			throw new FundSourceTopupSchedulerNotFoundException("FundSource Scheduler is not found with id: " + query
					.getId());
		}
		return scheduler.get();
	}

	private void deleteFundSourceById(Long id, Long tenantId) {
		FundSource fundSource = getFundSourceOrThrowNotFound(GetFundSourceQuery.builder()
				.fundSourceId(id)
				.tenantId(tenantId)
				.build());
		fundSourceSchedulerRepository.deleteAllFundSourceSchedulerBy(tenantId, id);
		fundSourceRepository.deleteFundSources(fundSource.getId()
				.getValue());
	}

	private void deleteFundSourceSchedulerById(Long id, Long tenantId) {
		FundSourceTopupScheduler scheduler = getFundSourceTopupSchedulerOrThrowNotFound(GetFundSourceSchedulerQuery
				.builder()
				.id(id)
				.tenantId(tenantId)
				.build());
		fundSourceSchedulerRepository.deleteFundSourceScheduler(scheduler.getId()
				.getValue());
	}

}
