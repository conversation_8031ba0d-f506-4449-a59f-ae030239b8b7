/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupHistoryRepository;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupTrackingRepository;
import com.styl.pacific.wallet.service.entity.FundSourceTopupHistory;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class FundSourceTopupHelper {

	private final FundSourceTopupHistoryRepository fundSourceTopupHistoryRepository;

	private final FundSourceTopupTrackingRepository fundSourceTopupTrackingRepository;

	@Transactional
	public FundSourceTopupHistory saveFundSourceTopupHistory(FundSourceTopupHistory fundSourceTopupHistory) {
		return fundSourceTopupHistoryRepository.save(fundSourceTopupHistory);
	}

	@Transactional
	public void saveFundSourceTopupTracking(FundSourceTopupTracking fundSourceTopupTracking) {
		fundSourceTopupTrackingRepository.save(fundSourceTopupTracking);
	}
}
