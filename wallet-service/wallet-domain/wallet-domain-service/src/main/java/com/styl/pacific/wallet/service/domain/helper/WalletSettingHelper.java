/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletSettingQuery;
import com.styl.pacific.wallet.service.domain.mapper.WalletSettingMapper;
import com.styl.pacific.wallet.service.domain.output.repository.WalletSettingRepository;
import com.styl.pacific.wallet.service.entity.WalletSetting;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WalletSettingHelper {

	private final WalletSettingRepository walletSettingRepository;

	public WalletSetting getWalletSettingOrGetDefaultIfNotFound(GetWalletSettingQuery query) {
		return walletSettingRepository.getWalletSetting(query)
				.orElse(WalletSettingMapper.INSTANCE.toWalletSettingDefault(query.getTenantId()));
	}
}
