/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.port.input.service;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletSettingCommand;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.WalletSettingsFilterQuery;
import com.styl.pacific.wallet.service.entity.WalletSetting;

/**
 * <AUTHOR>
 *
 */
public interface WalletSettingDomainService {

	WalletSetting saveWalletSetting(SaveWalletSettingCommand command);

	Paging<WalletSetting> findWalletSettings(PaginationQuery<WalletSettingsFilterQuery> query);

	WalletSetting getWalletSetting(GetWalletSettingQuery query);
}
