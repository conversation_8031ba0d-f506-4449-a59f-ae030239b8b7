/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.WalletId;
import com.styl.pacific.domain.valueobject.WalletSpendingLimitSettingId;
import com.styl.pacific.wallet.service.enums.SpendingLimitType;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public class WalletSpendingLimitSetting extends AggregateRoot<WalletSpendingLimitSettingId> {

	private TenantId tenantId;

	private WalletId walletId;

	private UserId customerId;

	private Money spendingLimit;

	private String currency;

	private Money amount;

	private SpendingLimitType type;

	private LocalDate lastAmountUpdatedAt;

	private Instant createdAt;

	private Instant updatedAt;

	private Instant deletedAt;

	private WalletSpendingLimitSetting(Builder builder) {
		setId(builder.id);
		tenantId = builder.tenantId;
		walletId = builder.walletId;
		customerId = builder.customerId;
		spendingLimit = builder.spendingLimit;
		currency = builder.currency;
		amount = builder.amount;
		type = builder.type;
		lastAmountUpdatedAt = builder.lastAmountUpdatedAt;
		createdAt = builder.createdAt;
		updatedAt = builder.updatedAt;
		deletedAt = builder.deletedAt;
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private WalletSpendingLimitSettingId id;
		private TenantId tenantId;
		private WalletId walletId;
		private UserId customerId;
		private Money spendingLimit;
		private String currency;
		private Money amount;
		private SpendingLimitType type;
		private LocalDate lastAmountUpdatedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Instant deletedAt;

		public Builder id(WalletSpendingLimitSettingId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder walletId(WalletId walletId) {
			this.walletId = walletId;
			return this;
		}

		public Builder customerId(UserId customerId) {
			this.customerId = customerId;
			return this;
		}

		public Builder spendingLimit(Money spendingLimit) {
			this.spendingLimit = spendingLimit;
			return this;
		}

		public Builder currency(String currency) {
			this.currency = currency;
			return this;
		}

		public Builder amount(Money amount) {
			this.amount = amount;
			return this;
		}

		public Builder type(SpendingLimitType type) {
			this.type = type;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder lastAmountUpdatedAt(LocalDate lastAmountUpdatedAt) {
			this.lastAmountUpdatedAt = lastAmountUpdatedAt;
			return this;
		}

		public WalletSpendingLimitSetting build() {
			return new WalletSpendingLimitSetting(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof WalletSpendingLimitSetting that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(tenantId, that.tenantId) && Objects.equals(walletId, that.walletId) && Objects.equals(
				customerId, that.customerId) && Objects.equals(spendingLimit, that.spendingLimit) && Objects.equals(
						currency, that.currency) && Objects.equals(amount, that.amount) && type == that.type && Objects
								.equals(lastAmountUpdatedAt, that.lastAmountUpdatedAt) && Objects.equals(createdAt,
										that.createdAt) && Objects.equals(updatedAt, that.updatedAt) && Objects.equals(
												deletedAt, that.deletedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), tenantId, walletId, customerId, spendingLimit, currency, amount, type,
				lastAmountUpdatedAt, createdAt, updatedAt, deletedAt);
	}
}
