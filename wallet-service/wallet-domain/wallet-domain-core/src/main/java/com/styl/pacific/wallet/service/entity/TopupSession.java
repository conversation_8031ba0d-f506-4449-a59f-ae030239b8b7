/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.TopupSessionId;
import com.styl.pacific.domain.valueobject.WalletId;
import com.styl.pacific.utils.stringtemplate.StringTemplateReplaceUtils;
import com.styl.pacific.wallet.service.constant.TopupPaymentRef;
import com.styl.pacific.wallet.service.enums.TopupSessionStatus;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public class TopupSession extends AggregateRoot<TopupSessionId> {

	public static final String PAYMENT_REF_PATTERN = "${CURRENCY_CODE}#${TOTAL_AMOUNT}#${NONCE}";

	private TenantId tenantId;

	private WalletId walletId;

	private String paymentRef;

	private UUID nonce;

	private String currency;

	private Money amount;

	private String description;

	private TopupSessionStatus status;

	private Integer version;

	private Instant createdAt;

	private Instant updatedAt;

	private Instant deletedAt;

	private TopupSession(Builder builder) {
		setId(builder.id);
		tenantId = builder.tenantId;
		walletId = builder.walletId;
		paymentRef = builder.paymentRef;
		currency = builder.currency;
		amount = builder.amount;
		description = builder.description;
		status = builder.status;
		version = builder.version;
		createdAt = builder.createdAt;
		updatedAt = builder.updatedAt;
		deletedAt = builder.deletedAt;
	}

	public static final class Builder {
		private TopupSessionId id;
		private TenantId tenantId;
		private WalletId walletId;
		private String paymentRef;
		private String currency;
		private Money amount;
		private String description;
		private TopupSessionStatus status;
		private Integer version;
		private Instant createdAt;
		private Instant updatedAt;
		private Instant deletedAt;

		private Builder() {
		}

		public static Builder builder() {
			return new Builder();
		}

		public Builder id(TopupSessionId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder walletId(WalletId walletId) {
			this.walletId = walletId;
			return this;
		}

		public Builder paymentRef(String paymentRef) {
			this.paymentRef = paymentRef;
			return this;
		}

		public Builder currency(String currency) {
			this.currency = currency;
			return this;
		}

		public Builder amount(Money amount) {
			this.amount = amount;
			return this;
		}

		public Builder description(String description) {
			this.description = description;
			return this;
		}

		public Builder status(TopupSessionStatus status) {
			this.status = status;
			return this;
		}

		public Builder version(Integer version) {
			this.version = version;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public TopupSession build() {
			return new TopupSession(this);
		}
	}

	public void pending() {
		this.status = TopupSessionStatus.PENDING;
	}

	public void generatePaymentRef() {
		this.nonce = UUID.randomUUID();
		this.paymentRef = String.format("%s.%s", TopupPaymentRef.PREFIX, DigestUtils.sha256Hex(
				StringTemplateReplaceUtils.replaceParams(PAYMENT_REF_PATTERN, Map.of("CURRENCY_CODE", currency,
						"TOTAL_AMOUNT", amount.toString(), "NONCE", nonce.toString()))));
	}

	public void withStatus(TopupSessionStatus status) {
		this.status = status;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof TopupSession that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(tenantId, that.tenantId) && Objects.equals(walletId, that.walletId) && Objects.equals(
				paymentRef, that.paymentRef) && Objects.equals(nonce, that.nonce) && Objects.equals(currency,
						that.currency) && Objects.equals(amount, that.amount) && Objects.equals(description,
								that.description) && status == that.status && Objects.equals(version, that.version)
				&& Objects.equals(createdAt, that.createdAt) && Objects.equals(updatedAt, that.updatedAt) && Objects
						.equals(deletedAt, that.deletedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), tenantId, walletId, paymentRef, nonce, currency, amount, description,
				status, version, createdAt, updatedAt, deletedAt);
	}
}
