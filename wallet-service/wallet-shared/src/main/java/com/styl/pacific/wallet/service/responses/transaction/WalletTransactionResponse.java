/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.responses.transaction;

import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.wallet.service.enums.TransactionCategory;
import com.styl.pacific.wallet.service.enums.TransactionType;
import java.math.BigInteger;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@Builder
@AllArgsConstructor
public class WalletTransactionResponse {

	private String transactionId;

	private String tenantId;

	private String customerId;

	private String walletId;

	private Long sourceWalletId;

	private Long destinationWalletId;

	private TransactionType transactionType;

	private TransactionCategory transactionCategory;

	private String paymentSessionId;

	private String paymentTransactionId;

	private CurrencyResponse currency;

	private String cardId;

	private BigInteger amount;

	private BigInteger oldBalance;

	private BigInteger balance;

	private Boolean isOffline;

	private Long createdAt;

	private String description;

	private String migrationId;
}
