/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.wallet.service.requests.fundsource.CreateFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.CreateFundSourceSchedulerRequest;
import com.styl.pacific.wallet.service.requests.fundsource.DeleteFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.DeleteFundSourceSchedulerRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FindFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FindFundSourcesSchedulerRequest;
import com.styl.pacific.wallet.service.requests.fundsource.UpdateFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.UpdateFundSourceSchedulerRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FindFundSourceTopupHistoriesRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FindFundSourceTopupTrackingRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceResponse;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceTopupSchedulerResponse;
import com.styl.pacific.wallet.service.responses.fundsource.ListFundSourceResponse;
import com.styl.pacific.wallet.service.responses.fundsource.ListFundSourceTopupHistoryResponse;
import com.styl.pacific.wallet.service.responses.fundsource.ListFundSourceTopupTrackingResponse;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface FundSourcesApi {

	@PostMapping(path = "/api/wallet/fund-sources")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_ADD)
	FundSourceResponse createFundSource(@RequestBody @Valid CreateFundSourceRequest request);

	@GetMapping(path = "/api/wallet/fund-sources")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListFundSourceResponse findFundSources(@ModelAttribute @Valid FindFundSourceRequest request);

	@GetMapping(path = "/api/wallet/fund-sources/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	FundSourceResponse getFundSources(@PathVariable long id);

	@PutMapping(path = "/api/wallet/fund-sources/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_UPDATE)
	FundSourceResponse updateFundSource(@RequestBody @Valid UpdateFundSourceRequest request, @PathVariable long id);

	@DeleteMapping(path = "/api/wallet/fund-sources")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_DELETE)
	void deleteFundSource(@RequestBody @Valid DeleteFundSourceRequest request);

	@PostMapping(path = "/api/wallet/fund-sources/{id}/lock")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_UPDATE)
	FundSourceResponse lockFundSource(@PathVariable long id);

	@PostMapping(path = "/api/wallet/fund-sources/{id}/activate")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_UPDATE)
	FundSourceResponse activateFundSource(@PathVariable long id);

	@PostMapping(path = "/api/wallet/fund-sources-scheduler")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_ADD)
	FundSourceTopupSchedulerResponse createFundSourceTopupScheduler(
			@RequestBody @Valid CreateFundSourceSchedulerRequest request);

	@PutMapping(path = "/api/wallet/fund-sources-scheduler/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_UPDATE)
	FundSourceTopupSchedulerResponse updateFundSourceTopupScheduler(
			@RequestBody @Valid UpdateFundSourceSchedulerRequest request, @PathVariable Long id);

	@GetMapping(path = "/api/wallet/fund-sources-scheduler")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	List<FundSourceTopupSchedulerResponse> findFundSourceTopupSchedulers(
			@ModelAttribute @Valid FindFundSourcesSchedulerRequest request);

	@DeleteMapping(path = "/api/wallet/fund-sources-scheduler")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.FUND_SOURCE_MGMT_DELETE)
	void deleteFundSourceScheduler(@RequestBody @Valid DeleteFundSourceSchedulerRequest request);

	@GetMapping(path = "/api/wallet/fund-sources-topup-history")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListFundSourceTopupHistoryResponse findFundSourceTopupHistories(
			@ModelAttribute @Valid FindFundSourceTopupHistoriesRequest request);

	@GetMapping(path = "/api/wallet/fund-sources-topup-history-record")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListFundSourceTopupTrackingResponse findFundSourceTopupHistoryRecord(
			@ModelAttribute @Valid FindFundSourceTopupTrackingRequest request);
}
