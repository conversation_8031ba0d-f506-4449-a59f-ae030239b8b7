/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.scheduling;

import com.styl.pacific.wallet.service.domain.port.input.service.WalletDomainService;
import com.styl.pacific.wallet.service.scheduling.config.WalletEntryExpiryJobConfig;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@Slf4j
@ConditionalOnProperty(name = "pacific.wallet.wallet-entry-expiry-job-config.enabled", havingValue = "true")
@DependsOn("schedulingConfiguration")
public class WalletEntryExpiryJobScheduler {
	private final WalletDomainService walletDomainService;
	private final WalletEntryExpiryJobConfig jobConfig;

	public WalletEntryExpiryJobScheduler(WalletEntryExpiryJobConfig jobConfig, WalletDomainService commandService) {
		this.walletDomainService = commandService;
		this.jobConfig = jobConfig;
	}

	@Scheduled(cron = "${pacific.wallet.wallet-entry-expiry-job-config.cron-expression}")
	@SchedulerLock(name = "WalletEntry.WalletEntryExpiryCheckJob", lockAtLeastFor = "${pacific.wallet.wallet-entry-expiry-job-config.lock-at-least-for:PT5M}", lockAtMostFor = "${pacific.wallet.wallet-entry-expiry-job-config.lock-at-most-for:PT1H}")
	public void scheduleWalletEntryExpiryCheckJob() {
		LockAssert.assertLocked();
		Instant start = Instant.now();
		log.info("WalletEntryExpiryCheckJob is running at {}", start);

		walletDomainService.processExpiredWalletEntries(jobConfig.getSize());

		Instant end = Instant.now();
		log.info("End WalletEntryExpiryCheckJob is ending at {}, take time {}s", end, end.getEpochSecond() - start
				.getEpochSecond());
	}
}
