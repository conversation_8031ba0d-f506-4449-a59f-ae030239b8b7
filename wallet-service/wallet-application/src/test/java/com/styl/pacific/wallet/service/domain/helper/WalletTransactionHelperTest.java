/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.WalletId;
import com.styl.pacific.wallet.service.domain.dto.walletspendinglimitsetting.FindWalletSpendingLimitSettingsQuery;
import com.styl.pacific.wallet.service.domain.output.repository.WalletSpendingLimitSettingRepository;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletSpendingLimitSetting;
import com.styl.pacific.wallet.service.enums.SpendingLimitType;
import com.styl.pacific.wallet.service.exception.WalletSpendingLimitException;
import java.time.LocalDate;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class WalletTransactionHelperTest {
	@Mock
	private WalletSpendingLimitSettingRepository walletSpendingLimitSettingRepository;

	@InjectMocks
	private WalletTransactionHelper walletTransactionHelper;

	@BeforeEach
	void setUp() {
		// No-op, MockitoExtension handles mocks
	}

	@Test
	void testValidateIndividualWalletSpendingLimit_AmountGreaterThanLimit_IsSameRangeTimeFalse() {
		// Arrange
		Wallet wallet = Mockito.mock(Wallet.class);
		when(wallet.getTenantId()).thenReturn(new TenantId(1L));
		when(wallet.getId()).thenReturn(new WalletId(2L));
		Money amount = new Money(200); // Test amount
		LocalDate requestedDate = LocalDate.now();

		WalletSpendingLimitSetting setting = Mockito.mock(WalletSpendingLimitSetting.class);
		when(setting.getSpendingLimit()).thenReturn(new Money(100)); // Limit is 100
		when(setting.getLastAmountUpdatedAt()).thenReturn(requestedDate.minusDays(1));
		when(setting.getType()).thenReturn(SpendingLimitType.DAY); // Adjust as needed for your logic

		when(walletSpendingLimitSettingRepository.findWalletSpendingLimitSettings(any(
				FindWalletSpendingLimitSettingsQuery.class))).thenReturn(Collections.singletonList(setting));

		// Act & Assert
		assertThrows(WalletSpendingLimitException.class, () -> walletTransactionHelper
				.validateIndividualWalletSpendingLimit(wallet, amount, requestedDate));
	}

	@Test
	void testValidateIndividualWalletSpendingLimit_AmountLessThanOrEqualLimit_IsSameRangeTimeFalse() {
		Wallet wallet = Mockito.mock(Wallet.class);
		when(wallet.getTenantId()).thenReturn(new TenantId(1L));
		when(wallet.getId()).thenReturn(new WalletId(2L));
		Money amount = new Money(50); // Less than limit
		LocalDate requestedDate = LocalDate.now();

		WalletSpendingLimitSetting setting = Mockito.mock(WalletSpendingLimitSetting.class);
		when(setting.getSpendingLimit()).thenReturn(new Money(100));
		when(setting.getLastAmountUpdatedAt()).thenReturn(requestedDate.minusDays(1));
		when(setting.getType()).thenReturn(SpendingLimitType.DAY);

		when(walletSpendingLimitSettingRepository.findWalletSpendingLimitSettings(any(
				FindWalletSpendingLimitSettingsQuery.class))).thenReturn(Collections.singletonList(setting));

		// Should NOT throw
		walletTransactionHelper.validateIndividualWalletSpendingLimit(wallet, amount, requestedDate);
	}

	@Test
	void testValidateIndividualWalletSpendingLimit_AmountPlusPreviousGreaterThanLimit_IsSameRangeTimeTrue() {
		Wallet wallet = Mockito.mock(Wallet.class);
		when(wallet.getTenantId()).thenReturn(new TenantId(1L));
		when(wallet.getId()).thenReturn(new WalletId(2L));
		Money amount = new Money(60);
		LocalDate requestedDate = LocalDate.now();

		WalletSpendingLimitSetting setting = Mockito.mock(WalletSpendingLimitSetting.class);
		when(setting.getSpendingLimit()).thenReturn(new Money(100));
		when(setting.getAmount()).thenReturn(new Money(50)); // Previous amount
		when(setting.getLastAmountUpdatedAt()).thenReturn(requestedDate);
		when(setting.getType()).thenReturn(SpendingLimitType.DAY);

		when(walletSpendingLimitSettingRepository.findWalletSpendingLimitSettings(any(
				FindWalletSpendingLimitSettingsQuery.class))).thenReturn(Collections.singletonList(setting));

		assertThrows(WalletSpendingLimitException.class, () -> walletTransactionHelper
				.validateIndividualWalletSpendingLimit(wallet, amount, requestedDate));
	}

	@Test
	void testValidateIndividualWalletSpendingLimit_NoSpendingLimitSettings() {
		Wallet wallet = Mockito.mock(Wallet.class);
		when(wallet.getTenantId()).thenReturn(new TenantId(1L));
		when(wallet.getId()).thenReturn(new WalletId(2L));
		Money amount = new Money(100);
		LocalDate requestedDate = LocalDate.now();

		when(walletSpendingLimitSettingRepository.findWalletSpendingLimitSettings(any(
				FindWalletSpendingLimitSettingsQuery.class))).thenReturn(Collections.emptyList());

		// Should NOT throw
		walletTransactionHelper.validateIndividualWalletSpendingLimit(wallet, amount, requestedDate);
	}

	@Test
	void testValidateIndividualWalletSpendingLimit_SpendingLimitIsZero() {
		Wallet wallet = Mockito.mock(Wallet.class);
		when(wallet.getTenantId()).thenReturn(new TenantId(1L));
		when(wallet.getId()).thenReturn(new WalletId(2L));
		Money amount = new Money(100);
		LocalDate requestedDate = LocalDate.now();

		WalletSpendingLimitSetting setting = Mockito.mock(WalletSpendingLimitSetting.class);
		when(setting.getSpendingLimit()).thenReturn(new Money(0)); // Limit is zero

		when(walletSpendingLimitSettingRepository.findWalletSpendingLimitSettings(any(
				FindWalletSpendingLimitSettingsQuery.class))).thenReturn(Collections.singletonList(setting));

		// Should NOT throw
		walletTransactionHelper.validateIndividualWalletSpendingLimit(wallet, amount, requestedDate);
	}
}
