/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCreatedEventAvroModel;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionStatus;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionType;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import com.styl.pacific.user.shared.http.users.response.UserResponse;
import com.styl.pacific.wallet.service.config.IntegrationTestConfiguration;
import com.styl.pacific.wallet.service.enums.TopupSessionStatus;
import com.styl.pacific.wallet.service.enums.WalletType;
import com.styl.pacific.wallet.service.requests.topupsession.CreateTopupSessionRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreateWalletRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreditRequest;
import com.styl.pacific.wallet.service.requests.wallet.UpdateSpendingLimitRequest;
import com.styl.pacific.wallet.service.responses.topupsession.TopupSessionResponse;
import com.styl.pacific.wallet.service.responses.wallet.WalletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Currency;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.reactive.server.FluxExchangeResult;

/**
 * <AUTHOR>
 */

@TestMethodOrder(OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
@ActiveProfiles(value = "local")
class SpendingLimitControllerIntegrationTest extends BaseWebClientWithDbTest implements KafkaContainerTest {

	private static Long walletId = null;
	private static Long tenantId = 1L;
	private ObjectMapper objectMapper = new ObjectMapper();
	private static String paymentRef = null;
	private final Long customerId = 123L;
	private final String paymentSessionId1 = "123";
	private final String paymentSessionId2 = "1234";
	private final String paymentSessionId3 = "pay12345";
	private final LocalDate now = LocalDate.now();

	@Autowired
	@Qualifier("tenant-service")
	private WireMockServer tenantServiceMock;

	@Autowired
	@Qualifier("user-service")
	private WireMockServer userServiceMock;

	@Autowired
	private KafkaProducer<UUID, PaymentSettlementCreatedEventAvroModel> producer;

	@Value(value = "${pacific.kafka.wallet-service.consumers.payment-settlement-created-event.group-id}")
	private String ID;

	@Value(value = "${pacific.kafka.wallet-service.consumers.payment-settlement-created-event.topic-name}")
	private String TOPIC;

	@Order(1)
	@Test
	void createWalletShouldSuccess() throws Exception {
		// Arrange
		TenantResponse responseTenant = TenantResponse.builder()
				.tenantId(tenantId)
				.name("Test Tenant")
				.settings(TenantSettingsResponse.builder()
						.currency(new CurrencyResponse(Currency.getInstance("VND")))
						.build())
				.build();

		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + tenantId))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(responseTenant))));

		UserResponse userResponse = UserResponse.builder()
				.id(customerId.toString())
				.email("<EMAIL>")
				.build();
		userServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching(String.format("/api/user/users/%s/profile",
				customerId)))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(userResponse))));

		CreateWalletRequest request = CreateWalletRequest.builder()
				.customerId(customerId)
				.type(WalletType.DEPOSIT)
				.build();

		// Act & Assert
		FluxExchangeResult<WalletResponse> response = webClient.post()
				.uri("/api/wallet/wallets")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(request))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(WalletResponse.class);

		WalletResponse walletResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(walletResponse);
		walletId = Long.valueOf(walletResponse.getWalletId());
		Assertions.assertNotNull(walletId);
	}

	@Order(2)
	@Test
	void updateSpendingLimitShouldSuccess() throws Exception {
		// Arrange
		UpdateSpendingLimitRequest updateSpendingLimitRequest = UpdateSpendingLimitRequest.builder()
				.spendingLimit(BigInteger.valueOf(10000L))
				.build();

		// Act & Assert
		FluxExchangeResult<WalletResponse> response = webClient.put()
				.uri("/api/wallet/wallets/{id}/spending-limit", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(updateSpendingLimitRequest))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(WalletResponse.class);

		WalletResponse walletResponse = response.getResponseBody()
				.blockFirst();
		Assertions.assertNotNull(walletResponse);
		assertEquals(BigInteger.valueOf(10000L), walletResponse.getSpendingLimit());
	}

	@Order(3)
	@Test
	void topupThenSuccess() throws JsonProcessingException, ExecutionException, InterruptedException {

		// topupSession
		CreateTopupSessionRequest createTopupSessionRequest = CreateTopupSessionRequest.builder()
				.walletId(walletId.toString())
				.currency("VND")
				.amount(BigInteger.valueOf(100000))
				.build();

		// Act & Assert
		FluxExchangeResult<TopupSessionResponse> dataTopup = webClient.post()
				.uri("api/wallet/topup-sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(createTopupSessionRequest))
				.exchange()
				.expectStatus()
				.isOk()
				.returnResult(TopupSessionResponse.class);

		TopupSessionResponse topupResponse = dataTopup.getResponseBody()
				.blockFirst();
		assertNotNull(topupResponse);
		assertNotNull(topupResponse.getTopupSessionId());
		assertEquals(TopupSessionStatus.PENDING, topupResponse.getStatus());
		paymentRef = topupResponse.getPaymentRef();

		// update statusTopUp from PaymentService
		PaymentSettlementCreatedEventAvroModel settlementAvroEvent = PaymentSettlementCreatedEventAvroModel.newBuilder()
				.setAmount(110000)
				.setTenantId(tenantId)
				.setPaymentMethodId(1234L)
				.setTransactionType(PaymentTransactionType.TOP_UP)
				.setPaymentSessionId("12345")
				.setPaymentTransactionId("1234567")
				.setId(UUID.randomUUID())
				.setCurrencyCode("VND")
				.setCustomerId(customerId.toString())
				.setAppliedFixedSurcharge(123L)
				.setCustomerEmail("<EMAIL>")
				.setAppliedSurchargeRate(BigDecimal.valueOf(0.2))
				.setFee(12L)
				.setPaidAt(Instant.now()
						.toEpochMilli())
				.setPaymentMethodDisplayName("PayNow")
				.setNetAmount(100000)
				.setPaymentReference(paymentRef)
				.setTransactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.setPaymentProcessorId("123")
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.setPaidAt(Instant.now()
						.toEpochMilli())
				.setSystemSource("Wallet-Service")
				.setDescription("test")
				.setIsAsync(true)
				.setInitiatedAt(Instant.now()
						.toEpochMilli())
				.setCreatedBy(customerId)
				.setSessionVersion(123L)
				.build();
		// Act
		CompletableFuture<SendResult<UUID, PaymentSettlementCreatedEventAvroModel>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, settlementAvroEvent.getId(), settlementAvroEvent, (
				uuidTenantCreatedEventAvroModelSendResult, throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidTenantCreatedEventAvroModelSendResult);
			}
		});
		assertNotNull(completableFuture.get());
		Thread.sleep(10000);
	}

	@Order(4)
	@Test
	void testPayWhenExceedSpendingLimitShouldFail() throws Exception {
		// pay with 90000 > spendingLimit (10000) should fail
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId1)
				.currency("VND")
				.amount(BigInteger.valueOf(90000))
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isBadRequest();
	}

	@Order(5)
	@Test
	void testPayWhenLessThanSpendingLimitShouldSuccess() throws Exception {
		// pay with 5000 < spendingLimit (10000) should success
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId1)
				.currency("VND")
				.amount(BigInteger.valueOf(5000))
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isOk();
	}

	@Order(6)
	@Test
	void testPayWhenExceedSpendingLimitRemainShouldFail() throws Exception {
		// pay with 6000 > remain spendingLimit (5000) should fail
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId2)
				.currency("VND")
				.amount(BigInteger.valueOf(6000))
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isBadRequest();
	}

	@Order(7)
	@Test
	void testPayWhenEqualSpendingLimitRemainShouldSuccess() throws Exception {
		// pay with 5000 = remain spendingLimit (5000) should success
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId2)
				.currency("VND")
				.amount(BigInteger.valueOf(5000))
				.requestedAt(now.toString())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isOk();
	}

	@Order(8)
	@Test
	void testPayWhenExceedLimitRemainShouldFail() throws Exception {
		// pay with 1000 = remain spendingLimit (0) should success
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId3)
				.currency("VND")
				.amount(BigInteger.valueOf(1000))
				.requestedAt(now.toString())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isBadRequest();
	}

	@Order(9)
	@Test
	void testPayWithNexDayAndLessThanSpendingLimitShouldSuccess() throws Exception {
		// pay with 1000 <  spendingLimit (10000) should success
		CreditRequest creditRequest = CreditRequest.builder()
				.paymentSessionId(paymentSessionId3)
				.currency("VND")
				.amount(BigInteger.valueOf(1000))
				.requestedAt(now.plusDays(1)
						.toString())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/wallet/wallets/{id}/pay", walletId)
				.contentType(MediaType.APPLICATION_JSON)
				.headers(httpHeaders -> httpHeaders.setAll(getHttpHeaders()))
				.bodyValue(objectMapper.writeValueAsString(creditRequest))
				.exchange()
				.expectStatus()
				.isOk();
	}

	private Map<String, String> getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		map.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(tenantId));
		return map;
	}
}
