/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.consumer;

import com.styl.pacific.kafka.consumer.KafkaConsumer;
import com.styl.pacific.kafka.users.avro.model.UserCreatedEvent;
import com.styl.pacific.kafka.users.avro.model.UserType;
import com.styl.pacific.wallet.service.domain.dto.wallet.CreateWalletCommandDto;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletDomainService;
import com.styl.pacific.wallet.service.enums.WalletType;
import com.styl.pacific.wallet.service.mapper.WalletMessagingDataMapper;
import jakarta.annotation.PostConstruct;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@ConditionalOnProperty(name = "pacific.kafka.wallet-service.consumers.user-created-event.enabled", havingValue = "true")
public class UserCreatedEventKafkaConsumer implements KafkaConsumer<UUID, UserCreatedEvent> {
	private static final Logger logger = LoggerFactory.getLogger(UserCreatedEventKafkaConsumer.class);

	private final WalletDomainService walletDomainService;

	@PostConstruct
	public void init() {
		logger.info("Constructed UserCreatedEventKafkaConsumer");
	}

	@Override
	@RetryableTopic(backoff = @Backoff(delayExpression = "#{${pacific.kafka.wallet-service.consumers.retry-interval-ms}}"), attempts = "${pacific.kafka.wallet-service.consumers.retry-attempts}", autoCreateTopics = "${pacific.kafka.wallet-service.consumers.retry-auto-create-topics}")
	@KafkaListener(id = "${pacific.kafka.wallet-service.consumers.user-created-event.group-id}", topics = "${pacific.kafka.wallet-service.consumers.user-created-event.topic-name}")
	public void receive(UserCreatedEvent event, UUID key, Integer partition, Long offset) {
		if (event.getUserType()
				.equals(UserType.CUSTOMER)) {
			CreateWalletCommandDto dto = WalletMessagingDataMapper.INSTANCE.toCreateWalletCommandDto(event,
					WalletType.DEPOSIT);
			walletDomainService.createWallet(dto);
		}
	}
}
