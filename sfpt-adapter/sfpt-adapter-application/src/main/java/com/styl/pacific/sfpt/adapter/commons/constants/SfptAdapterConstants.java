/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.sfpt.adapter.commons.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class SfptAdapterConstants {
	public static final String CSV_PARENT_FILE_NAME = "Parents.csv";
	public static final String CSV_STUDENT_FILE_NAME = "Students.csv";
	public static final String CSV_CARD_FILE_NAME = "Cards.csv";

	public static final String CSV_PACIFIC_CUSTOMER_DATA_FILE_NAME = "customers.csv";
	public static final String CSV_PACIFIC_GROUP_DATA_FILE_NAME = "groups.csv";
	public static final String CSV_PACIFIC_CARD_DATA_FILE_NAME = "cards.csv";
	public static final int BATCH_RECORD_MAX_SIZE = 200;
}
