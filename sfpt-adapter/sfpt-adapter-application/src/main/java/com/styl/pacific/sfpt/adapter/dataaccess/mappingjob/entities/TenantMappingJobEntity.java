/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.sfpt.adapter.dataaccess.mappingjob.entities;

import com.styl.pacific.sfpt.adapter.features.mappingjob.entities.TenantMappingJobStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@EntityListeners(AuditingEntityListener.class)
@DynamicUpdate
@Table(name = "tb_tenant_mapping_jobs")
public class TenantMappingJobEntity {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@NotNull
	@Column(name = "tenant_id")
	private Long tenantId;

	@Column(name = "status")
	@Builder.Default
	@Enumerated(EnumType.STRING)
	private TenantMappingJobStatus status = TenantMappingJobStatus.CREATED;

	@Column(name = "timezone")
	private String timezone;

	@Length(max = 350)
	@Column(name = "main_tenant_user_directory_path")
	private String mainTenantUserDirectoryPath;

	@Length(max = 350)
	@Column(name = "date_time_directory_path")
	private String dateTimeDirectoryPath;

	@Length(max = 350)
	@Column(name = "date_time_uploaded_directory_path")
	private String dateTimeUploadedDirectoryPath;

	@Length(max = 350)
	@Column(name = "date_time_photos_directory_path")
	private String dateTimePhotosDirectoryPath;

	@Length(max = 350)
	@Column(name = "mapped_data_directory_path")
	private String mappedDataDirectoryPath;

	@Length(max = 350)
	@Column(name = "zip_file_path")
	private String zipFilePath;

	@Length(max = 350)
	@Column(name = "zip_checksum_file_path")
	private String zipChecksumFilePath;

	@Length(max = 500)
	@Column(name = "data_sync_s3_landing_path")
	private String dataSyncS3LandingPath;

	@Column(name = "error_message")
	private String errorMessage;

	@Column(name = "stacktrace")
	private String stacktrace;

	@CreatedDate
	@Column(name = "created_at")
	private Instant createdAt;

	@LastModifiedDate
	@Column(name = "updated_at")
	private Instant updatedAt;

}