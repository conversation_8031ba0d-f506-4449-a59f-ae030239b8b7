/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.lambda.datasync.clients.s3.AmazonS3ClientService;
import com.styl.pacific.lambda.datasync.clients.s3.entities.DownloadedFileMetadata;
import com.styl.pacific.lambda.datasync.commons.domains.QueueJobMessage;
import com.styl.pacific.lambda.datasync.exceptions.DataSyncDomainException;
import com.styl.pacific.lambda.datasync.exceptions.S3FileNotExistedException;
import com.styl.pacific.lambda.datasync.hash.Md5FileHashDigester;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@Slf4j
@RequiredArgsConstructor
public class AmazonS3ClientServiceImpl implements AmazonS3ClientService {

	private final S3Client s3Client;
	private final ObjectMapper objectMapper = new ObjectMapper();

	@Override
	public DownloadedFileMetadata downloadFileToTmp(String bucket, String objectKey) {

		if (!existObjectKey(bucket, objectKey)) {
			throw new S3FileNotExistedException("Object key %s doesn't exist".formatted(objectKey));
		}

		final var localFilePathStr = "/tmp/%s".formatted(objectKey);
		final var localFilePath = Paths.get(localFilePathStr);

		final var parentDir = localFilePath.getParent()
				.toFile();
		if (!parentDir.exists()) {
			parentDir.mkdirs();
		}

		log.info("Downloading Object Key {}", objectKey);
		final var objectMetadata = s3Client.getObject(GetObjectRequest.builder()
				.bucket(bucket)
				.key(objectKey)
				.build(), localFilePath);

		log.info("Downloaded File Path at {}", localFilePathStr);
		return DownloadedFileMetadata.builder()
				.objectKey(objectKey)
				.localFilePath(localFilePath)
				.objectMetadata(objectMetadata)
				.build();
	}

	public boolean existObjectKey(String bucket, String key) {
		try {
			s3Client.headObject(HeadObjectRequest.builder()
					.bucket(bucket)
					.key(key)
					.build());
			return true;
		} catch (NoSuchKeyException e) {
			return false;
		}
	}

	@Override
	public void uploadFolderToDirectoryKey(Path localDirectoryPath, String bucket, String targetDirectoryKey) {
		final var contentMD5MessageDigester = new Md5FileHashDigester();
		try (Stream<Path> files = Files.walk(localDirectoryPath)) {
			files.filter(Files::isRegularFile)
					.forEach(file -> {
						final var relativePath = localDirectoryPath.relativize(file)
								.toString()
								.replace("\\", "/");
						final var s3ObjectKey = "%s/%s".formatted(targetDirectoryKey, relativePath);

						log.debug("Uploading: %s -> %s".formatted(file, s3ObjectKey));
						s3Client.putObject(PutObjectRequest.builder()
								.bucket(bucket)
								.key(s3ObjectKey)
								.contentMD5(contentMD5MessageDigester.digestToBase64(file))
								.build(), RequestBody.fromFile(file));
					});
		} catch (IOException e) {
			throw new DataSyncDomainException(e.getMessage(), e);
		}
	}

	@Override
	public String moveObjectToDirectoryKey(String bucket, String directoryKey, String objectKey) {
		log.info("Start copying object key %s to bucket: %s, directory: %s".formatted(objectKey, bucket, directoryKey));

		if (StringUtils.isBlank(objectKey)) {
			throw new DataSyncDomainException("ObjectKey is required");
		}

		if (!existObjectKey(bucket, objectKey)) {
			throw new DataSyncDomainException("ObjectKey %s has not existed".formatted(objectKey));
		}

		final var objectNameParts = objectKey.split("/");
		final var targetObjectKey = "%s/%s".formatted(directoryKey, objectNameParts[objectNameParts.length - 1]);

		s3Client.copyObject(CopyObjectRequest.builder()
				.sourceBucket(bucket)
				.sourceKey(objectKey)
				.destinationBucket(bucket)
				.destinationKey(targetObjectKey)
				.build());

		log.info("Completed copying object key %s to bucket: %s, target ObjectKey: %s".formatted(objectKey, bucket,
				targetObjectKey));

		s3Client.deleteObject(DeleteObjectRequest.builder()
				.bucket(bucket)
				.key(objectKey)
				.build());
		log.info("Completed deleted object key %s to bucket: %s".formatted(objectKey, bucket));

		return targetObjectKey;
	}

	@SneakyThrows
	@Override
	public void pushQueueDirectory(String bucket, String targetDirectoryKey, QueueJobMessage message) {
		final var queuingObjectKey = "%s.json".formatted(targetDirectoryKey);
		log.info("Pushing %s in queue".formatted(queuingObjectKey));

		s3Client.putObject(PutObjectRequest.builder()
				.bucket(bucket)
				.key(queuingObjectKey)
				.build(), RequestBody.fromBytes(objectMapper.writeValueAsBytes(message)));
		log.info("Pushed %s in queue".formatted(queuingObjectKey));
	}
}
