/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.processors;

import com.styl.pacific.lambda.datasync.chains.DataSyncChainFlowData;
import com.styl.pacific.lambda.datasync.clients.s3.AmazonS3ClientService;
import com.styl.pacific.lambda.datasync.commons.checksum.ChecksumAlgorithm;
import com.styl.pacific.lambda.datasync.commons.idgenerator.DataSyncFileIdGenerator;
import com.styl.pacific.lambda.datasync.exceptions.DataSyncDomainException;
import com.styl.pacific.lambda.datasync.exceptions.S3FileNotExistedException;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@RequiredArgsConstructor
public class DataSyncFilePuller implements DataSyncProcessor {
	private final AmazonS3ClientService s3ClientService;
	private final DataSyncFileCleaner dataSyncFileCleaner;

	@Override
	public DataSyncChainFlowData process(DataSyncChainFlowData data) {
		final var bucket = data.getS3NotificationRecord()
				.getS3()
				.getBucket();
		final var objectKey = data.getS3NotificationRecord()
				.getS3()
				.getObject()
				.getKey();

		if (StringUtils.isBlank(objectKey)) {
			throw new DataSyncDomainException("Object Key is required");
		}

		if (StringUtils.isBlank(bucket.getName())) {
			throw new DataSyncDomainException("Bucket Name is required");
		}

		final var objectPath = stripExtension(objectKey);
		final var tenantId = extractTenantId(objectPath);

		final var checksumKey = Arrays.stream(ChecksumAlgorithm.values())
				.map(checksumAlgorithm -> "%s.%s.checksum".formatted(objectPath, checksumAlgorithm.name()
						.toLowerCase()))
				.filter(checksumFileKey -> s3ClientService.existObjectKey(bucket.getName(), checksumFileKey))
				.findFirst()
				.orElseThrow(() -> new S3FileNotExistedException("Checksum file %s has not found".formatted(
						objectPath)));

		final var downloadedChecksumMetadata = s3ClientService.downloadFileToTmp(bucket.getName(), checksumKey);

		final var zipFileKey = "%s.zip".formatted(objectPath);
		final var downloadedZipMetadata = s3ClientService.downloadFileToTmp(bucket.getName(), zipFileKey);

		final var jobId = DataSyncFileIdGenerator.generate();

		final var archivedTargetDirectoryKey = "archived/%s-%s".formatted(jobId, tenantId);

		return data.withJobId(jobId)
				.withArchivedDirectory(archivedTargetDirectoryKey)
				.withTenantId(tenantId)
				.withFileName(extractFileName(objectPath))
				.withZipFileMetadata(downloadedZipMetadata)
				.withChecksumFileMetadata(downloadedChecksumMetadata);
	}

	private String stripExtension(String objectKey) {
		return objectKey.contains(".checksum")
				? FilenameUtils.removeExtension(FilenameUtils.removeExtension(objectKey))
				: FilenameUtils.removeExtension(objectKey);
	}

	@Override
	public void handleError(DataSyncChainFlowData data, Exception exception) {
		final var objectKey = data.getS3NotificationRecord()
				.getS3()
				.getObject()
				.getKey();

		final var objectPath = stripExtension(objectKey);
		final var zipFileKey = "%s.zip".formatted(objectPath);

		Arrays.stream(ChecksumAlgorithm.values())
				.map(checksumAlgorithm -> "%s.%s.checksum".formatted(objectPath, checksumAlgorithm.name()
						.toLowerCase()))
				.forEach(dataSyncFileCleaner::tryCleanTempLocalFile);

		dataSyncFileCleaner.tryCleanTempLocalFile(zipFileKey);
	}

	private String extractFileName(String objectPath) {
		// Example: landing/0123456789/20250205-student-data
		if (StringUtils.isBlank(objectPath)) {
			throw new DataSyncDomainException("Object Path is required");
		}

		final var parts = objectPath.split("/");
		return parts[parts.length - 1];

	}

	private String extractTenantId(String objectPath) {
		// Example: landing/0123456789/20250205-student-data
		if (StringUtils.isBlank(objectPath)) {
			throw new DataSyncDomainException("Object Path is required");
		}

		final var parts = objectPath.split("/");
		if (parts.length == 1) {
			throw new DataSyncDomainException("TenantId has not found from key %s".formatted(objectPath));
		}
		final var tenantId = parts[1];
		try {
			Long.valueOf(tenantId);
		} catch (Exception exception) {
			throw new DataSyncDomainException("TenantId must be number");
		}

		return parts[1];
	}
}
