{"openapi": "3.1.0", "info": {"title": "utility-service", "description": "Build time: 2025-06-11T23:14:10.557Z", "contact": {"name": "<EMAIL>", "email": "<EMAIL>"}, "version": "1.2.5"}, "servers": [{"url": "http://localhost:9208", "description": "Generated server url"}, {"url": "http://pacific-ii-sit.styl.solutions"}], "paths": {"/api/utility/presets/{presetId}": {"get": {"tags": ["preset-controller"], "operationId": "getPreset", "parameters": [{"name": "presetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresetResponse"}}}}}}, "put": {"tags": ["preset-controller"], "operationId": "updatePreset", "parameters": [{"name": "presetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePresetRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresetResponse"}}}}}}, "delete": {"tags": ["preset-controller"], "operationId": "deletePreset", "parameters": [{"name": "presetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/utility/presets": {"get": {"tags": ["preset-controller"], "operationId": "findPresets", "parameters": [{"name": "filter.id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["UPLOADING", "UPLOADED", "IMPORTING", "COMPLETED", "ERROR"]}}}, {"name": "filter.type", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CUSTOMER", "PRODUCT", "MENU", "PRE_ORDER_MENU"]}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingPresetResponse"}}}}}}, "post": {"tags": ["preset-controller"], "operationId": "createPreset", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePresetRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresetResponse"}}}}}}}, "/api/utility/presets/{presetId}/import": {"post": {"tags": ["preset-controller"], "operationId": "startImport", "parameters": [{"name": "presetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresetResponse"}}}}}}}, "/api/utility/dashboards/configuration": {"get": {"tags": ["dashboard-config-controller"], "operationId": "getDashboardConfig", "parameters": [{"name": "tenantOnly", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DashboardConfigResponse"}}}}}}, "post": {"tags": ["dashboard-config-controller"], "operationId": "updateDashboardConfig", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDashboardRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DashboardConfigResponse"}}}}}}}, "/api/utility/presets/{presetId}/records": {"get": {"tags": ["preset-controller"], "operationId": "getRecords", "parameters": [{"name": "presetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "filter", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresetPaging"}}}}}}}, "/api/utility/presets/sample/download": {"get": {"tags": ["preset-controller"], "operationId": "downloadSample", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/FindPresetSampleQuery"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/utility/metadata/timezones": {"get": {"tags": ["metadata-controller"], "operationId": "getTimezonesMetadata", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TimezoneResponse"}}}}}}}}, "/api/utility/metadata/time-formats": {"get": {"tags": ["metadata-controller"], "operationId": "getTimeFormatMetadata", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}}}}}, "/api/utility/metadata/date-formats": {"get": {"tags": ["metadata-controller"], "operationId": "getDateFormatMetadata", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}}}}}, "/api/utility/metadata/currencies": {"get": {"tags": ["metadata-controller"], "operationId": "getCurrenciesMetadata", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyResponse"}}}}}}}}, "/api/utility/metadata/countries": {"get": {"tags": ["metadata-controller"], "operationId": "getCountriesMetadata", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CountryResponse"}}}}}}}}, "/api/utility/metadata/business-types": {"get": {"tags": ["metadata-controller"], "operationId": "getBusinessTypes", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/utility/files/preset/upload/pre-signed-url": {"get": {"tags": ["file-utility-controller"], "operationId": "getPresignedUrlUploadPreset", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresignedUrlResponse"}}}}}}}, "/api/utility/files/images/upload/pre-signed-url": {"get": {"tags": ["file-utility-controller"], "operationId": "getPresignedUrlUploadImage", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresignedUrlResponse"}}}}}}}, "/api/utility/files/download/pre-signed-url": {"get": {"tags": ["file-utility-controller"], "operationId": "getPresignedUrlDownloadFile", "parameters": [{"name": "filePath", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PresignedUrlResponse"}}}}}}}, "/api/utility/data-sync/jobs": {"get": {"tags": ["data-sync-controller"], "operationId": "queryDataSyncJobs", "parameters": [{"name": "filter.byStatuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["NOT_STARTED", "LOADING", "LOADED", "IN_PROGRESS", "COMPLETED", "ERROR"]}, "uniqueItems": true}}, {"name": "filter.byArchivedZipFileName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingDataSyncJobResponse"}}}}}}}, "/api/utility/data-sync/jobs/{jobId}": {"get": {"tags": ["data-sync-controller"], "operationId": "getDataSyncJob", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataSyncJobResponse"}}}}}}}, "/api/utility/data-sync/jobs/{jobId}/records": {"get": {"tags": ["data-sync-controller"], "operationId": "queryDataSyncRecords", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.byType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["GROUP", "CUSTOMER", "CARD"]}}, {"name": "filter.byDataKeywords", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.byStatuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["VALIDATED", "IN_PROGRESS", "ERROR", "COMPLETED"]}, "uniqueItems": true}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingDataSyncRecordResponse"}}}}}}}, "/api/utility/dashboards/{id}/token": {"get": {"tags": ["dashboard-config-controller"], "operationId": "getDashboardToken", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DashboardTokenResponse"}}}}}}}, "/api/utility/config/domain": {"get": {"tags": ["config-controller"], "operationId": "getConfig", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ConfigurationResponse"}}}}}}}, "/api/utility/config/contact": {"get": {"tags": ["config-controller"], "operationId": "getContact", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContactResponse"}}}}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}}, "UpdatePresetRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "strategy": {"type": "string", "enum": ["UPSERT", "INSERT_ONLY", "UPDATE_ONLY"]}}, "required": ["strategy"]}, "CustomerPresetMetadataPayload": {"allOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}]}, "FileResponse": {"type": "object", "properties": {"path": {"type": "string"}, "url": {"type": "string"}}}, "MenuPresetMetadataPayload": {"allOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}, {"type": "object", "properties": {"menuId": {"type": "string"}}}], "required": ["menuId"]}, "PreOrderMenuPresetMetadataPayload": {"allOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}, {"type": "object", "properties": {"preOrderMenuId": {"type": "string"}}}], "required": ["preOrderMenuId"]}, "PresetMetadataPayload": {"type": "object", "discriminator": {"propertyName": "type"}, "properties": {"type": {"type": "string", "enum": ["CUSTOMER", "PRODUCT", "MENU", "PRE_ORDER_MENU"]}}}, "PresetResponse": {"type": "object", "properties": {"file": {"$ref": "#/components/schemas/FileResponse"}, "id": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "fileName": {"type": "string"}, "strategy": {"type": "string", "enum": ["UPSERT", "INSERT_ONLY", "UPDATE_ONLY"]}, "status": {"type": "string", "enum": ["UPLOADING", "UPLOADED", "IMPORTING", "COMPLETED", "ERROR"]}, "presetType": {"type": "string", "enum": ["CUSTOMER", "PRODUCT", "MENU", "PRE_ORDER_MENU"]}, "totalRecords": {"type": "integer", "format": "int64"}, "successRecords": {"type": "integer", "format": "int64"}, "failedRecords": {"type": "integer", "format": "int64"}, "metadata": {"oneOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}, {"$ref": "#/components/schemas/CustomerPresetMetadataPayload"}, {"$ref": "#/components/schemas/MenuPresetMetadataPayload"}, {"$ref": "#/components/schemas/PreOrderMenuPresetMetadataPayload"}, {"$ref": "#/components/schemas/ProductPresetMetadataPayload"}]}, "createdAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "updatedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}}}, "ProductPresetMetadataPayload": {"allOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}]}, "CreatePresetRequest": {"type": "object", "properties": {"filePath": {"type": "string", "minLength": 1}, "name": {"type": "string", "minLength": 1}, "description": {"type": "string"}, "fileName": {"type": "string", "minLength": 1}, "strategy": {"type": "string", "enum": ["UPSERT", "INSERT_ONLY", "UPDATE_ONLY"]}, "presetType": {"type": "string", "enum": ["CUSTOMER", "PRODUCT", "MENU", "PRE_ORDER_MENU"]}, "metadata": {"oneOf": [{"$ref": "#/components/schemas/PresetMetadataPayload"}, {"$ref": "#/components/schemas/CustomerPresetMetadataPayload"}, {"$ref": "#/components/schemas/MenuPresetMetadataPayload"}, {"$ref": "#/components/schemas/PreOrderMenuPresetMetadataPayload"}, {"$ref": "#/components/schemas/ProductPresetMetadataPayload"}]}}, "required": ["metadata", "presetType", "strategy"]}, "ReportConfig": {"type": "object", "properties": {"reportName": {"type": "string"}, "reportId": {"type": "string"}, "reportParams": {"type": "string"}, "dashboardHeight": {"type": "integer", "format": "int32"}}}, "UpdateDashboardRequest": {"type": "object", "properties": {"baseUrl": {"type": "string"}, "secretKey": {"type": "string"}, "dashboardId": {"type": "string"}, "dashboardParams": {"type": "string"}, "dashboardHeight": {"type": "integer", "format": "int32"}, "reports": {"type": "array", "items": {"$ref": "#/components/schemas/ReportConfig"}}}}, "DashboardConfigResponse": {"type": "object", "properties": {"baseUrl": {"type": "string"}, "secretKey": {"type": "string"}, "dashboardId": {"type": "string"}, "dashboardParams": {"type": "string"}, "dashboardHeight": {"type": "integer", "format": "int32"}, "reports": {"type": "array", "items": {"$ref": "#/components/schemas/ReportConfig"}}}}, "PagingPresetResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PresetResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "PresetPaging": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PresetRecord"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}}}, "PresetRecord": {"type": "object", "properties": {"tenantId": {"type": "integer", "format": "int64"}, "presetId": {"type": "string"}, "row": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "status": {"type": "string", "enum": ["SUCCESS", "ERROR"]}, "remarks": {"type": "string"}, "processedAt": {"type": "integer", "format": "int64"}}}, "FindPresetSampleQuery": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/PresetSampleFilter"}}}, "PresetSampleFilter": {"type": "object", "properties": {"type": {"type": "string", "enum": ["CUSTOMER", "PRODUCT", "MENU", "PRE_ORDER_MENU"]}}}, "TimezoneResponse": {"type": "object", "properties": {"zoneId": {"type": "string"}, "gtmOffset": {"type": "string"}, "displayName": {"type": "string"}}}, "CurrencyResponse": {"type": "object", "properties": {"displayName": {"type": "string"}, "numericCode": {"type": "integer", "format": "int32"}, "currencyCode": {"type": "string"}, "symbol": {"type": "string"}, "fractionDigits": {"type": "integer", "format": "int32"}}}, "CountryResponse": {"type": "object", "properties": {"countryCode": {"type": "string"}, "countryName": {"type": "string"}}}, "PresignedUrlResponse": {"type": "object", "properties": {"presignedUrl": {"type": "string"}, "filePath": {"type": "string"}}}, "ColumDefinition": {"type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "key": {"type": "string"}}}, "CsvDataSchemaResponse": {"type": "object", "properties": {"columDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/ColumDefinition"}}, "headerRowIndex": {"type": "integer", "format": "int32"}}}, "DataSyncJobResponse": {"type": "object", "properties": {"jobId": {"type": "string"}, "tenantId": {"type": "string"}, "archivedZipFilePath": {"type": "string"}, "archivedChecksumFilePath": {"type": "string"}, "archivedFileDirectory": {"type": "string"}, "zipFileChecksum": {"type": "string"}, "status": {"type": "string", "enum": ["NOT_STARTED", "LOADING", "LOADED", "IN_PROGRESS", "COMPLETED", "ERROR"]}, "errorMessage": {"type": "string"}, "recordTracking": {"$ref": "#/components/schemas/DataSyncRecordTrackingResponse"}, "groupCsvSchema": {"$ref": "#/components/schemas/CsvDataSchemaResponse"}, "customerCsvSchema": {"$ref": "#/components/schemas/CsvDataSchemaResponse"}, "cardCsvSchema": {"$ref": "#/components/schemas/CsvDataSchemaResponse"}, "createdAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "updatedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "manifestContent": {"$ref": "#/components/schemas/DataSyncManifestContentResponse"}, "validChecksum": {"type": "boolean"}}}, "DataSyncManifestContentResponse": {"type": "object", "properties": {"customerCsvPath": {"type": "string"}, "groupCsvPath": {"type": "string"}, "description": {"type": "string"}}}, "DataSyncRecordTrackingResponse": {"type": "object", "properties": {"groupCsvLoadingStartedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "groupCsvLoadingCompletedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "customerCsvLoadingStartedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "customerCsvLoadingCompletedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}}}, "PagingDataSyncJobResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/DataSyncJobResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "CardDataSyncRecordResponse": {"allOf": [{"$ref": "#/components/schemas/DataSyncRecordDataResponse"}, {"type": "object", "properties": {"customerNo": {"type": "string"}, "email": {"type": "string"}, "cardId": {"type": "string"}}}]}, "CustomerDataSyncRecordResponse": {"allOf": [{"$ref": "#/components/schemas/DataSyncRecordDataResponse"}, {"type": "object", "properties": {"customerNo": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "groupName": {"type": "string"}, "allergens": {"type": "string"}, "action": {"type": "string"}, "avatarImagePath": {"type": "string"}, "customerSponsorEmail": {"type": "string"}}}]}, "DataSyncRecordDataResponse": {"type": "object", "discriminator": {"propertyName": "recordType"}, "properties": {"recordType": {"type": "string", "enum": ["GROUP", "CUSTOMER", "CARD"]}}}, "DataSyncRecordResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "jobId": {"type": "string"}, "recordIndex": {"type": "integer", "format": "int64"}, "recordType": {"type": "string", "enum": ["GROUP", "CUSTOMER", "CARD"]}, "recordStatus": {"type": "string", "enum": ["VALIDATED", "IN_PROGRESS", "ERROR", "COMPLETED"]}, "errorMessage": {"type": "string"}, "stacktrace": {"type": "string"}, "data": {"oneOf": [{"$ref": "#/components/schemas/CardDataSyncRecordResponse"}, {"$ref": "#/components/schemas/CustomerDataSyncRecordResponse"}, {"$ref": "#/components/schemas/GroupDataSyncRecordResponse"}]}, "rawData": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "updatedAt": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}}}, "GroupDataSyncRecordResponse": {"allOf": [{"$ref": "#/components/schemas/DataSyncRecordDataResponse"}, {"type": "object", "properties": {"groupPath": {"type": "string"}, "groupDescription": {"type": "string"}}}]}, "PagingDataSyncRecordResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/DataSyncRecordResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "DashboardTokenResponse": {"type": "object", "properties": {"token": {"type": "string"}}}, "ConfigurationResponse": {"type": "object", "properties": {"baseDomain": {"type": "string"}}}, "ContactResponse": {"type": "object", "properties": {"email": {"type": "string"}, "phoneNumber": {"type": "string"}}}}}}