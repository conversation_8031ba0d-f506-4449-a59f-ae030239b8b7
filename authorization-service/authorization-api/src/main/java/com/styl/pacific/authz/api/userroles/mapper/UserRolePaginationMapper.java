/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.api.userroles.mapper;

import com.styl.pacific.authz.service.core.features.userroles.entities.UserRole;
import com.styl.pacific.authz.service.core.features.userroles.request.UserRolePaginationQuery;
import com.styl.pacific.authz.shared.http.requests.QueryUserRolePaginationRequest;
import com.styl.pacific.authz.shared.http.responses.UserRoleResponse;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, UserRoleResponseMapper.class })
public interface UserRolePaginationMapper {
	UserRolePaginationMapper INSTANCE = Mappers.getMapper(UserRolePaginationMapper.class);

	UserRolePaginationQuery toPagingQuery(QueryUserRolePaginationRequest source);

	Paging<UserRoleResponse> toPagingResponse(Paging<UserRole> source);

}
