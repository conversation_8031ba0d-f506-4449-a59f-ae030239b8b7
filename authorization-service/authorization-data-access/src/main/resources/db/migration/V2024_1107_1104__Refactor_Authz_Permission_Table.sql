ALTER TABLE
    IF EXISTS feature_permissions DROP
        COLUMN feature_node_id;

ALTER TABLE
    IF EXISTS feature_permissions DROP
        COLUMN allowed_access_path;

DROP
    TABLE
        IF EXISTS features_tree CASCADE;

DROP
    TABLE
        IF EXISTS content_ingestion_tracking CASCADE;

DROP
    TABLE
        IF EXISTS role_templates CASCADE;

ALTER TABLE
    IF EXISTS feature_permissions ADD COLUMN IF NOT EXISTS tenant_id BIGINT;

ALTER TABLE
    IF EXISTS feature_permissions ADD COLUMN IF NOT EXISTS feature_node_id BIGINT;