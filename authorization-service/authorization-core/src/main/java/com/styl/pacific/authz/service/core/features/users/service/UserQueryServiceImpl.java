/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.users.service;

import com.styl.pacific.authz.service.core.features.userroles.request.CountUserQuery;
import com.styl.pacific.authz.service.core.features.users.UserQueryService;
import com.styl.pacific.authz.service.core.features.users.UserRepository;
import com.styl.pacific.authz.service.core.features.users.entities.CountUserResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserQueryServiceImpl implements UserQueryService {
	private final UserRepository userRepository;

	@Override
	public CountUserResult countUserByRoles(CountUserQuery query) {
		return userRepository.countUserRoles(query.byUserRoleIds());
	}
}
