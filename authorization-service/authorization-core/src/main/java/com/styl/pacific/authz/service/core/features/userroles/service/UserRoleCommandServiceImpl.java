/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.authz.service.core.features.userroles.service;

import com.styl.pacific.authz.service.core.features.featurenode.FeatureNodeQueryService;
import com.styl.pacific.authz.service.core.features.featurenode.entities.FeatureNode;
import com.styl.pacific.authz.service.core.features.userroles.UserRoleCommandService;
import com.styl.pacific.authz.service.core.features.userroles.UserRoleRepository;
import com.styl.pacific.authz.service.core.features.userroles.entities.UserRole;
import com.styl.pacific.authz.service.core.features.userroles.mapper.UserRoleCommandMapper;
import com.styl.pacific.authz.service.core.features.userroles.request.CountUserQuery;
import com.styl.pacific.authz.service.core.features.userroles.request.DeleteSingleUserRoleCommand;
import com.styl.pacific.authz.service.core.features.userroles.request.UpsertUserRoleCommand;
import com.styl.pacific.authz.service.core.features.users.UserQueryService;
import com.styl.pacific.authz.shared.exceptions.FeatureNodeNotFoundException;
import com.styl.pacific.authz.shared.exceptions.UserRoleDeletionFailureException;
import com.styl.pacific.authz.shared.exceptions.UserRoleDeletionNotAllowedException;
import com.styl.pacific.authz.shared.exceptions.UserRoleNotFoundException;
import com.styl.pacific.domain.constants.CommonDomainConstants;
import com.styl.pacific.domain.enums.DefaultSystemRole;
import com.styl.pacific.domain.permissions.FeatureNodeType;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserRoleCommandServiceImpl implements UserRoleCommandService {
	private final UserRoleRepository userRoleRepository;
	private final FeatureNodeQueryService featureNodeQueryService;
	private final UserQueryService userQueryService;

	@Transactional
	@Override
	public List<UserRole> upsertUserRoles(@NotNull TenantId tenantId, List<UpsertUserRoleCommand> commands) {
		return saveUserRoles(tenantId, commands);
	}

	private List<UserRole> saveUserRoles(TenantId tenantId, List<UpsertUserRoleCommand> commands) {
		final var featureNodeMap = featureNodeQueryService.getFeatureNodesByPaths(commands.stream()
				.map(UpsertUserRoleCommand::getAllowedPermissionPaths)
				.flatMap(Set::stream)
				.collect(Collectors.toSet()))
				.stream()
				.collect(Collectors.toMap(FeatureNode::getNodePath, Function.identity()));

		final var userRoles = commands.stream()
				.map(cmd -> {
					final var featureNodes = cmd.getAllowedPermissionPaths()
							.stream()
							.map(path -> Optional.ofNullable(featureNodeMap.get(path))
									.orElseThrow(() -> new FeatureNodeNotFoundException(String.format(
											"%s feature node was not found", path))))
							.toList();
					final var leafNodes = Stream.concat(featureNodeQueryService.getChildrenByParentPaths(featureNodes
							.stream()
							.filter(it -> FeatureNodeType.MODULE.equals(it.getNodeType()))
							.map(FeatureNode::getNodePath)
							.collect(Collectors.toSet()))
							.stream(), featureNodes.stream()
									.filter(it -> FeatureNodeType.API.equals(it.getNodeType())))
							.collect(Collectors.toSet())
							.stream()
							.toList();

					return UserRoleCommandMapper.INSTANCE.toEntity(tenantId, cmd, leafNodes);
				})
				.toList();
		return userRoleRepository.saveAll(tenantId, userRoles);
	}

	@Override
	public void deleteUserRole(DeleteSingleUserRoleCommand command) {
		final var role = userRoleRepository.findUserRoleById(command.getUserRoleId())
				.orElseThrow(() -> new UserRoleNotFoundException("Not found user role"));

		if (CommonDomainConstants.SYSTEM_TENANT_ID.equals(role.getTenantId()) && StringUtils.isNotBlank(role
				.getExternalId())) {
			throw new UserRoleDeletionNotAllowedException("Cannot delete system user role");
		}

		final var countAvailableUserRoles = userQueryService.countUserByRoles(CountUserQuery.builder()
				.byUserRoleIds(Set.of(command.getUserRoleId()))
				.build());

		if (!countAvailableUserRoles.getCountUsersMap()
				.isEmpty() && countAvailableUserRoles.getCountUsersMap()
						.get(command.getUserRoleId()) > 0) {
			throw new UserRoleDeletionFailureException(
					"Cannot delete user role because the role is linking with the available users");
		}

		userRoleRepository.deleteUserRole(command);
	}

	@Override
	@Transactional
	public void initializeTenantUserRoles(TenantId tenantId) {
		final var updateRoleCommands = Arrays.stream(DefaultSystemRole.values())
				.filter(role -> !role.isDefaultSystemTenant() && role.getFixedRoleId() == null)
				.map(template -> {
					final var allowedPermissionPaths = new HashSet<String>();
					allowedPermissionPaths.addAll(featureNodeQueryService.getChildrenByParentPaths(template
							.getAllowedPermissions()
							.stream()
							.filter(it -> FeatureNodeType.MODULE.equals(it.getNodeType()))
							.map(PacificApiPermissionKey::getNodePath)
							.collect(Collectors.toSet()))
							.stream()
							.map(FeatureNode::getNodePath)
							.collect(Collectors.toSet()));
					allowedPermissionPaths.addAll(template.getAllowedPermissions()
							.stream()
							.filter(it -> FeatureNodeType.API.equals(it.getNodeType()))
							.map(PacificApiPermissionKey::getNodePath)
							.collect(Collectors.toSet()));
					return UpsertUserRoleCommand.builder()
							.externalId(template.getExternalId())
							.roleTitle(template.getRoleTitle())
							.allowedPermissionPaths(allowedPermissionPaths)
							.build();
				})
				.toList();

		saveUserRoles(tenantId, updateRoleCommands);

	}
}
