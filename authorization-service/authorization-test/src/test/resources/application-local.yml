---
server:
  port: 9209
logging:
  level:
    com.styl.pacific: TRACE
spring:
  application:
    name: authz-service
  jpa:
    open-in-view: false
    show-sql: false
    database-platform: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
    properties:
      hibernate:
        dialect: com.styl.pacific.data.access.jpa.dialect.ExtendedPostgresDialect
        jdbc:
          time_zone: UTC
          batch_size: 50
        format_sql: false
        generate_statistics: false
        order_inserts: true
        order_updates: true
        query:
          in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  datasource:
    url: ********************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 5000
            loggerLevel: FULL
            errorDecoder: com.styl.pacific.common.feign.exception.PacificFeignErrorDecoder
            requestInterceptors:
              - com.styl.pacific.common.feign.interceptor.PlatformFeignHeaderForwarderInterceptor
  flyway:
    enabled: true
    out-of-order: true
  sql:
    init:
      platform: postgres
pacific:
  clients:
    user-service:
      url: http://localhost:9202
  aws:
    s3:
      endpoint: http://localhost:9000
      region: ap-southeast-1
      accessKey: test
      secretKey: test
  kafka:
    authz-service:
      tenant-created-event:
        group-id: authz-service-tenant-created-event
        topic-name: tenant-service-tenant-created-event
        concurrency: 1
kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: ttp://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  payment-consumer-group-id: payment-topic-consumer
  restaurant-approval-consumer-group-id: restaurant-approval-topic-consumer
  customer-group-id: customer-topic-consumer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: true
  auto-startup: true
  concurrency-level: 3
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150
