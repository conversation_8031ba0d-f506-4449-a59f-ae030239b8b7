@import './typography.scss';

.mat-mdc-tab-group {
  .mdc-tab {
    @apply h-10 px-4 #{!important};

    &__text-label {
      @extend .web__body2;
      @apply text-grey-600;
    }
  }

  .mdc-tab--active {
    .mdc-tab {
      &__text-label {
        @apply font-bold text-[#212B36] #{!important};
      }
    }

    .mdc-tab-indicator {
      &__content {
        &--underline {
          @apply border-primary-900;
        }
      }
    }

    &:focus,
    &:hover {
      .mdc-tab-indicator {
        &__content {
          &--underline {
            @apply border-primary-900 #{!important};
          }
        }
      }
    }
  }

  .mat-mdc-tab-label-container {
    @apply m-0 #{!important};
  }

  .mat-mdc-tab-body-wrapper {
    @apply h-full;
  }

  .mat-mdc-tab-body-content {
    @apply p-0 #{!important};
  }
}
