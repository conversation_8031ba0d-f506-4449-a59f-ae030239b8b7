import { Injectable } from '@angular/core';
import {
  FuseMockApiService,
  FuseMockApiUtils,
} from 'libs/fuse/src/lib/mock-api';
import { notifications as notificationsData } from '../notifications/data';
import { assign, cloneDeep } from 'lodash-es';

@Injectable({ providedIn: 'root' })
export class NotificationsMockApi {
  private _notifications: any = notificationsData.content;
  private _notificationsResponse: any = notificationsData;

  /**
   * Constructor
   */
  constructor(private _fuseMockApiService: FuseMockApiService) {
    // Register Mock API handlers
    this.registerHandlers();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Register Mock API handlers
   */
  registerHandlers(): void {
    // -----------------------------------------------------------------------------------------------------
    // @ Notifications - GET
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onGet('api/common/notifications')
      .reply(() => [200, cloneDeep(this._notificationsResponse)]);

    // -----------------------------------------------------------------------------------------------------
    // @ Notifications - POST
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onPost('api/common/notifications')
      .reply(({ request }) => {
        // Get the notification
        const newNotification = cloneDeep(request.body.notification);

        // Generate a new GUID
        newNotification.id = FuseMockApiUtils.guid();

        // Unshift the new notification
        this._notifications.unshift(newNotification);

        // Return the response
        return [200, newNotification];
      });

    // -----------------------------------------------------------------------------------------------------
    // @ Notifications - PATCH
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onPatch('api/common/notifications')
      .reply(({ request }) => {
        // Get the id and notification
        // const id = request.body.userId;
        const ids = request.body.ids;

        // Prepare the updated notification
        let updatedNotification: any = [];

        // Find the notification and update it
        this._notifications.forEach(
          (item: any, index: number, notifications: any[]) => {
            if (ids.includes(item.id)) {
              // Update the notification
              notifications[index] = assign({}, item, {
                ...item,
                read: true,
              });

              // Store the updated notification
              updatedNotification.push(notifications[index]);
            } else {
              // Store the updated notification
              updatedNotification.push(notifications[index]);
            }
          },
        );

        // Return the response
        return [200, updatedNotification];
      });

    // -----------------------------------------------------------------------------------------------------
    // @ Notifications - DELETE
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onDelete('api/common/notifications')
      .reply(({ request }) => {
        // Get the id
        const ids = request.params.get('ids');

        // Prepare the deleted notification
        let deletedNotification = null;

        // Filter the notification
        const filteredNotification = this._notifications.filter((item: any) =>
          ids?.includes(item.id),
        );

        // Store the deleted notification
        deletedNotification = cloneDeep(filteredNotification);

        // Delete the notification
        this._notifications = this._notifications.filter(
          (item: any) => !ids?.includes(item.id),
        );

        // Return the response
        return [200, deletedNotification];
      });

    // -----------------------------------------------------------------------------------------------------
    // @ Mark all as read - GET
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onGet('api/common/notifications/mark-all-as-read')
      .reply(() => {
        // Go through all notifications
        this._notifications.forEach(
          (item: any, index: number, notifications: any[]) => {
            // Mark it as read
            notifications[index].read = true;
            notifications[index].seen = true;
          },
        );

        // Return the response
        return [200, true];
      });

    // -----------------------------------------------------------------------------------------------------
    // @ Toggle read status - POST
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onPost('api/common/notifications/toggle-read-status')
      .reply(({ request }) => {
        // Get the notification
        const notification = cloneDeep(request.body.notification);

        // Prepare the updated notification
        let updatedNotification = null;

        // Find the notification and update it
        this._notifications.forEach(
          (item: any, index: number, notifications: any[]) => {
            if (item.id === notification.id) {
              // Update the notification
              notifications[index].read = notification.read;

              // Store the updated notification
              updatedNotification = notifications[index];
            }
          },
        );

        // Return the response
        return [200, updatedNotification];
      });
  }
}
