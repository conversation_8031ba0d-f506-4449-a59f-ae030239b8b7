/* eslint-disable */
import { DateTime } from 'luxon';

/* Get the current instant */
const now = DateTime.now();

export const notifications = {
  content: [
    {
      id: '493190c9-5b61-4912-afe5-78c21f1044d7',
      icon: 'heroicons_mini:star',
      title: 'Daily challenges',
      content: 'Your submission has been accepted',
      createdAt: now.minus({ minute: 25 }).valueOf(), // 25 minutes ago
      read: false,
    },
    {
      id: '6e3e97e5-effc-4fb7-b730-52a151f0b641',
      content:
        '<strong><PERSON></strong> added you to <em>Top Secret Project</em> group and assigned you as a <em>Project Manager</em>',
      createdAt: now.minus({ minute: 50 }).valueOf(), // 50 minutes ago
      read: false,
    },
    {
      id: 'b91ccb58-b06c-413b-b389-87010e03a120',
      icon: 'heroicons_mini:envelope',
      title: 'Mailbox',
      content: 'You have 15 unread mails across 3 mailboxes',
      createdAt: now.minus({ hour: 3 }).valueOf(), // 3 hours ago
      read: false,
    },
    {
      id: '541416c9-84a7-408a-8d74-27a43c38d797',
      icon: 'heroicons_mini:arrow-path',
      title: 'Cron jobs',
      content: 'Your <em>Docker container</em> is ready to publish',
      createdAt: now.minus({ hour: 5 }).valueOf(), // 5 hours ago
      read: false,
    },
    {
      id: 'ef7b95a7-8e8b-4616-9619-130d9533add9',
      content: '<strong>Roger Murray</strong> accepted your friend request',
      createdAt: now.minus({ hour: 7 }).valueOf(), // 7 hours ago
      read: false,
    },
    {
      id: 'eb8aa470-635e-461d-88e1-23d9ea2a5665',
      content: '<strong>Sophie Stone</strong> sent you a direct message',
      createdAt: now.minus({ hour: 9 }).valueOf(), // 9 hours ago
      read: false,
    },
    {
      id: 'b85c2338-cc98-4140-bbf8-c226ce4e395e',
      icon: 'heroicons_mini:envelope',
      title: 'Mailbox',
      content: 'You have 3 new mails',
      createdAt: now.minus({ day: 1 }).valueOf(), // 1 day ago
      read: false,
    },
    {
      id: '8f8e1bf9-4661-4939-9e43-390957b60f42',
      icon: 'heroicons_mini:star',
      title: 'Daily challenges',
      content:
        'Your submission has been accepted and you are ready to sign-up for the final assigment which will be ready in 2 days',
      createdAt: now.minus({ day: 3 }).valueOf(), // 3 days ago
      read: false,
    },
  ],
};
