import { Injectable } from '@angular/core';
import { FuseMockApiService } from 'libs/fuse/src/lib/mock-api';
import { assign, cloneDeep } from 'lodash-es';
import { tenantManagement } from './data';

@Injectable({ providedIn: 'root' })
export class TenantManagementMockApi {
  private _tenant: any = tenantManagement;

  /**
   * Constructor
   */
  constructor(private _fuseMockApiService: FuseMockApiService) {
    // Register Mock API handlers
    this.registerHandlers();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Register Mock API handlers
   */
  registerHandlers(): void {
    // -----------------------------------------------------------------------------------------------------
    // @ Tenant - GET
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService.onGet('/api/tenant/tenants').reply(() => {
      return [200, cloneDeep(this._tenant)];
    });

    // -----------------------------------------------------------------------------------------------------
    // @ Tenant - PATCH
    // -----------------------------------------------------------------------------------------------------
    this._fuseMockApiService
      .onPatch('api/common/tenant-management')
      .reply(({ request }) => {
        // Get the tenant mock-api
        const tenant = cloneDeep(request.body.tenant);

        // Update the tenant mock-api
        this._tenant = assign({}, this._tenant, tenant);

        // Return the response
        return [200, cloneDeep(this._tenant)];
      });
  }
}
