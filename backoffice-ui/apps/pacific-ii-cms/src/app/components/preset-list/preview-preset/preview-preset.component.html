<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">
          {{ breadcrumbs[0] }}
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__parent">
          {{ breadcrumbs[1] }}
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Preset</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="gotoPresetList()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>
          {{ presetDetail && presetDetail.name ? presetDetail.name : '-' }}
          @if (
            presetDetail &&
            presetDetail.status !== PresetStatus.COMPLETED &&
            presetDetail.status !== PresetStatus.ERROR
          ) {
            <span>
              (Preview)
              <mat-icon
                [svgIcon]="'heroicons_outline:information-circle'"
                class="w-3.5 h-3.5 cursor-pointer -translate-y-1.5"
                #tooltip="matTooltip"
                [matTooltip]="
                  'The preset has not been imported into the database.'
                "
                [matTooltipPosition]="'above'"
              ></mat-icon>
            </span>
          }
        </div>
        @if (
          presetDetail &&
          presetDetail.presetType &&
          manualLink[presetDetail.presetType]
        ) {
          <fuse-help-link
            [url]="manualLink[presetDetail.presetType]"
          ></fuse-help-link>
        }
      </div>
    </div>

    <div>
      @if (presetDetail && presetDetail.status === PresetStatus.UPLOADED) {
        <button
          class="btn-contained__primary__medium"
          (click)="handleImportPreset()"
          [disabled]="loading"
        >
          <span>Import</span>
        </button>
      }

      @if (
        presetDetail &&
        (presetDetail.status === PresetStatus.UPLOADING ||
          presetDetail.status === PresetStatus.IMPORTING)
      ) {
        <button class="btn-contained__primary__medium" [disabled]="true">
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
          <span>{{
            presetDetail.status === PresetStatus.UPLOADING
              ? 'Uploading'
              : 'Importing'
          }}</span>
        </button>
      }
    </div>
  </div>

  <div class="flex justify-end gap-8 p-4">
    <div class="flex gap-2.5">
      <span class="web__subtitle1 text-[#212B36]">Total:</span>
      <span class="web__body1 text-[#212B36]">
        {{
          presetDetail && !FuseUltils.isNullOrEmpty(presetDetail.totalRecords)
            ? presetDetail.totalRecords
            : '-'
        }}
      </span>
    </div>

    <div class="flex gap-2.5">
      <span class="web__subtitle1 text-success-main">Success:</span>
      <span class="web__body1 text-success-main">
        {{
          presetDetail && !FuseUltils.isNullOrEmpty(presetDetail.successRecords)
            ? presetDetail.successRecords
            : '-'
        }}
      </span>
    </div>

    <div class="flex gap-2.5">
      <span class="web__subtitle1 text-error-main">Error:</span>
      <span class="web__body1 text-error-main">
        {{
          presetDetail && !FuseUltils.isNullOrEmpty(presetDetail.failedRecords)
            ? presetDetail.failedRecords
            : '-'
        }}
      </span>
    </div>
  </div>

  <div
    class="box !p-4 mb-4 mx-4 flex-auto overflow-auto w-auto h-[calc(100vh_-_290px)]"
  >
    <fuse-table-component
      [dataSource]="dataSource"
      [displayedColumns]="displayedColumns"
      [total]="total"
      [hideColBtn]="true"
    ></fuse-table-component>
  </div>
</div>
