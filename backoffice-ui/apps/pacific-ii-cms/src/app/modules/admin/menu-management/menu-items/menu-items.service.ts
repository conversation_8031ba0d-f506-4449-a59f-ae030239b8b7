import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../../core/const';
import { IMenuItemResponse } from './menu-items.types';

@Injectable({ providedIn: 'root' })
export class MenuItemsService {
  private _httpClient = inject(HttpClient);
  private _items: ReplaySubject<any> = new ReplaySubject<any>(1);

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for menu items
   */
  get items$(): Observable<any> {
    return this._items.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all menu item data
   */
  getMenuItems(menuId: string, queryParams: any = {}): Observable<any> {
    return this._httpClient
      .get<any>(
        `${API.MENU.ITEMS.LIST.replace('{menuId}', menuId)}?${queryParams}`,
      )
      .pipe(
        tap((items: IMenuItemResponse) => {
          this._items.next(items);
        }),
      );
  }

  /**
   * Get menu item detail
   */
  getMenuItemDetail(menuId: string, id: string): Observable<any> {
    return this._httpClient.get<any>(
      API.MENU.ITEMS.DETAIL.replace('{menuId}', menuId).replace('{itemId}', id),
    );
  }

  /**
   * Add new menu item
   */
  addMenuItem(menuId: string, data: any): Observable<any> {
    return this._httpClient.post<any>(
      API.MENU.ITEMS.ADD.replace('{menuId}', menuId),
      data,
    );
  }

  /**
   * Update menu item
   */
  updateMenuItem(menuId: string, id: string, data: any): Observable<any> {
    return this._httpClient.put<any>(
      API.MENU.ITEMS.UPDATE.replace('{menuId}', menuId).replace('{itemId}', id),
      data,
    );
  }

  /**
   * Delete menu item
   */
  deleteMenuItem(menuId: string, id: string): Observable<any> {
    return this._httpClient.delete<any>(
      API.MENU.ITEMS.DELETE.replace('{menuId}', menuId).replace('{itemId}', id),
    );
  }
}
