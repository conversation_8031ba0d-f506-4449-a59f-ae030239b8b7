import {
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { forkJoin, Subject, takeUntil, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MenuItemsService } from './menu-items.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseUltils } from '@fuse/ultils';
import { IMenuItem, IMenuItemResponse } from './menu-items.types';
import { ProductCategoryManagementService } from '../../product-category-management/product-category-management.service';
import { AdvancedSearchComponent } from '@fuse/components/advanced-search';
import { CommonModule } from '@angular/common';
import { AddMenuItemComponent } from './add-menu-tem/add-menu-item.component';
import {
  APP_TOOLTIP,
  DATE_TIME_FORMAT,
  DateOfWeekList,
  PRODUCT_STATUS,
  ROUTE,
} from '../../../../core/const';
import { MenuItemCardListComponent } from './menu-tem-card-list/menu-item-card-list.component';
import { FusePaginatorComponent } from '@fuse/components/paginator';
import { Utils } from '../../../../core/utils/utils';
import { Currency } from '../../tenant-management/tenant.types';
import { FuseErrorNoDataComponent } from '@fuse/components/error-no-data/error-no-data.component';
import { MatTooltip } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'app-menu-items',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTooltip,
    MatIconModule,
    MatMenuModule,
    FuseTableComponent,
    AdvancedSearchComponent,
    MenuItemCardListComponent,
    FusePaginatorComponent,
    FuseErrorNoDataComponent,
  ],
  templateUrl: './menu-items.component.html',
})
export class MenuItemsComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() id!: string;
  @Input() storeId!: string;
  @Input() mode = 'view';
  @Input() updatePermission = { update: false, productView: false };

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;

  actions: Array<IAction> = [];
  bulkActions: Array<IAction> = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };

  searchForm: any;
  queryParams!: any;

  optionListCategory: Array<any> = [];

  viewMode = 'grid';
  selectedMenuItems: Array<string> = [];

  productStatus = PRODUCT_STATUS;
  productWarning = APP_TOOLTIP.PRODUCT_ARCHIVE;

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _menuItemsService: MenuItemsService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _confirmationService: FuseConfirmationService,
    private _categoriesService: ProductCategoryManagementService,
  ) {
    this.initMenuItemsSubscription();
    this.searchForm = this.initSearchForm();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetMenuItems();
      });

    this.handleGetCategory();
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetCategory(): void {
    this._categoriesService.getProductCategories().subscribe((res: any) => {
      this.optionListCategory = res.content.map((item: any) => {
        return {
          ...item,
          label: item.name,
          value: item.id,
        };
      });

      this.searchForm.basic[2].options = [
        { label: 'All', value: '' },
        ...this.optionListCategory,
      ];
    });
  }

  handleGetMenuItems(): void {
    this.selectedMenuItems = [];
    const mappedData = this.getMenuItemParams(this.queryParams);
    this._menuItemsService
      .getMenuItems(this.id, FuseUltils.objectToQueryString(mappedData))
      .subscribe();
  }

  getMenuItemParams(queryParams: any): any {
    const basicSearch = this.searchForm.basic;

    const filter = {
      name: queryParams['name'] ?? basicSearch[0].defaultValue,
      sku: queryParams['sku'] ?? basicSearch[1].defaultValue,
      categoryId: queryParams['categoryId'] ?? basicSearch[2].defaultValue,
      // fromTime: queryParams['fromTime'] || '',
      // toTime: queryParams['toTime'] || '',
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  onChangeViewMode(viewMode: string): void {
    this.viewMode = viewMode;
    this.selectedMenuItems = [];
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedMenuItems = event.map((item) => item.id);
  }

  onUpdateSelectedMenuItems(event: string): void {
    const index = this.selectedMenuItems.findIndex(
      (item: string) => item == event,
    );
    if (index != -1) {
      this.selectedMenuItems.splice(index, 1);
    } else {
      this.selectedMenuItems.push(event);
    }
  }

  onOpenAddMenuItemDialog() {
    const dialogRef = this._dialog.open(AddMenuItemComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.storeId,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetMenuItems();
      }
    });
  }

  onOpenEditMenuItemDialog(item: any): void {
    const dialogRef = this._dialog.open(AddMenuItemComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.storeId,
        detail: item,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetMenuItems();
      }
    });
  }

  onOpenDeleteMenuItemDialog(item: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menu Item',
      message: 'Please confirm to delete this menu item.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteMenuItem(item);
      }
    });
  }

  handleDeleteMenuItem(item: any): void {
    this._menuItemsService.deleteMenuItem(this.id, item.id).subscribe(() => {
      this._toast.success('Delete menu item success!');
      this.handleGetMenuItems();
    });
  }

  onOpenDeleteSelectedMenuItemsDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menu Items',
      message: 'Please confirm to delete this selected menu item(s).',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedMenuItems();
      }
    });
  }

  handleDeleteSelectedMenuItems(): void {
    forkJoin(
      this.selectedMenuItems.map((itemId: any) =>
        this._menuItemsService.deleteMenuItem(this.id, itemId),
      ),
    ).subscribe({
      next: () => {
        this._toast.success(
          'Selected menu item(s) have been deleted successfully!',
        );
        this.handleGetMenuItems();
      },
      error: () => {
        this.handleGetMenuItems();
      },
    });
  }

  handleViewProductDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.DETAIL}/${element.product.id}`,
    ]);
  }

  getAvailableDate(arrangement: any): string {
    if (
      !FuseUltils.isNullOrEmpty(arrangement.startDate) &&
      !FuseUltils.isNullOrEmpty(arrangement.endDate)
    ) {
      return `${FuseUltils.tsToLocalTime(arrangement.startDate, DATE_TIME_FORMAT.DATE_DEFAULT)} - ${FuseUltils.tsToLocalTime(arrangement.endDate, DATE_TIME_FORMAT.DATE_DEFAULT)}`;
    } else if (
      !FuseUltils.isNullOrEmpty(arrangement.startDate) &&
      FuseUltils.isNullOrEmpty(arrangement.endDate)
    ) {
      return `${FuseUltils.tsToLocalTime(arrangement.startDate, DATE_TIME_FORMAT.DATE_DEFAULT)}`;
    } else if (
      FuseUltils.isNullOrEmpty(arrangement.startDate) &&
      !FuseUltils.isNullOrEmpty(arrangement.endDate)
    ) {
      return `${FuseUltils.tsToLocalTime(arrangement.endDate, DATE_TIME_FORMAT.DATE_DEFAULT)}`;
    } else {
      return '-';
    }
  }

  getAvailableTime(arrangement: any): string {
    if (arrangement.startTime && arrangement.endTime) {
      return `${Utils.updateTimeFormat(arrangement.startTime)} - ${Utils.updateTimeFormat(arrangement.endTime)}`;
    } else if (arrangement.startTime && !arrangement.endTime) {
      return `${Utils.updateTimeFormat(arrangement.startTime)}`;
    } else if (!arrangement.startTime && arrangement.endTime) {
      return `${Utils.updateTimeFormat(arrangement.endTime)}`;
    } else {
      return '-';
    }
  }

  getDateOfWeekString(dateOfWeek: Array<string>): string {
    if (dateOfWeek.length == 7) {
      return 'Every day';
    }

    const selectedDate = DateOfWeekList.filter((item: any) =>
      dateOfWeek.find((date: string) => date == item.value),
    ).map((item: any) => item.name);
    const str = 'Every ' + selectedDate.join(', ');

    return str;
  }

  formatPriceValue(value: any, currency: Currency): string {
    return FuseUltils.formatPrice(value, currency) ?? '';
  }

  onRowClick = (row: IMenuItem) => {
    if (this.updatePermission.update) {
      this.onOpenEditMenuItemDialog(row);
    }
  };

  private initMenuItemsSubscription(): void {
    this._menuItemsService.items$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((res: IMenuItemResponse) => {
        this.dataSource = res.content.map((item: IMenuItem) => {
          return {
            ...item,
            product: {
              ...item.product,
              unitPriceStr: this.formatPriceValue(
                item.product.unitPrice,
                item.product?.currency,
              ),
              listingPriceStr: this.formatPriceValue(
                item.product.listingPrice,
                item.product?.currency,
              ),
            },
            name: item.product.name,
            sku: item.product.sku,
            description: item.product.description,
            category: item.product.category,
            availableDate: this.getAvailableDate(item.arrangement),
            availableTime: this.getAvailableTime(item.arrangement),
            availableOnString: this.getDateOfWeekString(
              item.arrangement.availableOn,
            ),
          };
        });

        this.total = res.totalElements;
      });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'name',
        name: 'Product Name',
        sort: true,
        selected: true,
        custom: true,
        // renderHtml: (value: string) => {
        //   return `<p class="font-semibold">${value}</p>`;
        // },
      },
      {
        key: 'sku',
        name: 'SKU',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'category',
        name: 'Category',
        sort: true,
        selected: true,
        render: (value: any) => {
          return value?.name ?? '';
        },
      },
      {
        key: 'description',
        name: 'Description',
        selected: false,
      },
      {
        key: 'availableDate',
        name: 'Available Date',
        selected: true,
      },
      {
        key: 'availableTime',
        name: 'Available Time',
        selected: true,
      },
      {
        key: 'availableOnString',
        name: 'Available on',
        selected: true,
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View Product Detail',
        type: 'event',
        hidden: () => !this.updatePermission.productView,
        callback: (element) => this.handleViewProductDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        callback: (element) => this.onOpenEditMenuItemDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        callback: (element) => this.onOpenDeleteMenuItemDialog(element),
      },
    ];

    this.bulkActions = [
      {
        name: 'Delete',
        type: 'event',
        callback: () => this.onOpenDeleteSelectedMenuItemsDialog(),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Product Name',
          name: 'name',
          placeholder: 'Enter name',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'SKU',
          name: 'sku',
          placeholder: 'Enter sku',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Category',
          name: 'categoryId',
          placeholder: 'Select category',
          type: 'select',
          defaultValue: '',
          options: [
            {
              label: 'All',
              value: '',
            },
          ],
        },
      ],
    };
  }
}
