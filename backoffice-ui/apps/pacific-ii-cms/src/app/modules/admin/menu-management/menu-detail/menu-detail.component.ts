import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { ROLE_MODULES, ROLE_SCOPES, ROUTE } from '../../../../core/const';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from '../../../../core/utils/utils';
import { MenuManagementService } from '../menu-management.service';
import { IMenuInfo } from '../menu.types';
import { MenuItemsComponent } from '../menu-items/menu-items.component';
import { StoreManagementService } from '../../store-management/store-management.service';
import { FuseUltils } from '@fuse/ultils';
import { UserPermissionService } from 'apps/pacific-ii-cms/src/app/core/user/user-permission.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-menu-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MenuItemsComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './menu-detail.component.html',
})
export class MenuDetailComponent implements OnInit, OnDestroy {
  id!: string;
  menuDetail!: IMenuInfo;

  optionListStore: Array<any> = [];

  permissionList = {
    update: false,
    productView: false,
  };

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  constructor(
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _menusService: MenuManagementService,
    private _storeService: StoreManagementService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.getMenuId();
    this.initMenuDetailSubscription();
  }

  ngOnInit(): void {
    this.handleGetMenuDetail();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetMenuDetail(): void {
    this._menusService.getMenuDetail(this.id).subscribe();
  }

  handleGetStoreDetail(storeId: string): void {
    const params = {
      'filter.storeIds': [storeId],
    };
    this._storeService
      .getStores(FuseUltils.objectToQueryString(params))
      .subscribe((stores: any) => {
        this.optionListStore = stores.content;
      });
  }

  gotoEditMenu(): void {
    this._router.navigate([`${ROUTE.MENU.MAIN}/${ROUTE.MENU.EDIT}/${this.id}`]);
  }

  gotoMenuManagement(): void {
    this._router.navigate([ROUTE.MENU.MAIN]);
  }

  getStatusColor(status: string): string {
    return Utils.getStatusColor(status);
  }

  private initMenuDetailSubscription(): void {
    this._menusService.menuDetail$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((detail: IMenuInfo) => {
        this.menuDetail = { ...detail };

        if (detail.storeId) {
          this.handleGetStoreDetail(detail.storeId);
        }
      });
  }

  private getMenuId(): void {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.MENU_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
          productView: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.PRODUCT_MGMT,
              scope: ROLE_SCOPES.VIEW,
            },
          ]),
        };
      });
  }
}
