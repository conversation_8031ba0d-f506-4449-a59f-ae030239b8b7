/* eslint-disable @typescript-eslint/no-inferrable-types */
import { Component, On<PERSON>estroy, OnInit } from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSpinner } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseDateTimePickerComponent } from '@fuse/components/date-time-picker';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseSelectComponent } from '@fuse/components/select';
import { FuseTextareaComponent } from '@fuse/components/textarea';
import { FuseUltils } from '@fuse/ultils';
import {
  FUND_SOURCE_EXPIRED_IN_OPTIONS,
  FUND_SOURCE_SCHEDULE_TYPE_OPTIONS,
  FUND_SOURCE_STATUS_OPTIONS,
  REGEX,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { DateTime } from 'luxon';
import { ToastrService } from 'ngx-toastr';
import { Subject, takeUntil } from 'rxjs';
import { Currency } from '../../tenant-management/tenant.types';
import { CustomerUsingWalletComponent } from '../customer-using-wallet/customer-using-wallet.component';
import { SourceOfFundService } from '../source-of-fund.service';
import { IScheduler, ISourceOfFund } from '../source-of-fund.types';
import { TopUpSchedulerComponent } from '../top-up-scheduler/top-up-scheduler.component';

@Component({
  selector: 'app-edit-source-of-fund',
  standalone: true,
  imports: [
    MatIconModule,
    FuseInputComponent,
    FuseSelectComponent,
    FuseTextareaComponent,
    FuseDateTimePickerComponent,
    ReactiveFormsModule,
    MatSpinner,
    CustomerUsingWalletComponent,
    MatLabel,
    MatRadioModule,
    TopUpSchedulerComponent,
  ],
  templateUrl: './edit-source-of-fund.component.html',
})
export class EditSourceOfFundComponent implements OnInit, OnDestroy {
  id!: string;
  sourceOfFundDetail!: ISourceOfFund;
  topUpSchedulers!: Array<IScheduler>;
  sourceForm!: UntypedFormGroup;
  expiredForm!: UntypedFormGroup;
  loading = false;
  currency!: Currency;
  minTimestampExpire = DateTime.now().startOf('day').toMillis();

  statusOptions = [...FUND_SOURCE_STATUS_OPTIONS];
  fundSourceScheduleOptions = [...FUND_SOURCE_SCHEDULE_TYPE_OPTIONS];
  expiredInOptions = [...FUND_SOURCE_EXPIRED_IN_OPTIONS];

  expiredMode = null;

  errorMessages = {
    name: {
      required: "Please enter fund's name!",
    },
    status: {
      required: "Please choose fund's status!",
    },
    description: {
      required: "Please enter fund's description!",
      maxlength: 'Description cannot exceed 255 characters!',
    },
    fundExpiresOn: {
      required: 'Please choose the expired time!',
    },
    fundExpiresIn: {
      required: 'Please enter the amount of time top-up would be expired!',
      pattern: 'The amount of time must be a non-negative integer!',
      min: 'The amount of time must be a non-negative integer!',
    },
  };

  private _unsubscribeAll: Subject<any> = new Subject();

  constructor(
    private _sourceOfFundService: SourceOfFundService,
    private _activatedRoute: ActivatedRoute,
    private _formBuilder: FormBuilder,
    private _router: Router,
    private _toast: ToastrService,
    private _storage: StorageService,
  ) {
    this.getSourceOfFundId();
    this.initSourceForm();
    this.currency = this._storage.getTenantCurrency();
  }

  ngOnInit(): void {
    this.getTopUpSchedulerList();
    this.handleGetSourceOfFundDetail();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleGetSourceOfFundDetail() {
    this._sourceOfFundService
      .getSourceOfFundDetail(this.id)
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((value) => {
        this.sourceOfFundDetail = value;
        this.sourceForm.patchValue({
          ...value,
          expiredMode: value.fundExpiresOn
            ? 'time'
            : value.fundExpiresInMs
              ? 'amount'
              : 'none',
          topupAmount: FuseUltils.formatCurrency(
            value.topupAmount,
            this.currency.fractionDigits,
          ),
        });

        if (value.fundExpiresOn) {
          this.expiredForm.patchValue({ fundExpiresOn: value.fundExpiresOn });
        }

        if (value.fundExpiresInMs) {
          const fundExpiresIn = Math.floor(
            value.fundExpiresInMs / (1000 * 3600 * 24),
          );

          this.expiredForm.patchValue({
            fundExpiresIn: fundExpiresIn,
          });
        }
      });
  }

  // Get the top-up scheduler list
  getTopUpSchedulerList() {
    this._sourceOfFundService
      .getTopUpSchedulerList(this.id)
      .subscribe((data: Array<IScheduler>) => {
        this.topUpSchedulers = data;

        const maxTopUpTime = Math.max(
          ...this.topUpSchedulers.map((value) => value.startTopupDate),
        );

        if (
          DateTime.fromMillis(maxTopUpTime).get('day') >
          DateTime.now().get('day')
        ) {
          this.minTimestampExpire = DateTime.fromMillis(maxTopUpTime)
            .startOf('day')
            .toMillis();
        }
      });
  }

  navigateToDetailPage() {
    this._router.navigate([
      `${ROUTE.SOURCE_OF_FUND.MAIN}/${ROUTE.SOURCE_OF_FUND.DETAIL}/${this.id}`,
    ]);
  }

  checkFormDirty() {
    if (this.sourceForm.dirty) {
      return true;
    }

    if (
      this.sourceForm.value['expiredMode'] === 'amount' &&
      this.expiredForm.controls['fundExpiresIn'].dirty
    ) {
      return true;
    }

    if (
      this.sourceForm.value['expiredMode'] === 'time' &&
      this.expiredForm.controls['fundExpiresOn'].dirty
    ) {
      return true;
    }

    return false;
  }

  // Refactor to use shared utility methods
  submitForm() {
    for (const key in this.sourceForm.controls) {
      this.sourceForm.controls[key].markAsDirty();
      this.sourceForm.controls[key].markAsTouched();
      this.sourceForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.expiredForm.controls) {
      this.expiredForm.controls[key].markAsTouched();
      this.expiredForm.controls[key].markAsDirty();
      this.expiredForm.controls[key].updateValueAndValidity();
    }

    if (this.sourceForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    if (
      this.sourceForm.value.expiredMode === 'amount' &&
      this.expiredForm.controls['fundExpiresIn'].invalid
    ) {
      Utils.scrollToInvalid();
      return;
    }

    if (
      this.sourceForm.value.expiredMode === 'time' &&
      this.expiredForm.controls['fundExpiresOn'].invalid
    ) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const data = this._sourceOfFundService.formatFundData(
      this.sourceForm.getRawValue(),
      this.expiredForm.getRawValue(),
      this.currency,
    );

    console.log('formatted data', data);

    this._sourceOfFundService.updateSourceOfFund(this.id, data).subscribe({
      next: () => {
        this._toast.success('You have updated source of fund successfully!');
        this.loading = false;
        this.navigateToDetailPage();
      },
      error: () => {
        this.loading = false;
        this.handleGetSourceOfFundDetail();
      },
    });
  }

  private initSourceForm() {
    this.sourceForm = this._formBuilder.group({
      fundSourceId: [{ value: null, disabled: true }, [Validators.required]],
      name: [null, [Validators.required]],
      status: [null, [Validators.required]],
      description: [null, [Validators.maxLength(255)]],
      expiredMode: ['none'],
    });

    this.expiredForm = this._formBuilder.group({
      fundExpiresOn: [null, [Validators.required]],
      fundExpiresIn: [
        null,
        [
          Validators.pattern(REGEX.NONNEGATIVE_INTEGER),
          Validators.required,
          Validators.min(0),
        ],
      ],
    });

    this.sourceForm.controls['expiredMode'].valueChanges.subscribe((value) => {
      this.expiredMode = value;
    });
  }

  private getSourceOfFundId() {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }
}
