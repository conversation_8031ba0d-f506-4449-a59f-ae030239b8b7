export interface IMenuBoard {
  id: string;
  tenantId: string;
  name: string;
  images: Array<IMenuBoardImageResponse>;
  status: string;
  displayMode: string;
  activatedAt: number;
  delayBetweenImages: number;
  createdAt: number;
  updatedAt: number;
  onboard?: string;
  sessionCount?: number;
}

export interface IMenuBoardImageResponse {
  id: string;
  menuBoardId: string;
  image: {
    path: string;
    url: string;
  };
  createdAt: number;
  updatedAt: number;
}

export interface IMenuBoardSessionDetail {
  id: string;
  tenantId: string;
  name: string;
  menuBoardId: string;
  createdAt: number;
  updatedAt: number;
}

export interface IMenuBoardAuthorization {
  menuBoardId: string;
  code: string;
}
