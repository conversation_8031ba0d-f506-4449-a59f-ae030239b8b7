<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-1">
    <div class="web__h6 text-grey-900 flex items-center gap-2">
      Menu Board Sessions
      <fuse-help-link
        [url]="'/menu-management/menu-board-management/#revoke-menu-board'"
      ></fuse-help-link>
    </div>
    <div class="text-xs font-bold leading-5 uppercase font-onest text-grey-600">
      {{ data.menuBoard.name }}
    </div>
  </div>

  <div class="flex p-2 border-[1px] flex-col overflow-auto max-h-128">
    @if (sessionList && sessionList.length > 1 && permissionList.revoke) {
      <mat-checkbox
        class="permission"
        color="primary"
        (change)="toggleAllSessions()"
        [checked]="isAllSessionsSelected()"
        [indeterminate]="
          selectedSessions.length > 0 && !isAllSessionsSelected()
        "
      >
        All
      </mat-checkbox>
    }

    @for (session of sessionList; track session.id || $index) {
      @if (permissionList.revoke) {
        <mat-checkbox
          class="permission"
          color="primary"
          [checked]="isSessionSelected(session)"
          (change)="onChange($event, session)"
        >
          {{ session.name }}
        </mat-checkbox>
      } @else {
        <div class="web__subtitle1 !leading-7 pl-2">
          {{ session.name }}
        </div>
      }
    }
  </div>

  <div class="flex items-start justify-end gap-2">
    @if (permissionList.revoke) {
      <button
        class="btn-outlined__error__medium"
        [disabled]="loading || !selectedSessions.length"
        (click)="openConfirmationDialog()"
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        }
        Revoke
      </button>
    }

    <button class="btn-contained__primary__medium" (click)="onClose()">
      Cancel
    </button>
  </div>
</div>
