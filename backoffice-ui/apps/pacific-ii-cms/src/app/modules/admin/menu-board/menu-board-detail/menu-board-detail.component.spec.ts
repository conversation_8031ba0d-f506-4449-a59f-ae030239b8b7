import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MenuBoardDetailComponent } from './menu-board-detail.component';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { IMenuBoard, IMenuBoardImageResponse } from '../menu-board.types';
import {
  API,
  MENU_BOARD_DISPLAY_MODE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('MenuBoardDetailComponent', () => {
  let component: MenuBoardDetailComponent;
  let fixture: ComponentFixture<MenuBoardDetailComponent>;
  let httpTestingController: HttpTestingController;

  const mockMenuBoardId = 'test1';

  const mockImage: IMenuBoardImageResponse = {
    id: 'test123',
    menuBoardId: mockMenuBoardId,
    image: {
      path: 'path.test.com',
      url: 'url.test.com',
    },
    createdAt: 0,
    updatedAt: 0,
  };

  const mockMenuBoard: IMenuBoard = {
    id: mockMenuBoardId,
    name: 'Menu Board 1',
    displayMode: MENU_BOARD_DISPLAY_MODE.SINGLE,
    status: 'CREATED',
    createdAt: 1729340726348,
    activatedAt: 1729340726348,
    updatedAt: 1729340726348,
    onboard: mockMenuBoardId,
    tenantId: '1234',
    images: [mockImage],
    delayBetweenImages: 60,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MenuBoardDetailComponent, MatDialogModule],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            id: mockMenuBoardId,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MenuBoardDetailComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);

    jest.spyOn(component['_dialog'], 'open');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should send request to server to get menu board detail when run ngOnInit', () => {
    component.ngOnInit();

    const req = httpTestingController.match(
      API.MENU_BOARD.DETAIL.replace('{id}', mockMenuBoardId),
    );
    req[1].flush(mockMenuBoard);

    expect(component.menuBoardDetail).toEqual(mockMenuBoard);
  });

  it('should close the dialog when run the onClose method', () => {
    component.onClose();

    expect(component['_dialogRef'].close).toHaveBeenCalled();
  });

  it('should open the dialog to view image when run openZoomImageDialog', () => {
    component.viewImage = null;

    component.onOpenZoomImageDialog(mockImage);

    expect(component.viewImage).toEqual(mockImage.image);
    expect(component['_dialog'].open).toHaveBeenCalledWith(
      component.zoomImageTemplate,
      {
        width: '472px',
      },
    );
  });

  it('should check if menu display mode is carousel when run checkDisplayModeCarousel', () => {
    const check = component.checkDisplayModeCarousel(mockMenuBoard.displayMode);
    expect(check).toBe(false);
  });

  it('should return the label of display mode when run formatDisplayMode', () => {
    const label = component.formatDisplayMode(mockMenuBoard.displayMode);
    expect(label).toBe('Single Image');
  });
});
