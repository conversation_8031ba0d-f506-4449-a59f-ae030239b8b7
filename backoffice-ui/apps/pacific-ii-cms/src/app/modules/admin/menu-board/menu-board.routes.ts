import { Routes } from '@angular/router';
import { MenuBoardComponent } from './menu-board.component';
import { ROUTE } from '../../../core/const';
import { AddMenuBoardComponent } from './add-menu-board/add-menu-board.component';

export default [
  {
    path: ROUTE.MENU_BOARD.LIST,
    component: MenuBoardComponent,
  },
  {
    path: ROUTE.MENU_BOARD.ADD,
    component: AddMenuBoardComponent,
  },
  {
    path: ROUTE.MENU_BOARD.EDIT + '/:id',
    component: AddMenuBoardComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.MENU_BOARD.LIST },
] as Routes;
