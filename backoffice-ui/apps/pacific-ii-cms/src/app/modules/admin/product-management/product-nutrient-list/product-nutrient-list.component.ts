import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { FuseErrorMessageComponent } from '@fuse/components/error-message/error-message.component';
import { FuseTableComponent } from '@fuse/components/table';
import { FuseUltils } from '@fuse/ultils';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { REGEX } from '../../../../core/const';
import { ProductNutrientManagementService } from '../../product-nutrient-management/product-nutrient-management.service';
import { ProductNutrientListService } from './product-nutrient-list.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';

@Component({
  selector: 'app-product-nutrient-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    FuseTableComponent,
    FuseErrorMessageComponent,
  ],
  templateUrl: './product-nutrient-list.component.html',
})
export class ProductNutrientListComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() id!: string;
  @Input() mode = 'view';

  dataSource: Array<any> = [];
  displayedColumns: any = [];

  nutrientForm!: UntypedFormGroup;
  loading = false;

  optionListNutrient: Array<any> = [];

  errorMessages = {
    nutritionId: {
      required: 'Please select nutrient!',
    },
    value: {
      required: 'Please enter amount per serving!',
      pattern: 'Invalid addition price value!',
    },
  };

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  /**
   * Constructor
   */
  constructor(
    private _toast: ToastrService,
    private _formBuilder: UntypedFormBuilder,
    private _changeDetectorRef: ChangeDetectorRef,
    private _nutrientsService: ProductNutrientManagementService,
    private _productNutrientsService: ProductNutrientListService,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.handleGetNutrients();
    this.handleGetProductNutrient();
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetNutrients(): void {
    this._nutrientsService.getProductNutrients().subscribe((res: any) => {
      this.optionListNutrient = res.content;
    });
  }

  handleGetProductNutrient(): void {
    this._productNutrientsService
      .getProductNutrients(this.id, '')
      .subscribe((res: any) => {
        this.nutritionArray.reset();
        this.nutritionArray.clear();
        res.content.forEach((item: any) => this.addNutrient(item));
      });
  }

  submitForm(): void {
    const nutrition = this.nutritionArray.value.map((item: any) => {
      return FuseUltils.trimObjectValues(item);
    });
    this.nutrientForm.patchValue({
      nutrition: nutrition,
    });

    for (const i of this.nutritionArray.controls) {
      i.get('nutritionId')?.markAsTouched();
      i.get('value')?.markAsTouched();
      i.get('name')?.markAsTouched();
      i.get('unit')?.markAsTouched();

      i.get('nutritionId')?.updateValueAndValidity();
      i.get('value')?.updateValueAndValidity();
      i.get('name')?.updateValueAndValidity();
      i.get('unit')?.updateValueAndValidity();
    }

    if (this.nutrientForm.invalid) {
      Utils.scrollToInvalid('.ng-invalid');
      return;
    }
    if (this.checkNutrientDuplicate()) {
      this._toast.error('Duplicate nutrients are not allowed.');
      return;
    }

    this.loading = true;
    const nutrientData = this.nutritionArray.value.map((item: any) => {
      return {
        nutritionId: item.nutritionId,
        value: item.value,
      };
    });
    this.handleUpdateProductNutrient(nutrientData);
  }

  handleUpdateProductNutrient(nutrientData: any): void {
    this._productNutrientsService
      .updateProductNutrients(this.id, nutrientData)
      .subscribe({
        next: () => {
          this.loading = false;
          this._toast.success('Update nutrient success!');
          this.handleGetProductNutrient();
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  onNutrientValueChange(event: any, element: any, index: any): void {
    const nutrient = this.optionListNutrient.find(
      (item: any) => item.id == event,
    );

    if (nutrient) {
      element.nutritionId = nutrient.id;
      element.name = nutrient.name;
      element.unit = nutrient.unit;
    }
    this.nutrientForm.markAsDirty();
    this.nutritionArray.controls[index].patchValue({ ...element });
  }

  onAmountValueChange(event: any, element: any, index: any): void {
    element.value = event.target.value;
    this.nutrientForm.markAsDirty();
    this.nutritionArray.controls[index].patchValue({ value: element.value });
  }

  checkNutrientDuplicate(): boolean {
    const nutrients = this.nutritionArray.value.map(
      (item: any) => item.nutritionId,
    );

    return new Set(nutrients).size !== nutrients.length;
  }

  get nutritionArray(): UntypedFormArray {
    return this.nutrientForm.get('nutrition') as UntypedFormArray;
  }

  getNutrientFormGroup(nutrient: any) {
    return nutrient as UntypedFormGroup;
  }

  addNutrient(nutrientData?: any): void {
    const nutrient = this._formBuilder.group({
      nutritionId: [
        nutrientData ? nutrientData.id : null,
        [Validators.required],
      ],
      value: [
        nutrientData ? nutrientData.value : null,
        [Validators.required, Validators.pattern(REGEX.NUMBER)],
      ],
      name: [nutrientData ? nutrientData.name : null],
      unit: [nutrientData ? nutrientData.unit : null],
    });
    this.nutritionArray.push(nutrient);

    if (!nutrientData) {
      this.nutrientForm.markAsDirty();
    }
  }

  removeNutrient(index: number) {
    this.nutritionArray.removeAt(index);
    this.nutrientForm.markAsDirty();
  }

  private initForm(): void {
    this.nutrientForm = this._formBuilder.group({
      nutrition: this._formBuilder.array([]),
    });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'name',
        name: 'Nutrient',
        selected: true,
        custom: true,
      },
      {
        key: 'value',
        name: 'Amount per Serving',
        selected: true,
        custom: true,
      },
      {
        key: 'unit',
        name: 'Unit',
        selected: true,
        custom: true,
      },
      {
        key: 'action',
        name: 'Action',
        selected: this.mode != 'view',
        custom: true,
      },
    ];
  }
}
