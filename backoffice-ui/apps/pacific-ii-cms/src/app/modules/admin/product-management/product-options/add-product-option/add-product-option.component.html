<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="web__h6 text-grey-900">
      {{ data?.detail ? 'Edit Option' : 'Add New Option' }}
    </div>

    <mat-horizontal-stepper #stepper labelPosition="bottom" [linear]="true">
      <mat-step [stepControl]="optionInfoForm" label="Option Information">
        <form [formGroup]="optionInfoForm">
          <div class="flex flex-col gap-4">
            <fuse-input
              class="w-full"
              [form]="optionInfoForm"
              [label]="'Option Group'"
              [name]="'title'"
              [placeholder]="'Enter option group'"
              [errorMessages]="errorMessages.title"
            />
            <fuse-input
              class="w-full"
              [form]="optionInfoForm"
              [label]="'Minimum'"
              [name]="'minimum'"
              [placeholder]="'Enter minimum'"
              [errorMessages]="errorMessages.minimum"
            />
            <fuse-input
              class="w-full"
              [form]="optionInfoForm"
              [label]="'Maximum'"
              [name]="'maximum'"
              [placeholder]="'Enter maximum'"
              [errorMessages]="errorMessages.maximum"
            />
          </div>

          <div class="flex items-start justify-end gap-2 mt-6">
            <button class="btn-outlined__primary__medium" (click)="onClose()">
              Cancel
            </button>
            <button
              (click)="checkOptionInfoFormValidate()"
              class="btn-contained__primary__medium"
              [disabled]="optionInfoForm.invalid"
            >
              Next
            </button>
          </div>
        </form>
      </mat-step>

      <mat-step [stepControl]="optionItemForm" label="List of Option Items">
        <form [formGroup]="optionItemForm" class="flex flex-col gap-6">
          <div
            class="flex justify-between gap-3 border-b-[1px] border-dashed border-grey-400 pb-6"
          >
            <div class="flex flex-col items-start justify-center gap-4">
              <div class="flex flex-col items-start gap-1">
                <div class="web__body1 text-grey-600">Option Group</div>
                <div class="web__body2 text-grey-900">
                  {{
                    optionInfoForm && optionInfoForm.value.title
                      ? optionInfoForm.value.title
                      : 'N/A'
                  }}
                </div>
              </div>

              <div class="flex items-center gap-2.5">
                <div class="flex flex-col items-start gap-1">
                  <div class="web__body1 text-grey-600">Minimum</div>
                  <div class="web__body2 text-grey-900">
                    {{
                      optionInfoForm && optionInfoForm.value.minimum
                        ? optionInfoForm.value.minimum
                        : 'N/A'
                    }}
                  </div>
                </div>
                <div class="flex flex-col items-start gap-1">
                  <div class="web__body1 text-grey-600">Maximum</div>
                  <div class="web__body2 text-grey-900">
                    {{
                      optionInfoForm && optionInfoForm.value.maximum
                        ? optionInfoForm.value.maximum
                        : 'N/A'
                    }}
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-end justify-end">
              <button
                class="btn-outlined__primary__medium"
                (click)="addOptionItem()"
              >
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                Add Option Item
              </button>
            </div>
          </div>

          <div
            class="flex flex-col w-full gap-4 overflow-auto h-60"
            formArrayName="items"
          >
            @for (item of itemArray.controls; track item; let i = $index) {
              <div class="flex w-full gap-4" [formGroupName]="i">
                <div class="flex flex-1 gap-4">
                  <fuse-input
                    class="w-full"
                    [form]="getOptionItemGroup(item)"
                    [label]="'Name'"
                    [name]="'name'"
                    [placeholder]="'Enter name'"
                    [errorMessages]="errorMessages.name"
                  />
                  <fuse-input
                    class="w-full"
                    [form]="getOptionItemGroup(item)"
                    [label]="'Addition Price'"
                    [name]="'additionPrice'"
                    [placeholder]="'Enter addition price'"
                    [prefixText]="tenantCurrency.currencyCode"
                    [prefixIconColor]="'#343330'"
                    [errorMessages]="errorMessages.additionPrice"
                  />
                  @if (data?.detail) {
                    <fuse-switch
                      class="w-full h-[66px]"
                      [form]="getOptionItemGroup(item)"
                      [label]="'Status'"
                      [name]="'active'"
                    />
                  }
                </div>

                <div class="w-10 mt-6 mr-0.5">
                  @if (itemArray.length > 1) {
                    <button
                      class="flex items-center justify-center w-10 h-10 rounded-3 shadow-3xl"
                      type="button"
                      (click)="removeOptionItem(i)"
                    >
                      <mat-icon
                        class="w-4 h-4 text-error-main"
                        [svgIcon]="'heroicons_outline:trash'"
                      ></mat-icon>
                    </button>
                  }
                </div>
              </div>
            }
          </div>

          <div class="flex items-start justify-between gap-2">
            <button matStepperPrevious class="btn-outlined__primary__medium">
              <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
              Back
            </button>

            <div class="flex items-start justify-end gap-2">
              <button class="btn-outlined__primary__medium" (click)="onClose()">
                Cancel
              </button>
              <button
                class="btn-contained__primary__medium"
                (click)="submitForm()"
                [disabled]="
                  loading ||
                  optionInfoForm.invalid ||
                  optionItemForm.invalid ||
                  (!optionInfoForm.dirty && !optionItemForm.dirty)
                "
              >
                @if (loading) {
                  <mat-spinner diameter="18" class="mr-0"></mat-spinner>
                }
                <span>Save</span>
              </button>
            </div>
          </div>
        </form>
      </mat-step>
    </mat-horizontal-stepper>
  </div>
</div>
