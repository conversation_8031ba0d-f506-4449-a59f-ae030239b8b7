import { Routes } from '@angular/router';
import { ROUTE } from '../../../core/const';
import { ProductManagementComponent } from './product-management.component';
import { AddProductComponent } from './add-product/add-product.component';
import { ProductDetailComponent } from './product-detail/product-detail.component';

export default [
  {
    path: ROUTE.PRODUCT.LIST,
    component: ProductManagementComponent,
  },
  {
    path: ROUTE.PRODUCT.ADD,
    component: AddProductComponent,
  },
  {
    path: ROUTE.PRODUCT.EDIT + '/:id',
    component: AddProductComponent,
  },
  {
    path: ROUTE.PRODUCT.DETAIL + '/:id',
    component: ProductDetailComponent,
  },
  {
    path: ROUTE.PRODUCT.IMPORT.MAIN,
    loadChildren: () => import('./import-products/import-products.routes'),
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.PRODUCT.LIST },
] as Routes;
