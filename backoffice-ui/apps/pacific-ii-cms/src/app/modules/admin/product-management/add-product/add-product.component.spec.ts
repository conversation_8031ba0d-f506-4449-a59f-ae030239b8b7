import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddProductComponent } from './add-product.component';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule } from 'ngx-toastr';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { StorageService } from '../../../../core/services/storage.service';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AddProductComponent', () => {
  let component: AddProductComponent;
  let fixture: ComponentFixture<AddProductComponent>;

  const tenantCurrency = {
    displayName: 'US Dollar',
    numericCode: 840,
    currencyCode: 'USD',
    symbol: '$',
    fractionDigits: 2,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        AddProductComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {},
            }),
          },
        },
        {
          provide: StorageService,
          useValue: {
            getTenantCurrency: jest.fn().mockReturnValue(tenantCurrency),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddProductComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
