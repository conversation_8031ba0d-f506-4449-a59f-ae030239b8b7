import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../core/const';

@Injectable({ providedIn: 'root' })
export class ProductCategoryManagementService {
  private _httpClient = inject(HttpClient);
  private _categories: ReplaySubject<any> = new ReplaySubject<any>(1);
  private _categoriesTree: ReplaySubject<any> = new ReplaySubject<any>(1);

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for product category
   */
  get categories$(): Observable<any> {
    return this._categories.asObservable();
  }

  /**
   * Getter for product category tree
   */
  get categoriesTree$(): Observable<any> {
    return this._categoriesTree.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all product category data
   */
  getProductCategories(queryParams: any = ''): Observable<any> {
    return this._httpClient
      .get<any>(`${API.PRODUCT_CATEGORY.LIST}?${queryParams}`)
      .pipe(
        tap((categories) => {
          this._categories.next(categories);
        }),
      );
  }

  /**
   * Get product category tree data
   */
  getCategoriesTree(): Observable<any> {
    return this._httpClient.get<any>(API.PRODUCT_CATEGORY.TREE).pipe(
      tap((categoriesTree) => {
        this._categoriesTree.next(categoriesTree.content);
      }),
    );
  }

  /**
   * Get product category detail
   */
  getCategoryDetail(id: string): Observable<any> {
    return this._httpClient.get<any>(
      API.PRODUCT_CATEGORY.DETAIL.replace('{id}', id),
    );
  }

  /**
   * Add new product category
   */
  addCategory(data: any): Observable<any> {
    return this._httpClient.post<any>(API.PRODUCT_CATEGORY.ADD, data);
  }

  /**
   * Update product category
   */
  updateCategory(id: string, data: any): Observable<any> {
    return this._httpClient.put<any>(
      API.PRODUCT_CATEGORY.UPDATE.replace('{id}', id),
      data,
    );
  }

  /**
   * Delete product category
   */
  deleteCategory(id: string): Observable<any> {
    return this._httpClient.delete<any>(
      API.PRODUCT_CATEGORY.DELETE.replace('{id}', id),
    );
  }
}
