import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { MatIconModule } from '@angular/material/icon';
import { map, Subject, takeUntil, tap } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { StorageService } from '../../../core/services/storage.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../core/user/user.service';
import {
  DATE_TIME_FORMAT,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
  USER_ACTION,
  USER_STATUS,
} from '../../../core/const';
import { FuseUltils } from '@fuse/ultils';
import { CustomerManagementService } from './customer-management.service';
import { AddCustomerComponent } from './add-customer/add-customer.component';
import { MatDialog } from '@angular/material/dialog';
import { IUserGroup } from './customer.types';
import { GroupManagementService } from '../group-management/group-management.service';
import { UserManagementService } from '../user-management/user-management.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-customer-list',
  standalone: true,
  templateUrl: './customer-management.component.html',
  imports: [FuseTableComponent, MatIconModule, FuseHelpLinkComponent],
  encapsulation: ViewEncapsulation.None,
})
export class CustomerManagementComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  profile!: any;
  queryParams!: any;
  userRoleList: Array<any> = [];

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;
  sortDefault: any = {
    field: 'lastName',
    direction: 'asc',
  };
  selectedCustomer: Array<string> = [];

  searchForm: any;
  actions: Array<IAction> = [];
  bulkActions: Array<IAction> = [];

  permissionList = {
    create: false,
    update: false,
    delete: false,
  };

  groupList: any = [];
  groupParams: any = {
    filter: {},
    page: 0,
    size: 10,
  };
  groupAppend = false;

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  /**
   * Constructor
   */
  constructor(
    private _customerService: CustomerManagementService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _storageService: StorageService,
    private _router: Router,
    private _confirmationService: FuseConfirmationService,
    private _toast: ToastrService,
    private _userService: UserService,
    private _groupsService: GroupManagementService,
    private _usersService: UserManagementService,
    private _dialog: MatDialog,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.searchForm = this.initSearchForm();
    this.handleGetGroups();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetCustomers();
      });
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleLazyLoadSelect(event: any) {
    this.groupAppend = event.value.page > this.groupParams.page;
    this.groupParams.filter.byGroupName = event.value.search;
    this.groupParams.page = event.value.page;

    this.handleGetGroups();
  }

  handleGetGroups(): void {
    this._groupsService
      .getGroups(FuseUltils.objectToQueryString(this.groupParams))
      .pipe(
        map((res: any) => {
          const groupList = res.content.map((item: any) => {
            return {
              ...item,
              label: item.groupName,
              value: item.path,
            };
          });

          return groupList;
        }),
      )
      .subscribe((groups: any) => {
        this.groupList = this.groupAppend
          ? [...this.groupList, ...groups]
          : [{ label: 'All', value: '' }, ...groups];

        const updatedForm = { ...this.searchForm };
        updatedForm.basic[2].options = [...this.groupList];
        this.searchForm = { ...updatedForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetCustomers(): void {
    const mappedData = this.getCustomerParams(this.queryParams);
    this._customerService
      .getCustomers(FuseUltils.objectToQueryString(mappedData))
      .subscribe((users: any) => {
        this.dataSource = users.content;
        this.total = users.totalElements;
      });
  }

  getCustomerParams(queryParams: any): any {
    const tenantId = this._storageService.getTenantId();

    const filter = {
      byTenantId: tenantId,
      byName: queryParams['name'] || '',
      byContainingEmail: queryParams['email'] || '',
      byUserGroupPath: queryParams['groupPath'] || '',
      byUserStatuses:
        queryParams['statuses'] || this.searchForm.basic[3].defaultValue,
      byUserId: queryParams['id'] || '',
      bySsoId: queryParams['ssoId'] || '',
      byContainingExternalId: queryParams['externalId'] || '',
      byPhoneNumber: queryParams['phoneNumber'] || '',
      byContainingUserCardId: queryParams['cardId'] || '',
      groupLevel: 0,
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  gotoImportCustomerList(): void {
    this._router.navigate([
      `${ROUTE.CUSTOMERS.MAIN}/${ROUTE.CUSTOMERS.IMPORT.MAIN}`,
    ]);
  }

  onOpenAddCustomerDialog(): void {
    const dialogRef = this._dialog.open(AddCustomerComponent, {
      width: '60%',
      autoFocus: false,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetCustomers();
      }
    });
  }

  handleViewCustomerDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.CUSTOMERS.MAIN}/${ROUTE.CUSTOMERS.DETAIL}/${element.id}`,
    ]);
  }

  onOpenActivateCustomerDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Activate Customer',
      message: 'Please confirm to activate this customer.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Activate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateCustomerStatus(element, USER_STATUS.ACTIVE);
      }
    });
  }

  onOpenArchiveCustomerDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Archive Customer',
      message: 'Please confirm to archive this customer.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Archive',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateCustomerStatus(element, USER_STATUS.ARCHIVED);
      }
    });
  }

  onOpenBlockCustomerDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Block Customer',
      message: 'Please confirm to block this customer.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Block',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateCustomerStatus(element, USER_STATUS.BLOCKED);
      }
    });
  }

  handleUpdateCustomerStatus(customer: any, status: string): void {
    const data = {
      ...customer,
      userStatus: status,
      updatePermissions: customer.permissions,
    };

    let message = '';
    switch (status) {
      case USER_STATUS.ACTIVE:
        if (data?.schedulingDeletedAt) {
          data.schedulingDeletedAt = null;
        }
        message = 'Activate customer success!';
        break;
      case USER_STATUS.ARCHIVED:
        message = 'Archive customer success!';
        break;
      case USER_STATUS.BLOCKED:
        message = 'Block customer success!';
        break;
    }

    this._customerService.updateCustomer(data).subscribe(() => {
      this._toast.success(message);
      this.handleGetCustomers();
    });
  }

  handleResendInvitation(element: any): void {
    this._usersService.resendInvitation(element.id).subscribe(() => {
      this._toast.success('Resend invitation success!');
    });
  }

  onOpenDeleteCustomerDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Customer',
      message: `Please confirm to delete this customer. <br/>After confirming deletion, the customer's status will be changed to "Archived" and the customer will be automatically deleted by the system after a set period.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteCustomer(element);
      }
    });
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedCustomer = event.map((item) => item.id);
  }

  handleClickDeleteSelected() {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Customer(s)',
      message: `Please confirm to delete the selected customer(s). <br/>After confirming deletion, the status of the selected customer(s) will be changed to "Archived" and the selected customer(s) will be automatically deleted by the system after a set period.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const deleteData = {
          byUserIds: this.selectedCustomer,
        };
        this._customerService.deleteCustomer(deleteData).subscribe(() => {
          this._toast.success(
            'Selected customer(s) deletion schedule processed successfully!',
          );
          this.handleGetCustomers();
        });
      }
    });
  }

  handleDeleteCustomer(customer: any): void {
    const data = {
      byUserId: customer.id,
    };
    this._customerService.deleteCustomer(data).subscribe(() => {
      this._toast.success('Customer deletion schedule processed successfully!');
      this.handleGetCustomers();
    });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this._userService.userProfile$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((user: any) => {
        this.profile = user;
      });

    this.displayedColumns = [
      {
        key: 'id',
        name: 'Customer ID',
        sort: true,
        selected: false,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'externalId',
        name: 'Customer No.',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'firstName',
        name: 'First Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'lastName',
        name: 'Last Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      { key: 'email', name: 'Email', sort: true, selected: true },
      {
        key: 'userGroup',
        name: 'Group',
        selected: true,
        render: (group: IUserGroup) => {
          return group.groupName;
        },
      },
      { key: 'totalSubAccounts', name: 'Sub-accounts', selected: true },

      {
        key: 'createdAt',
        name: 'Registration Date',
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'schedulingDeletedAt',
        name: 'Deletion Date',
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'userStatus',
        name: 'Status',
        selected: true,
        renderHtml: (value: string) => {
          let chipColor = '';
          switch (value) {
            case 'CREATED':
              chipColor = 'bg-[#21B5D5] text-white';
              break;
            case 'ACTIVE':
              chipColor = 'bg-[#36B37E] text-white';
              break;
            case 'ARCHIVED':
              chipColor = 'bg-[#D0021B] text-white';
              break;
            case 'BLOCKED':
              chipColor = 'bg-[#FFAB00] text-black';
              break;
            default:
              chipColor = 'bg-[#21B5D5] text-white';
              break;
          }
          return `<mat-chip class="px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</mat-chip>`;
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewCustomerDetail(element),
      },
      {
        name: 'Activate',
        type: 'event',
        hidden: (element) =>
          (element.userStatus != USER_STATUS.ARCHIVED &&
            element.userStatus != USER_STATUS.BLOCKED &&
            !(element.userStatus == USER_STATUS.CREATED && !element.email)) ||
          !this.permissionList.update,
        callback: (element) => this.onOpenActivateCustomerDialog(element),
      },
      {
        name: 'Archive',
        type: 'event',
        hidden: (element) =>
          element.userStatus != USER_STATUS.ACTIVE ||
          !this.permissionList.update,
        callback: (element) => this.onOpenArchiveCustomerDialog(element),
      },
      {
        name: 'Block',
        type: 'event',
        hidden: (element) =>
          element.userStatus != USER_STATUS.ACTIVE ||
          !this.permissionList.update,
        callback: (element) => this.onOpenBlockCustomerDialog(element),
      },
      {
        name: 'Resend Invitation',
        type: 'event',
        hidden: (element) =>
          element.userStatus == USER_STATUS.ARCHIVED ||
          element.userStatus == USER_STATUS.BLOCKED ||
          !(
            element?.email &&
            element?.userNonCompletedActions?.includes(
              USER_ACTION.KEYCLOAK_REGISTRATION,
            )
          ),
        callback: (element) => this.handleResendInvitation(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: (element) =>
          !this.permissionList.delete ||
          !(
            element.userStatus !== USER_STATUS.ARCHIVED ||
            (element.userStatus == USER_STATUS.ARCHIVED &&
              !element?.schedulingDeletedAt)
          ),
        callback: (element) => this.onOpenDeleteCustomerDialog(element),
      },
    ];

    this.bulkActions = [
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.delete,
        callback: () => this.handleClickDeleteSelected(),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Name',
          name: 'name',
          placeholder: 'Search name',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'Email',
          name: 'email',
          placeholder: 'Search email',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'Group',
          name: 'groupPath',
          type: 'lazy-load-select',
          defaultValue: '',
          showSearch: true,
          options: [],
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: ['CREATED', 'ACTIVE', 'BLOCKED'],
          isMultiple: true,
          options: [
            { label: 'Created', value: 'CREATED' },
            { label: 'Active', value: 'ACTIVE' },
            {
              label: 'Archived',
              value: 'ARCHIVED',
            },
            { label: 'Blocked ', value: 'BLOCKED' },
          ],
        },
      ],
      advanced: [
        {
          label: 'User ID',
          name: 'id',
          placeholder: 'Enter user ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Keycloak ID',
          name: 'ssoId',
          placeholder: 'Enter keycloak ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Phone',
          name: 'phoneNumber',
          placeholder: 'Enter phone',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Customer No.',
          name: 'externalId',
          placeholder: 'Enter customer no.',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Card ID',
          name: 'cardId',
          placeholder: 'Enter card id',
          type: 'text',
          defaultValue: '',
        },
        // {
        //   label: 'datetime',
        //   name: 'datetime',
        //   placeholder: 'datetime',
        //   type: 'datetime',
        //   defaultValue: '',
        // },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          create: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.CUSTOMER_MGMT, scope: ROLE_SCOPES.ADD },
          ]),
          update: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.CUSTOMER_MGMT, scope: ROLE_SCOPES.UPDATE },
          ]),
          delete: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.CUSTOMER_MGMT, scope: ROLE_SCOPES.DELETE },
          ]),
        };

        if (this.displayedColumns) {
          this.displayedColumns = [...this.displayedColumns];
        }
      });
  }
}
