<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="flex items-center gap-2 web__h6 text-grey-900">
      Add Customer
      <fuse-help-link
        [url]="'/customer-management/cus-manage/#add-a-customer'"
      ></fuse-help-link>
    </div>

    <form class="flex flex-col gap-4" [formGroup]="customerForm">
      <fuse-input
        class="w-full"
        [form]="customerForm"
        [label]="'Email'"
        [name]="'email'"
        [placeholder]="'Enter email'"
        [errorMessages]="errorMessages.email"
      />
      <fuse-input
        class="w-full"
        [form]="customerForm"
        [label]="'First Name'"
        [name]="'firstName'"
        [placeholder]="'Enter first name'"
        [errorMessages]="errorMessages.firstName"
      />
      <fuse-input
        class="w-full"
        [form]="customerForm"
        [label]="'Last Name'"
        [name]="'lastName'"
        [placeholder]="'Enter last name'"
        [errorMessages]="errorMessages.lastName"
      />
    </form>
  </div>

  <div class="flex items-start justify-end gap-2">
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      Cancel
    </button>
    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || customerForm.invalid"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      Add
    </button>
  </div>
</div>
