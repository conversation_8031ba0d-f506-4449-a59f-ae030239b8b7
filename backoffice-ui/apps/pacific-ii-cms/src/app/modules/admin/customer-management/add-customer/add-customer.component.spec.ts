import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { FuseUltils } from '@fuse/ultils';
import { USER_TYPE } from 'apps/pacific-ii-cms/src/app/core/const';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { AuthorizationService } from 'apps/pacific-ii-cms/src/app/services/authorization.service';
import { ToastrModule } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { CustomerManagementService } from '../customer-management.service';
import { ICustomer } from '../customer.types';
import { AddCustomerComponent } from './add-customer.component';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AddCustomerComponent', () => {
  let component: AddCustomerComponent;
  let fixture: ComponentFixture<AddCustomerComponent>;

  const mockTenantId = '135261622300581888';

  const mockCustomer: ICustomer = {
    id: '125157569389419520',
    ssoId: null,
    externalId: null,
    uniqueExternalId: null,
    firstName: 'aa',
    lastName: 'aaa',
    email: '',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'ACTIVE',
    phoneNumber: null,
    totalSubAccounts: null,
    totalSponsors: null,
    userGroup: {
      id: '121520479628366848',
      tenantId: '116024918514279424',
      path: '121520479628366848',
      groupName: 'Default',
      groupKey: 'DEFAULT_GROUP',
      parent: null,
      isDefaultGroup: true,
      description:
        'By default, all customer of tenant would be added into this group. ',
      createdAt: null,
      updatedAt: null,
    },
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '125157569389419520',
        userRoleId: '125157222424000514',
        permissionStatus: 'ACTIVE',
      },
    ],
    createdAt: *************,
    updatedAt: *************,
  };

  const mockUserRoleListResponse = {
    content: [
      {
        id: '125157222424000512',
        roleTitle: 'Tenant Owner',
        externalId: 'TENANT_OWNER',
        tenantId: mockTenantId,
        createdAt: *************,
        updatedAt: *************,
        permissions: [],
      },
      {
        id: '125157222424000513',
        roleTitle: 'Tenant Operator',
        externalId: 'TENANT_OPERATOR',
        tenantId: mockTenantId,
        createdAt: *************,
        updatedAt: *************,
        permissions: [],
      },
      {
        id: '126930861470057472',
        roleTitle: 'Customer Manager',
        externalId: null,
        tenantId: mockTenantId,
        createdAt: 1730262675649,
        updatedAt: 1730262675649,
        permissions: [],
      },
    ],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddCustomerComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: StorageService,
          useValue: {
            getTenantId: jest.fn().mockReturnValue(mockTenantId),
          },
        },
        {
          provide: CustomerManagementService,
          useValue: {
            addCustomer: jest.fn().mockImplementation((value: ICustomer) => {
              if (value.id === 'test') {
                return throwError(null);
              }

              return of(null);
            }),
          },
        },
        {
          provide: AuthorizationService,
          useValue: {
            getUserRole: jest
              .fn()
              .mockReturnValue(of(mockUserRoleListResponse)),
          },
        },
      ],
    }).compileComponents();

    jest.spyOn(AddCustomerComponent.prototype, 'handleGetUserRoles');

    fixture = TestBed.createComponent(AddCustomerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_toast'], 'success');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.handleGetUserRoles).toHaveBeenCalled();
  });

  it('should send request to get the user roles when run handleGetUserRoles', () => {
    const params = {
      'filter.byTenantId': mockTenantId,
      'filter.byExternalId': USER_TYPE.CUSTOMER,
    };

    component.handleGetUserRoles();

    expect(component['_authorizationService'].getUserRole).toHaveBeenCalledWith(
      FuseUltils.objectToQueryString(params),
    );
    expect(component.userRoleList).toEqual(mockUserRoleListResponse.content);
  });

  it('should send request to add customer and show success toast after success when run handleAddCustomerGroup ', () => {
    jest.spyOn(component, 'onClose');
    component.loading = true;

    component.handleAddCustomerGroup(mockCustomer);

    expect(component['_customerService'].addCustomer).toHaveBeenCalledWith(
      mockCustomer,
    );
    expect(component.loading).toBe(false);
    expect(component['_toast'].success).toHaveBeenCalledWith(
      'Add customer success!',
    );
    expect(component.onClose).toHaveBeenCalledWith('save');
  });

  it('should send request to add customer and show nothing after fail when run handleAddCustomerGroup ', () => {
    jest.spyOn(component, 'onClose');
    component.loading = true;
    const failCustomer = { ...mockCustomer, id: 'test' };

    component.handleAddCustomerGroup(failCustomer);

    expect(component['_customerService'].addCustomer).toHaveBeenCalledWith(
      failCustomer,
    );
    expect(component.loading).toBe(false);
    expect(component['_toast'].success).not.toHaveBeenCalledWith(
      'Add customer success!',
    );
    expect(component.onClose).not.toHaveBeenCalledWith('save');
  });

  it('should check the validation of form and call handleAddCustomer if form is valid when run submitForm', () => {
    const data = {
      email: '<EMAIL>',
      firstName: 'First',
      lastName: 'Last',
      tenantId: mockTenantId,
      userType: 'CUSTOMER',
      userRoleId: mockUserRoleListResponse.content[0].id,
    };
    component.userRoleList = mockUserRoleListResponse.content;
    component.customerForm.patchValue(data);
    jest.spyOn(component, 'handleAddCustomerGroup');

    component.submitForm();

    expect(component.customerForm.invalid).toBe(false);
    expect(component.handleAddCustomerGroup).toHaveBeenCalledWith(data);
  });

  it('should check the validation of form and do nothing if form is invalid when run submitForm', () => {
    jest.spyOn(component, 'handleAddCustomerGroup');

    component.submitForm();

    expect(component.customerForm.invalid).toBe(true);
    expect(component.handleAddCustomerGroup).not.toHaveBeenCalled();
  });

  it('should close the dialog with message you passed to method when run onClose', () => {
    component.onClose('test');

    expect(component['_dialogRef'].close).toHaveBeenCalledWith('test');
  });
});
