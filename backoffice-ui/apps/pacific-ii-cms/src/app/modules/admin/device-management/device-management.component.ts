import {
  <PERSON><PERSON><PERSON>w<PERSON>nit,
  ChangeDetector<PERSON>ef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseUltils } from '@fuse/ultils';
import { DateTime } from 'luxon';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import {
  DATE_TIME_FORMAT,
  DEVICE_STATUS,
  DeviceTypeList,
  R<PERSON><PERSON>_MODULES,
  ROLE_SCOPES,
  ROUTE,
  TENANT_STATUS,
} from '../../../core/const';
import { StorageService } from '../../../core/services/storage.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';
import { Utils } from '../../../core/utils/utils';
import { StoreManagementService } from '../store-management/store-management.service';
import { TenantManagementService } from '../tenant-management/tenant-management.service';
import { DeviceAssignComponent } from './device-assgin/device-assign.component';
import { DeviceManagementService } from './device-management.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-device-management',
  standalone: true,
  templateUrl: './device-management.component.html',
  imports: [
    ReactiveFormsModule,
    FuseTableComponent,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    FuseHelpLinkComponent,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class DeviceManagementComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  queryParams!: any;

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;

  searchForm: any;
  actions: Array<IAction> = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };
  userMode: any;
  tenantId: any;

  optionListTenant: Array<any> = [];
  optionListStore: Array<any> = [];

  permissionList = {
    update: false,
    assign: false,
  };

  storeAppend = false;
  storeParams: any = {
    filter: {},
    page: 0,
    size: 10,
  };

  tenantAppend = false;
  tenantParams: any = {
    filter: {
      statuses: [TENANT_STATUS[1].value],
    },
    page: 0,
    size: 10,
  };

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _storageService: StorageService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _storeService: StoreManagementService,
    private _tenantService: TenantManagementService,
    private _devicesService: DeviceManagementService,
    private _confirmationService: FuseConfirmationService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.userMode = this._storageService.getUserMode();
    this.tenantId = this._storageService.getTenantId();
    this.searchForm = this.initSearchForm();
    this.handleGetTenants();

    this.initTableColumn();
    if (this.userMode == 'tenant') {
      this.displayedColumns.splice(2, 1);
    }
  }

  ngOnInit(): void {
    this.handleGetStores();
    this.handleGetTenants();

    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetDevices();
      });
  }

  handleLazyLoadSelect(event: any) {
    if (event.controlName === 'tenantId') {
      this.tenantAppend = event.value.page > this.tenantParams.page;
      this.tenantParams.filter.name = event.value.search;
      this.tenantParams.page = event.value.page;

      this.handleGetTenants();
    } else {
      this.storeAppend = event.value.page > this.storeParams.page;
      this.storeParams.filter.name = event.value.search;
      this.storeParams.page = event.value.page;

      this.handleGetStores();
    }
  }

  handleGetTenants(): void {
    this._tenantService
      .getTenants(FuseUltils.objectToQueryString(this.tenantParams))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((tenants) => {
        const tenantServer = tenants.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.tenantId,
          };
        });
        this.optionListTenant = this.tenantAppend
          ? [...this.optionListTenant, ...tenantServer]
          : [{ label: 'All', value: '' }, ...tenantServer];

        const updatedForm = { ...this.searchForm };
        updatedForm.advanced[1].options = [...this.optionListTenant];

        this.searchForm = { ...updatedForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetStores() {
    this._storeService
      .getStores(FuseUltils.objectToQueryString(this.storeParams))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((stores) => {
        const storeServer = stores.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.storeId,
          };
        });
        this.optionListStore = this.storeAppend
          ? [...this.optionListStore, ...storeServer]
          : [{ label: 'All', value: '' }, ...storeServer];

        const updatedForm = { ...this.searchForm };

        updatedForm.advanced[2].options = [...this.optionListStore];

        this.searchForm = { ...updatedForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetDisplayedTenants(tenantIds: Array<any>) {
    const params = {
      filter: {
        ids: tenantIds,
      },
      page: 0,
      size: this.queryParams.size,
    };
    return this._tenantService
      .getTenants(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((res: any) => {
          if (this.displayedColumns) {
            this.displayedColumns[2] = {
              key: 'tenantId',
              name: 'Tenant Name',
              selected: true,
              render: (value: string) => {
                const tenant = res.content.find(
                  (item: any) => item.tenantId == value,
                );
                return tenant ? tenant.name : `Unknown Tenant (${value})`;
              },
            };

            this.displayedColumns = [...this.displayedColumns];
            this._changeDetectorRef.detectChanges();
          }
        }),
      );
  }

  handleGetDisplayedStores(storeIds: Array<any>) {
    const params = {
      filter: {
        storeIds: storeIds,
      },
      page: 0,
      size: this.queryParams.size,
    };
    return this._storeService
      .getStores(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((res: any) => {
          if (this.displayedColumns) {
            const index = this.userMode == 'tenant' ? 2 : 3;
            this.displayedColumns[index] = {
              key: 'storeId',
              name: 'Store Name',
              sort: true,
              selected: true,
              render: (value: string) => {
                const store = res.content.find(
                  (item: any) => item.storeId == value,
                );
                return store ? store.name : `Unknown Store (${value})`;
              },
            };

            this.displayedColumns = [...this.displayedColumns];
            this._changeDetectorRef.detectChanges();
          }
        }),
      );
  }

  ngAfterViewInit(): void {
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleGetDevices(): void {
    const mappedData = this.getDeviceParams(this.queryParams);
    this._devicesService
      .getDevices(FuseUltils.objectToQueryString(mappedData))
      .pipe(
        tap((devices: any) => {
          this.dataSource = devices.content;
          this.total = devices.totalElements;
        }),
        switchMap((devices: any) => {
          const tenantIds = [
            ...new Set(
              devices.content
                .filter((value: any) => value.tenantId != null)
                .map((value: any) => value.tenantId),
            ),
          ];
          const storeIds = [
            ...new Set(
              devices.content
                .filter((value: any) => value.storeId != null)
                .map((value: any) => value.storeId),
            ),
          ];
          return forkJoin([
            this.userMode === 'admin'
              ? this.handleGetDisplayedTenants(tenantIds)
              : of(null),
            this.handleGetDisplayedStores(storeIds),
          ]);
        }),
      )
      .subscribe();
  }

  getDeviceParams(queryParams: any): any {
    const filter = {
      name: queryParams['name'] || '',
      deviceIds: queryParams['deviceId'] || '',
      tenantId: queryParams['tenantId'] || '',
      storeId: queryParams['storeId'] || '',
      type: queryParams['type'] || '',
      serialNumber: queryParams['serialNumber'] || '',
      model: queryParams['model'] || '',
      firmwareVersion: queryParams['firmwareVersion'] || '',
      statuses:
        queryParams['statuses'] || this.searchForm.basic[1].defaultValue,
    };
    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    if (queryParams['sortFields']) {
      mappedData.sortFields = mappedData.sortFields.replace('deviceId', 'id');
    }

    return mappedData;
  }

  handleViewDeviceDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.DEVICES.MAIN}/${ROUTE.DEVICES.DETAIL}/${element.deviceId}`,
    ]);
  }

  handleEditDevice(element: any): void {
    this._router.navigate([
      `${ROUTE.DEVICES.MAIN}/${ROUTE.DEVICES.EDIT}/${element.deviceId}`,
    ]);
  }

  onOpenUnassignDeviceDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Unassign Device',
      message: 'Please confirm to unassign this device.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Unassign',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUnassignDevice(element);
      }
    });
  }

  onOpenAssignDeviceDialog(element: any): void {
    const dialogRef = this._dialog.open(DeviceAssignComponent, {
      width: '60%',
      data: element,
      autoFocus: false,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetDevices();
      }
    });
  }

  handleUnassignDevice(device: any): void {
    this._devicesService
      .unassign(device.deviceId, {
        tenantId: device.tenantId,
        storeId: device.storeId,
      })
      .subscribe(() => {
        this._toast.success('Unassign device success!');
        this.handleGetDevices();
      });
  }

  onOpenChangeStatusDialog(element: any, doActivate = true) {
    const dialogRef = this._confirmationService.open({
      title: `${doActivate ? 'Activate' : 'Deactivate'} Device`,
      message: `Please confirm to ${doActivate ? 'activate' : 'deactivate'} this device`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: doActivate ? 'Activate' : 'Deactivate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleChangeDeviceStatus(element, doActivate);
      }
    });
  }

  handleChangeDeviceStatus(device: any, doActivate: boolean) {
    this._devicesService
      .changeDeviceStatus(device.deviceId, doActivate)
      .subscribe(() => {
        this._toast.success(
          `${doActivate ? 'Activate' : 'Deactivate'} device successfully!`,
        );
        this.handleGetDevices();
      });
  }

  checkOnline(value: number) {
    if (value == null) {
      return false;
    }

    const compareTime = DateTime.now().minus({ minutes: 5 });

    if (value >= compareTime.valueOf()) {
      return true;
    }

    return false;
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'deviceId',
        name: 'Device ID',
        sort: true,
        selected: true,
        custom: true,
      },
      {
        key: 'name',
        name: 'Device Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'tenantId',
        name: 'Tenant Name',
        // sort: true,
        selected: true,
        render: (value: string) => {
          const tenant = this.optionListTenant.find(
            (item: any) => item.tenantId == value,
          );
          return tenant ? tenant.name : value;
        },
      },
      {
        key: 'storeId',
        name: 'Store Name',
        sort: true,
        selected: true,
        render: (value: string) => {
          return `Unknown Store (${value})`;
        },
      },
      {
        key: 'type',
        name: 'Device Type',
        // sort: true,
        selected: true,
      },
      {
        key: 'model',
        name: 'Device Model',
        selected: true,
      },
      {
        key: 'firmwareVersion',
        name: 'Device Version',
        selected: false,
      },
      {
        key: 'serialNumber',
        name: 'Device Serial Number',
        selected: false,
      },
      {
        key: 'lastOnline',
        name: 'Last Online',
        sort: true,
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'status',
        name: 'Device Status',
        selected: true,
        renderHtml: (value: string) => {
          const chipColor = Utils.getStatusColor(value);
          return `<mat-chip class="px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</mat-chip>`;
        },
      },
      {
        key: 'assignedDate',
        name: 'Assigned Date',
        sort: true,
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewDeviceDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        hidden: (element) =>
          element.status != DEVICE_STATUS.ACTIVE || !this.permissionList.update,
        callback: (element) => this.handleEditDevice(element),
      },
      {
        name: 'Unassign',
        type: 'event',
        hidden: (element) =>
          element.status != DEVICE_STATUS.ACTIVE ||
          this.userMode != 'admin' ||
          !this.permissionList.assign,
        callback: (element) => this.onOpenUnassignDeviceDialog(element),
      },
      {
        name: 'Change Store',
        type: 'event',
        hidden: (element) =>
          element.status != DEVICE_STATUS.ACTIVE ||
          this.userMode != 'tenant' ||
          !this.permissionList.assign,
        callback: (element) => this.onOpenAssignDeviceDialog(element),
      },
      {
        name: 'Assign device',
        type: 'event',
        hidden: (element) =>
          element.status != DEVICE_STATUS.AVAILABLE ||
          this.userMode != 'admin' ||
          !this.permissionList.assign,
        callback: (element) => this.onOpenAssignDeviceDialog(element),
      },
      {
        name: 'Activate',
        type: 'event',
        hidden: (element) =>
          element.status !== DEVICE_STATUS.INACTIVE ||
          !this.permissionList.update,
        callback: (element) => this.onOpenChangeStatusDialog(element),
      },
      {
        name: 'Deactivate',
        type: 'event',
        hidden: (element) =>
          element.status !== DEVICE_STATUS.ACTIVE ||
          !this.permissionList.update,
        callback: (element) => this.onOpenChangeStatusDialog(element, false),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        // {
        //   label: 'Search',
        //   name: 'name',
        //   placeholder: 'Search',
        //   type: 'text',
        //   prefixIcon: 'styl:MagnifyingGlassOutlineBold',
        //   defaultValue: '',
        // },
        {
          label: 'Device Name',
          name: 'name',
          placeholder: 'Enter device name',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: [DEVICE_STATUS.ACTIVE, DEVICE_STATUS.AVAILABLE],
          isMultiple: true,
          options: [
            { label: 'Active', value: DEVICE_STATUS.ACTIVE },
            { label: 'Available', value: DEVICE_STATUS.AVAILABLE },
            { label: 'Inactive ', value: DEVICE_STATUS.INACTIVE },
            {
              label: 'Broken',
              value: DEVICE_STATUS.BROKEN,
            },
          ],
        },
        // {
        //   label: 'Assigned Date',
        //   name: 'assignedDate',
        //   type: 'datetime',
        //   defaultValue: '',
        // },
      ],
      advanced: [
        {
          label: 'Device ID',
          name: 'deviceId',
          placeholder: 'Enter device ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Tenant Name',
          name: 'tenantId',
          placeholder: 'Enter tenant name',
          type: 'lazy-load-select',
          showSearch: true,
          defaultValue: '',
          options: [],
          hidden: this.userMode == 'tenant',
        },
        {
          label: 'Store Name',
          name: 'storeId',
          placeholder: 'Enter store name',
          type: 'lazy-load-select',
          showSearch: true,
          defaultValue: '',
          options: [],
        },
        {
          label: 'Device Type',
          name: 'type',
          placeholder: 'Select device type',
          type: 'select',
          defaultValue: '',
          options: [{ label: 'All', value: '' }, ...DeviceTypeList],
        },
        {
          label: 'Device Model',
          name: 'model',
          placeholder: 'Enter device model',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Device Version',
          name: 'firmwareVersion',
          placeholder: 'Enter device version',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Serial Number',
          name: 'serialNumber',
          placeholder: 'Enter serial number',
          type: 'text',
          defaultValue: '',
        },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.DEVICE_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
          assign: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.DEVICE_MGMT,
              scope: ROLE_SCOPES.ASSIGN,
            },
          ]),
        };
      });
  }
}
