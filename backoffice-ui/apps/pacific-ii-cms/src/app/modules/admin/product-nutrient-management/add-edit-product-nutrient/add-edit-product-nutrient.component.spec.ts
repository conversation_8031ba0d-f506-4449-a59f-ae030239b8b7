import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddEditProductNutrientComponent } from './add-edit-product-nutrient.component';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule } from 'ngx-toastr';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AddEditProductNutrientComponent', () => {
  let component: AddEditProductNutrientComponent;
  let fixture: ComponentFixture<AddEditProductNutrientComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        AddEditProductNutrientComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {},
            }),
          },
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        { provide: MAT_DIALOG_DATA, useValue: { data: {} } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddEditProductNutrientComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
