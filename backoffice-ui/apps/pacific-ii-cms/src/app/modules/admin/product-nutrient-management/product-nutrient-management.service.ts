import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../core/const';

@Injectable({ providedIn: 'root' })
export class ProductNutrientManagementService {
  private _httpClient = inject(HttpClient);
  private _nutrients: ReplaySubject<any> = new ReplaySubject<any>(1);
  private _nutrientDetail: ReplaySubject<any> = new ReplaySubject<any>(1);

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for product nutrient
   */
  get nutrients$(): Observable<any> {
    return this._nutrients.asObservable();
  }

  /**
   * Getter for product nutrient detail
   */
  get nutrientDetail$(): Observable<any> {
    return this._nutrientDetail.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all product nutrient data
   */
  getProductNutrients(queryParams: any = ''): Observable<any> {
    return this._httpClient
      .get<any>(`${API.PRODUCT_NUTRITION.LIST}?${queryParams}`)
      .pipe(
        tap((nutrients) => {
          this._nutrients.next(nutrients);
        }),
      );
  }

  /**
   * Get product nutrient detail
   */
  getNutrientDetail(id: string): Observable<any> {
    return this._httpClient
      .get<any>(API.PRODUCT_NUTRITION.DETAIL.replace('{id}', id))
      .pipe(
        tap((detail) => {
          this._nutrientDetail.next(detail);
        }),
      );
  }

  /**
   * Delete product nutrient
   */
  deleteNutrient(id: string): Observable<any> {
    return this._httpClient.delete<any>(
      API.PRODUCT_NUTRITION.DELETE.replace('{id}', id),
    );
  }

  addNewNutrient(data: any, id?: any): Observable<any> {
    return this._httpClient.post<any>(API.PRODUCT_NUTRITION.ADD, data);
  }

  editNutrient(data: any, id?: any): Observable<any> {
    return this._httpClient.put<any>(
      API.PRODUCT_NUTRITION.UPDATE.replace('{id}', id),
      data,
    );
  }
}
