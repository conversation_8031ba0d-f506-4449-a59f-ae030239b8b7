import { provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { FuseUltils } from '@fuse/ultils';
import { of } from 'rxjs';
import { API } from '../../../core/const';
import { provideIcons } from '../../../core/icons/icons.provider';
import { StorageService } from '../../../core/services/storage.service';
import { CustomerManagementService } from '../customer-management/customer-management.service';
import { Currency } from '../tenant-management/tenant.types';
import { TransactionHistoryComponent } from './transaction-history.component';
import { TransactionHistoryService } from './transaction-history.service';
import { ITransactionHistoryResponse } from './transaction-history.types';
import { provideTranslate } from '../../../core/transloco/transloco.provider';

describe('TransactionHistoryComponent', () => {
  let component: TransactionHistoryComponent;
  let fixture: ComponentFixture<TransactionHistoryComponent>;
  let transactionService: TransactionHistoryService;
  let customerService: CustomerManagementService;
  let changeDetectionRef: ChangeDetectorRef;
  let storageService: StorageService;

  let transactionServiceMock: jest.SpyInstance;
  let customerServiceMock: jest.SpyInstance;
  let httpTestingController: HttpTestingController;

  /**
   * Component mock component data
   */
  const mockQueryParams = {
    filter: {
      customerIds: [],
      ids: [],
      paymentTransactionIds: [],
    },
    size: 10,
    page: 0,
    sortDirection: 'desc',
    sortFields: ['createdAt'],
  };

  const mockTableColumns = [
    {
      key: 'createdAt',
      name: 'Creation Time',
      selected: true,
      sort: true,
      renderHtml: expect.any(Function),
    },
    {
      key: 'transactionId',
      name: 'Transaction ID',
      selected: true,
      sort: false,
      renderHtml: expect.any(Function),
    },
    {
      key: 'paymentTransactionId',
      name: 'Payment Transaction ID',
      selected: true,
      sort: false,
      renderHtml: expect.any(Function),
    },
    {
      key: 'customerName',
      name: 'Customer Name',
      selected: true,
    },
    {
      key: 'amount',
      name: 'Amount',
      selected: true,
      sort: false,
      headerAlign: 'right',
      renderHtml: expect.any(Function),
    },
    // {
    //   key: 'debitAmount',
    //   name: 'Debit Amount',
    //   selected: true,
    //   sort: false,
    //   headerAlign: 'right',
    //   renderHtml: expect.any(Function),
    // },
    // {
    //   key: 'creditAmount',
    //   name: 'Credit Amount',
    //   selected: true,
    //   sort: false,
    //   headerAlign: 'right',
    //   renderHtml: expect.any(Function),
    // },
    {
      key: 'transactionCategory',
      name: 'Transaction Category',
      selected: true,
      sort: true,
    },
    {
      key: 'walletId',
      name: 'Wallet ID',
      selected: false,
    },
    {
      key: 'walletType',
      name: 'Wallet Type',
      selected: true,
    },
  ];

  const mockCurrency: Currency = {
    currencyCode: 'USD',
    displayName: 'US Dollar',
    fractionDigits: 2,
    numericCode: 840,
    symbol: '$',
  };

  const mockSearchForm = {
    basic: [
      {
        label: 'Transaction ID',
        name: 'ids',
        placeholder: 'Enter transaction id',
        type: 'text',
        defaultValue: '',
      },
      {
        label: 'Customer',
        name: 'customerId',
        placeholder: 'Choose customer',
        type: 'lazy-load-select',
        defaultValue: '',
        options: [],
        showSearch: true,
      },
      {
        label: 'Transaction Categories',
        name: 'transactionCategory',
        placeholder: 'Choose the transaction categories',
        type: 'select',
        defaultValue: [],
        isMultiple: true,
        options: [
          {
            label: 'DEPOSIT',
            value: 'DEPOSIT',
          },
          {
            label: 'WITHDRAW',
            value: 'WITHDRAW',
          },
          {
            label: 'PURCHASE',
            value: 'PURCHASE',
          },
          {
            label: 'TRANSFER',
            value: 'TRANSFER',
          },
          {
            label: 'REFUND',
            value: 'REFUND',
          },
          {
            label: 'EXPIRED',
            value: 'EXPIRED',
          },
        ],
      },
    ],
    advanced: [
      {
        label: 'Payment Transaction ID',
        name: 'paymentTransactionIds',
        placeholder: 'Enter payment transaction id',
        type: 'text',
        defaultValue: '',
      },
      {
        label: 'wallet.id',
        name: 'walletId',
        placeholder: 'wallet.id-placeholder',
        type: 'text',
        defaultValue: '',
      },
      {
        label: 'Transaction Type',
        name: 'transactionType',
        placeholder: 'Choose the transaction type',
        type: 'select',
        defaultValue: [],
        isMultiple: true,
        options: [
          {
            label: 'DEBIT',
            value: 'DEBIT',
          },
          {
            label: 'CREDIT',
            value: 'CREDIT',
          },
          {
            label: 'EXPIRED',
            value: 'EXPIRED',
          },
        ],
      },
    ],
  };

  /**
   * Mock params and transaction history and customer
   */
  const mockCustomerParams = {
    filter: {
      byName: '',
    },
    size: 10,
    page: 0,
  };

  const mockTransactionHistoryList: ITransactionHistoryResponse = {
    content: [
      {
        transactionId: '122577099144884224',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 1781,
        oldBalance: null,
        balance: null,
        createdAt: 1729224657810,
        description: null,
      },
      {
        transactionId: '122575934952894464',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 1110,
        oldBalance: null,
        balance: null,
        createdAt: 1729224380255,
        description: null,
      },
      {
        transactionId: '121543294095952896',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 500,
        oldBalance: null,
        balance: null,
        createdAt: 1728978179480,
        description: null,
      },
    ],
    totalElements: 3,
    totalPages: 1,
    page: 0,
    sort: ['createdAt'],
  };

  const mockCustomerData = {
    content: [
      {
        id: '121848682972648448',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'oo',
        lastName: '99',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: 1,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '121848682972648448',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: *************,
        updatedAt: *************,
      },
      {
        id: '121498338669302784',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'A1',
        lastName: 'A1',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: 1,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '121498338669302784',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: *************,
        updatedAt: *************,
      },
      {
        id: '121573514752758784',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'a1',
        lastName: 'a12',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: 1,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '121573514752758784',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: *************,
        updatedAt: *************,
      },
    ],
  };

  const mockCustomerDataReplace = {
    content: [
      {
        id: '124443448113890304',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'ddd',
        lastName: 'dd',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: 1,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '124443448113890304',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: *************,
        updatedAt: *************,
      },
      {
        id: '124398338324038656',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'Dustin',
        lastName: 'dd',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: 1,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '124398338324038656',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: *************,
        updatedAt: *************,
      },
      {
        id: '123771893836278784',
        ssoId: null,
        externalId: null,
        uniqueExternalId: null,
        firstName: 'Dustin',
        lastName: 'dhhe',
        email: null,
        userType: 'CUSTOMER',
        realmId: '7c6e99f7-c9d7-47b5-8a2e-b931ce3a35b7',
        avatar: null,
        userStatus: 'ACTIVE',
        phoneNumber: null,
        totalSubAccounts: null,
        totalSponsors: null,
        userGroup: {
          id: '121474488237757440',
          tenantId: '115697404415799296',
          path: '121474488237757440',
          groupName: 'Default',
          groupKey: 'DEFAULT_GROUP',
          parent: {
            id: null,
            tenantId: null,
            path: null,
            groupName: null,
            groupKey: null,
            parent: null,
            isDefaultGroup: false,
            description: null,
            createdAt: null,
            updatedAt: null,
          },
          isDefaultGroup: true,
          description:
            'By default, all customer of tenant would be added into this group. ',
          createdAt: null,
          updatedAt: null,
        },
        userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
        defaultDateFormat: 'dd/MM/yyyy',
        defaultTimeFormat: 'HH:mm',
        defaultTimezone: {
          zoneId: 'Etc/GMT+12',
          gtmOffset: 'GMT-12:00',
          displayName: '(GMT-12:00) Etc/GMT+12',
        },
        permissions: [
          {
            tenantId: '115697404415799296',
            userId: '123771893836278784',
            userRoleId: '115697409382864896',
            permissionStatus: 'ACTIVE',
          },
        ],
        createdAt: 1729509519064,
        updatedAt: 1729509519064,
      },
    ],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TransactionHistoryComponent],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of(mockQueryParams),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TransactionHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    storageService = TestBed.inject(StorageService);
    transactionService = TestBed.inject(TransactionHistoryService);
    customerService = TestBed.inject(CustomerManagementService);
    changeDetectionRef = component['_changeDetectorRef'];
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get tenant currency, init search form, and call transactionHistorySubscription function when create the component', () => {
    /**
     * Spy on the function called in the constructor
     */
    jest
      .spyOn(storageService, 'getTenantCurrency')
      .mockReturnValue(mockCurrency);
    const transactionSubscriptionSpy = jest.spyOn(
      TransactionHistoryComponent.prototype,
      'transactionHistorySubscription',
    );

    /**
     * Create another instance of component to test if the function in constructor is called
     */
    const testComponent = TestBed.createComponent(
      TransactionHistoryComponent,
    ).componentInstance;

    expect(testComponent.tenantCurrency).toEqual(mockCurrency);
    expect(testComponent.searchForm).toEqual(mockSearchForm);
    expect(transactionSubscriptionSpy).toHaveBeenCalled();
  });

  describe('Lifecycle Hook', () => {
    it('should call getAllTransactionHistory and getAllCustomers function when run the ngOnInit hook', () => {
      const getAllTransactionMock = jest.spyOn(
        component,
        'getAllTransactionHistories',
      );
      const getCustomerMock = jest.spyOn(component, 'getAllCustomers');

      component.ngOnInit();

      expect(component.queryParams).toEqual(mockQueryParams);
      expect(getAllTransactionMock).toHaveBeenCalled();
      expect(getCustomerMock).toHaveBeenCalled();
    });

    it('should initialize the table column and table action when run the ngAfterViewInit hook', () => {
      const detectChangeSpy = jest.spyOn(changeDetectionRef, 'detectChanges');
      component.ngAfterViewInit();

      expect(detectChangeSpy).toHaveBeenCalled();
      expect(component.displayedColumns).toEqual(mockTableColumns);
      expect(component.actions).toEqual([
        {
          name: 'View',
          type: 'event',
          callback: expect.any(Function),
        },
      ]);
    });

    it('should unsubscribe all service when run ngOnDestroy', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data fetching', () => {
    let customerIndex = -1;

    beforeEach(() => {
      transactionServiceMock = jest.spyOn(
        transactionService,
        'getAllTransactionHistories',
      );
      customerServiceMock = jest.spyOn(customerService, 'getCustomers');

      customerIndex = component.searchForm.basic.findIndex(
        (item: any) => item.name == 'customerId',
      );
    });

    it('should return customer data and transaction history after running the ngOnInit', () => {
      transactionServiceMock.mockRestore();
      customerServiceMock.mockRestore();

      component.ngOnInit();

      // Mock return value from http
      const requestTransaction = httpTestingController.match(
        `${API.TRANSACTION_HISTORY.MAIN}?${FuseUltils.objectToQueryString(mockQueryParams)}`,
      );
      requestTransaction[0].flush(mockTransactionHistoryList);

      const requestCustomer = httpTestingController.match(
        `${API.CUSTOMER.LIST}?${FuseUltils.objectToQueryString(mockCustomerParams)}`,
      );
      requestCustomer[0].flush(mockCustomerData);

      // Check return value
      const formatTransaction = mockTransactionHistoryList.content;
      expect(component.transactionHistories).toEqual(formatTransaction);
      expect(component.total).toEqual(mockTransactionHistoryList.totalElements);

      const formatCustomer = mockCustomerData.content.map((value) => ({
        label: `${value.firstName} ${value.lastName}`,
        value: value.id,
      }));

      expect(component.searchForm.basic[customerIndex].options).toEqual([
        {
          label: 'All',
          value: '',
        },
        ...formatCustomer,
      ]);
    });

    it('should run the getAllCustomers function and the getCustomers in Customer Service when we run the handleLazyLoadSelect function', () => {
      const getAllCustomersMock = jest.spyOn(component, 'getAllCustomers');

      component.handleLazyLoadSelect({
        value: {
          page: 1,
          search: 'Test',
        },
      });

      expect(getAllCustomersMock).toHaveBeenCalled();
      expect(customerServiceMock).toHaveBeenCalled();
      expect(component.customerAppend).toBe(true);
      expect(component.customerSearchConfig).toEqual({
        ...mockCustomerParams,
        page: 1,
        filter: {
          byName: 'Test',
        },
      });
    });

    it('should append the customer data when we call handleLazyLoadSelect with next page', () => {
      // Mock the first request to generate options for the customer option
      component.ngOnInit();

      let requestCustomer = httpTestingController.match(
        `${API.CUSTOMER.LIST}?${FuseUltils.objectToQueryString(mockCustomerParams)}`,
      );
      requestCustomer[0].flush(mockCustomerData);

      // Mock the handle lazy load select to update the option
      component.handleLazyLoadSelect({
        value: {
          page: 1,
        },
      });

      requestCustomer = httpTestingController.match(
        `${API.CUSTOMER.LIST}?${FuseUltils.objectToQueryString({ ...mockCustomerParams, page: 1 })}`,
      );
      requestCustomer[0].flush(mockCustomerDataReplace);

      // Check the customer options after update
      expect(component.customerAppend).toBe(true);

      const formatCustomer = [
        ...mockCustomerData.content,
        ...mockCustomerDataReplace.content,
      ].map((value) => ({
        label: `${value.firstName} ${value.lastName}`,
        value: value.id,
      }));
      formatCustomer.unshift({
        label: 'All',
        value: '',
      });

      expect(component.searchForm.basic[customerIndex].options).toEqual([
        ...formatCustomer,
      ]);
    });

    it('should replace the customer data when we call handleLazyLoadSelect with search value', () => {
      // Mock the first request to generate options for the customer option
      component.ngOnInit();

      let requestCustomer = httpTestingController.match(
        `${API.CUSTOMER.LIST}?${FuseUltils.objectToQueryString(mockCustomerParams)}`,
      );
      requestCustomer[0].flush(mockCustomerData);

      // Mock the handle lazy load select to update the option
      component.handleLazyLoadSelect({
        value: {
          page: 0,
          search: 'Test',
        },
      });

      requestCustomer = httpTestingController.match(
        `${API.CUSTOMER.LIST}?${FuseUltils.objectToQueryString({ ...mockCustomerParams, filter: { byName: 'Test' } })}`,
      );
      requestCustomer[0].flush(mockCustomerDataReplace);

      // Check the customer options after update
      expect(component.customerAppend).toBe(false);

      const formatCustomer = [...mockCustomerDataReplace.content].map(
        (value) => ({
          label: `${value.firstName} ${value.lastName}`,
          value: value.id,
        }),
      );
      formatCustomer.unshift({
        label: 'All',
        value: '',
      });

      expect(component.searchForm.basic[customerIndex].options).toEqual([
        ...formatCustomer,
      ]);
    });
  });
});
