<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex items-center gap-2">
      Healthier Choice
      <fuse-help-link
        [url]="
          '/product-management/healthier-choice/#healthier-choice-management'
        "
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.action) {
        <button
          class="btn-contained__primary__medium"
          (click)="onOpenAddHealthierChoiceDialog()"
        >
          Add Healthier Choice
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [bulkActions]="bulkActions"
        [template]="template"
        [sortDefault]="sortDefault"
        [pagination]="false"
        [rowClick]="handleViewHealthierChoiceDetail"
        [selectElement]="permissionList.action"
        (selectedElementChanged)="onChangeSelectedElement($event)"
        [searchForm]="searchForm"
      ></fuse-table-component>

      <ng-template #template let-column="header" let-element="data">
        @if (column.name === 'Symbol') {
          <ng-container>
            <img
              class="object-contain w-20 h-20 p-1"
              [src]="element.symbol.url"
              alt="Healthier Choice"
            />
          </ng-container>
        }
      </ng-template>
    </div>
  </div>
</div>
