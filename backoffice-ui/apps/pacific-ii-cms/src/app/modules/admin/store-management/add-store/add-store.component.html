<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Store Management</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ id ? 'Edit Store' : 'Add New Store' }}
        </div>
      </div>

      <div class="page-header__title">
        <button
          type="button"
          (click)="id ? gotoStoreDetail() : gotoStoreManagement()"
        >
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>{{ id ? 'Edit Store' : 'Add New Store' }}</div>
        <fuse-help-link
          [url]="
            id
              ? '/store-operation/storemange/#view-and-edit-store-details'
              : '/store-operation/storemange/#create-a-store'
          "
        ></fuse-help-link>
      </div>
    </div>

    <div class="flex gap-4">
      <button
        class="btn-outlined__primary__medium"
        (click)="id ? gotoStoreDetail() : gotoStoreManagement()"
      >
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:x-mark'"
        ></mat-icon>
        <span>Cancel</span>
      </button>
      <button
        [ngClass]="
          id
            ? 'btn-contained__success__medium'
            : 'btn-contained__primary__medium'
        "
        (click)="submitForm()"
        [disabled]="loading || storeForm.invalid || !storeForm.dirty"
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        } @else {
          @if (id) {
            <mat-icon
              class="w-5 h-5"
              [svgIcon]="'heroicons_outline:check'"
            ></mat-icon>
          }
        }
        <span>{{ id ? 'Save' : 'Create' }}</span>
      </button>
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <div class="box">
      <div class="box__header__title">General Information</div>

      <form
        class="flex flex-col gap-4"
        [formGroup]="storeForm"
        autocomplete="off"
      >
        <div class="grid grid-cols-2 gap-4">
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Store Name'"
            [name]="'name'"
            [placeholder]="'Enter store name'"
            [errorMessages]="errorMessages.name"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Email'"
            [name]="'email'"
            [placeholder]="'Enter email'"
            [prefixIcon]="'heroicons_outline:envelope'"
            [prefixIconColor]="'#343330'"
            [errorMessages]="errorMessages.email"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Phone'"
            [name]="'phoneNumber'"
            [placeholder]="'Enter phone'"
            [prefixIcon]="'heroicons_outline:phone'"
            [prefixIconColor]="'#343330'"
            [errorMessages]="errorMessages.phoneNumber"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Address Line 1'"
            [name]="'addressLine2'"
            [placeholder]="'Enter address line 2'"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Address Line 2'"
            [name]="'addressLine1'"
            [placeholder]="'Enter address line 1'"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'City'"
            [name]="'city'"
            [placeholder]="'Enter city'"
          />
          <fuse-select
            class="w-full"
            [form]="storeForm"
            [label]="'Country'"
            [name]="'country'"
            [options]="optionListCountry"
            [placeholder]="'Select country'"
            [optionLabel]="'countryName'"
            [optionValue]="'countryCode'"
            [showSearch]="true"
            [offlineSearch]="true"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Postal Code'"
            [name]="'postalCode'"
            [placeholder]="'Enter postal code'"
          />
          <fuse-input
            class="w-full"
            [form]="storeForm"
            [label]="'Opening Hour'"
            [name]="'workingHour'"
            [placeholder]="'Enter opening hour'"
          />
        </div>
      </form>
    </div>
  </div>
</div>
