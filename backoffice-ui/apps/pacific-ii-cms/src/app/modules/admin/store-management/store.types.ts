export interface IStoreList {
  content: Array<IStore>;
  totalElements: number;
  totalPages: number;
  page: number;
  sort: Array<string>;
}

export interface IStore {
  storeId: number;
  tenantId: number;
  name: string;
  email: string;
  phoneNumber: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  country: string;
  status: string;
  postalCode: string;
  workingHour: string;
  createdAt?: number;
}

export interface IStoreClosure {
  suspendType: string;
  note?: string;
}
