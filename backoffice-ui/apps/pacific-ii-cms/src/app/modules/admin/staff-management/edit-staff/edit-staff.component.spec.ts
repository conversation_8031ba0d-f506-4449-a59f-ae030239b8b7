import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { ToastrModule } from 'ngx-toastr';
import { of, Subject, throwError } from 'rxjs';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { IDataTable } from '../../../../types/utility.types';
import { StaffService } from '../staff-management.service';
import { IStaffAssignment, StaffInfo } from '../staff-management.types';
import { EditStaffComponent } from './edit-staff.component';
import { ROUTE } from '../../../../core/const';

describe('EditStaffComponent', () => {
  let component: EditStaffComponent;
  let fixture: ComponentFixture<EditStaffComponent>;

  const mockStaff: StaffInfo = {
    staffId: 118572059130307584,
    tenantId: 116024918514279424,
    cardId: '',
    staffCode: '1234567',
    name: 'Trang',
    email: '',
    type: 'CASHIER',
    status: 'ACTIVE',
    createdAt: 1728269781859,
  };

  const mockStaffAssignments: Array<IStaffAssignment> = [
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '116037144468648960',
    },
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '116741353926476800',
    },
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '117510486964629504',
    },
  ];

  const mockStoresResponse: IDataTable<any> = {
    content: [
      {
        storeId: '116037144468648960',
        tenantId: '116024918514279424',
        name: 'The Little Bean Rooftop',
        email: null,
        phoneNumber: '09123456789',
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1727665411111,
      },
      {
        storeId: '116741353926476800',
        tenantId: '116024918514279424',
        name: 'Red Bean',
        email: '<EMAIL>',
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'TERMINATED',
        postalCode: null,
        workingHour: null,
        createdAt: 1727833307728,
      },
      {
        storeId: '117510486964629504',
        tenantId: '116024918514279424',
        name: 'White Bean',
        email: null,
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1728016683354,
      },
    ],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: ['id,ASC'],
  };

  const mockFormValue = {
    name: mockStaff.name,
    email: mockStaff.email,
    phoneNumber: mockStaff.phoneNumber,
    type: mockStaff.type,
    cardId: mockStaff.cardId,
    staffCode: mockStaff.staffCode,
    storeIds: mockStaffAssignments.map((value) => value.storeId),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EditStaffComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {
                id: mockStaff.staffId,
              },
            }),
          },
        },
        {
          provide: MatDialog,
          useValue: {
            open: jest
              .fn()
              .mockReturnValue({ afterClosed: () => of('confirmed') }),
          },
        },
        {
          provide: StaffService,
          useValue: {
            getAssignStores: jest
              .fn()
              .mockReturnValue(of(mockStaffAssignments)),
            getStoreList: jest.fn().mockReturnValue(of(mockStoresResponse)),
            staffDetail$: new Subject<StaffInfo>(),
            getStaffDetail: function () {
              this.staffDetail$.next(mockStaff);
              return of(mockStaff);
            },
            editStaff: jest.fn().mockReturnValue(of(null)),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditStaffComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_router'], 'navigate');

    jest.spyOn(component['_ref'], 'markForCheck');

    jest.spyOn(component['_toast'], 'success');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockStaff.staffId);
  });

  describe('Lifecycle Hook', () => {
    it('should call handleGetStores and get the store detail when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetStores');
      jest.spyOn(component['_staffService'], 'getStaffDetail');

      component.ngOnInit();

      expect(component.handleGetStores).toHaveBeenCalled();
      expect(component['_staffService'].getStaffDetail).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get the staff detail and then get the list of assigned store when staffDetail$ return data', () => {
      const params = {
        filter: {
          storeIds: mockStaffAssignments.map((value) => value.storeId),
        },
        size: mockStaffAssignments.length,
      };

      component['_staffService'].getStaffDetail(mockStaff.staffId.toString());

      expect(component.staffDetail).toEqual(mockStaff);
      expect(component['_staffService'].getAssignStores).toHaveBeenCalledWith([
        mockStaff.staffId.toString(),
      ]);
      expect(component['_staffService'].getStoreList).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(params),
      );
      expect(component.staffStoreList).toEqual(mockStoresResponse.content);
    });

    it("should get the store list and update the store's options when run handleGetStores ", () => {
      jest
        .spyOn(component, 'combineListStore')
        .mockReturnValue([...mockStoresResponse.content]);
      component.optionListStore = [];

      component.handleGetStores();

      expect(component['_staffService'].getStoreList).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(component.storeParams),
      );
      expect(component.optionListStore).toEqual([
        ...mockStoresResponse.content,
      ]);
      expect(component['_ref'].markForCheck).toHaveBeenCalled();
    });
  });

  describe('Utility Function', () => {
    beforeEach(() => {
      jest.spyOn(component, 'handleGetStores');
      jest.spyOn(component, 'gotoStaffDetail');
    });

    it('should return not call the editStaff function if form is invalid when run submitForm', () => {
      component.staffForm.patchValue({
        name: null,
        storeIds: [],
      });
      component.submitForm();
      expect(component['_staffService'].editStaff).not.toHaveBeenCalled();
    });

    it('should call editStaff and handle success when run submitForm with valid data', () => {
      component.staffForm.patchValue(mockFormValue);
      const { email, staffCode, phoneNumber, ...data } = mockFormValue;

      component.submitForm();

      expect(component['_staffService'].editStaff).toHaveBeenCalledWith(
        mockStaff.staffId,
        {
          ...data,
        },
      );
      expect(component['_toast'].success).toHaveBeenCalledWith(
        'Edit staff successfully!',
      );
      expect(component.loading).toBe(false);
      expect(component.gotoStaffDetail).toHaveBeenCalled();
    });

    it('should handle error in submitForm', () => {
      jest
        .spyOn(component['_staffService'], 'editStaff')
        .mockReturnValueOnce(throwError(() => null));
      component.submitForm();
      expect(component.loading).toBe(false);
    });

    it('should navigate to staff detail page when run gotoStaffDetail', () => {
      component.gotoStaffDetail();
      expect(component['_router'].navigate).toHaveBeenCalledWith([
        `${ROUTE.STAFF.MAIN}/${ROUTE.STAFF.DETAIL}/${mockStaff.staffId}`,
      ]);
    });

    it('should update storeParams and call handleGetStores when run handleLazyLoadSelect', () => {
      const event = { page: 2, search: 'test' };
      component.handleLazyLoadSelect(event);
      expect(component.storeAppend).toBe(true);
      expect(component.storeParams.filter.name).toBe('test');
      expect(component.storeParams.page).toBe(2);
      expect(component.handleGetStores).toHaveBeenCalled();
    });

    it('should combine and deduplicate store lists', () => {
      const returnStores = mockStoresResponse.content;
      const stores = [returnStores[2], returnStores[1]];
      const storeList = [returnStores[0], returnStores[1]];

      const result = component.combineListStore(stores, storeList);
      expect(result).toEqual([...returnStores]);
    });

    it('should not modify store list if filter name is set', () => {
      const returnStores = mockStoresResponse.content;
      const stores = [returnStores[0]];
      const storeList = [returnStores[1]];

      const result = component.combineListStore(stores, storeList);
      expect(result).toEqual([returnStores[1], returnStores[0]]);
    });
  });
});
