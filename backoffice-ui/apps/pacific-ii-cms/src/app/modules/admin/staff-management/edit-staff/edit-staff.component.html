<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">
          Store Staff Management
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Staff Information</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="gotoStaffDetail()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Staff Information</div>
        <fuse-help-link
          [url]="'/store-operation/staffmanage/#view-staff-and-edit-details'"
        ></fuse-help-link>
      </div>
    </div>

    <div class="flex gap-4">
      <button class="btn-outlined__primary__medium" (click)="gotoStaffDetail()">
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:x-mark'"
        ></mat-icon>
        <span>Cancel</span>
      </button>
      <button
        class="btn-contained__success__medium"
        (click)="submitForm()"
        [disabled]="loading || staffForm.invalid || !staffForm.dirty"
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        } @else {
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:check'"
          ></mat-icon>
        }
        <span>Save</span>
      </button>
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <div class="box">
      <div class="box__header__title">General Information</div>

      <form
        class="flex flex-col gap-4"
        [formGroup]="staffForm"
        autocomplete="off"
      >
        <div class="grid grid-cols-2 gap-4">
          <fuse-input
            class="w-full"
            [form]="staffForm"
            [label]="'Name'"
            [name]="'name'"
            [placeholder]="'Enter name'"
            [errorMessages]="errorMessages.name"
          />
          <fuse-input
            class="w-full"
            [form]="staffForm"
            [label]="'Email'"
            [name]="'email'"
            [placeholder]="'Enter email'"
            [prefixIcon]="'heroicons_outline:envelope'"
            [prefixIconColor]="'#343330'"
          />
          <fuse-input
            class="w-full"
            [form]="staffForm"
            [label]="'Phone'"
            [name]="'phoneNumber'"
            [placeholder]="'Enter phone'"
            [prefixIcon]="'heroicons_outline:phone'"
            [prefixIconColor]="'#343330'"
          />
          <fuse-select
            class="w-full"
            [form]="staffForm"
            [label]="'Role'"
            [name]="'type'"
            [options]="optionListRole"
            [placeholder]="''"
            [optionLabel]="'label'"
            [optionValue]="'id'"
          />
          <fuse-input
            class="w-full"
            [form]="staffForm"
            [label]="'Card ID'"
            [name]="'cardId'"
            [placeholder]="''"
          />
          <fuse-input
            class="w-full"
            [form]="staffForm"
            [label]="'Staff Code'"
            [name]="'staffCode'"
            [placeholder]="''"
          />
          <fuse-lazy-load-select
            class="w-full"
            [form]="staffForm"
            [label]="'Store'"
            [name]="'storeIds'"
            [options]="optionListStore"
            [placeholder]="''"
            [optionLabel]="'name'"
            [optionValue]="'storeId'"
            [errorMessages]="errorMessages.storeId"
            [showSearch]="true"
            [multiple]="true"
            (searchValueChanged)="handleLazyLoadSelect($event)"
          />
        </div>
      </form>
    </div>
  </div>
</div>
