import {
  ChangeDetectorRef,
  Component,
  Inject,
  AfterViewInit,
} from '@angular/core';
import {
  FormGroup,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { FuseInputComponent } from '@fuse/components/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { StaffService } from '../staff-management.service';
import { ToastrService } from 'ngx-toastr';
import { REGEX } from 'apps/pacific-ii-cms/src/app/core/const';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-change-pincode',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FuseInputComponent,
    MatProgressSpinnerModule,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './change-pincode.component.html',
})
export class ChangePincodeComponent {
  pinCodeForm!: UntypedFormGroup;
  loading = false;
  errorMessages = {
    pinCode: {
      required: 'Please enter pin code!',
      maxlength: 'Maximum is 12 characters!',
      minlength: 'Minimum is 4 characters!',
      pattern: 'Pincode must be a positive numeric value!',
    },
    retypePinCode: {
      required: 'Please enter confirm pin code!',
      match: 'Pincode and Confirm Pincode do not match',
      maxlength: 'Maximum is 12 characters!',
      minlength: 'Minimum is 4 characters!',
      pattern: 'Pincode must be a positive numeric value!',
    },
  };

  constructor(
    private _formBuilder: UntypedFormBuilder,
    private _dialogRef: MatDialogRef<any>,
    private _staffService: StaffService,
    private _toast: ToastrService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  submitForm() {
    for (const i in this.pinCodeForm.controls) {
      this.pinCodeForm.controls[i].markAsTouched();
      this.pinCodeForm.controls[i].updateValueAndValidity();
    }

    if (this.pinCodeForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const { retypePinCode, ...data } = this.pinCodeForm.value;
    this._staffService.resetPinCode(this.data.staffId, data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Change Pincode successfully!');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  private initForm() {
    this.pinCodeForm = this._formBuilder.group(
      {
        pinCode: [
          '',
          [
            Validators.required,
            Validators.minLength(4),
            Validators.maxLength(12),
            Validators.pattern(REGEX.NONNEGATIVE_INTEGER),
          ],
        ],
        retypePinCode: [
          '',
          [
            Validators.required,
            Validators.minLength(4),
            Validators.maxLength(12),
            Validators.pattern(REGEX.NONNEGATIVE_INTEGER),
          ],
        ],
      },
      { validator: this.pinCodeMatchValidator() },
    );
  }

  private pinCodeMatchValidator(): ValidationErrors {
    return (formGroup: FormGroup) => {
      const pinCode = formGroup.get('pinCode')?.value;
      const retypePinCode = formGroup.get('retypePinCode')?.value;

      // Check if pinCode matches retype
      if (pinCode !== retypePinCode) {
        formGroup.get('retypePinCode')?.setErrors({ match: true });
      } else {
        formGroup.get('retypePinCode')?.setErrors(null);
      }
      return null;
    };
  }
}
