import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatSpinner } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseUltils } from '@fuse/ultils';
import { TranslocoPipe, TranslocoService } from '@jsverse/transloco';
import {
  REGEX,
  ROLE_MODULES,
  ROLE_SCOPES,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { UserPermissionService } from 'apps/pacific-ii-cms/src/app/core/user/user-permission.service';
import { ToastrService } from 'ngx-toastr';
import { pairwise, startWith, Subject, takeUntil } from 'rxjs';
import { Currency } from '../../tenant-management/tenant.types';
import { WalletManagementService } from '../wallet-management.service';
import { IWalletSpendingLimit } from '../wallet-management.types';
import { Utils } from '../../../../core/utils/utils';

@Component({
  selector: 'app-spending-limit',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    FuseInputComponent,
    MatSpinner,
    TranslocoPipe,
  ],
  templateUrl: './spending-limit.component.html',
})
export class SpendingLimitComponent implements OnInit, OnDestroy {
  @Input() id!: string;

  walletSpendingLimits: Array<IWalletSpendingLimit> = [];
  spendingLimitForm!: FormGroup;
  loading = false;
  tenantCurrency!: Currency;

  permissionList = {
    update: false,
  };

  errorMessages = {
    daily: {
      pattern: 'wallet.daily-limit.message-pattern',
      min: 'wallet.daily-limit.message-min',
      maxWeekly: 'wallet.daily-limit.message-max-weekly',
      maxMonthly: 'wallet.daily-limit.message-max-monthly',
    },
    weekly: {
      pattern: 'wallet.weekly-limit.message-pattern',
      min: 'wallet.weekly-limit.message-min',
      maxMonthly: 'wallet.weekly-limit.message-max',
    },
    monthly: {
      pattern: 'wallet.monthly-limit.message-pattern',
      min: 'wallet.monthly-limit.message-min',
    },
  };
  private _unsubscribeAll = new Subject<any>();

  constructor(
    private _formBuilder: FormBuilder,
    private _toast: ToastrService,
    private _walletService: WalletManagementService,
    private _storageService: StorageService,
    private _translocoService: TranslocoService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this._translocoService.langChanges$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.updateErrorMessages();
      });

    this.initFormGroup();
    this.getPermission();
  }

  ngOnInit(): void {
    this.getSpendingLimits();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  submitForm() {
    for (const key in this.spendingLimitForm.controls) {
      this.spendingLimitForm.controls[key].markAsTouched();
      this.spendingLimitForm.controls[key].markAsDirty();
      this.spendingLimitForm.controls[key].updateValueAndValidity();
    }

    if (this.spendingLimitForm.invalid) return;

    this.loading = true;
    const typeMapping: any = {
      daily: 'DAY',
      weekly: 'WEEK',
      monthly: 'MONTH',
    };

    const items = Object.keys(this.spendingLimitForm.value).map((key) => {
      return {
        currency: this.tenantCurrency.currencyCode,
        type: typeMapping[key],
        spendingLimit: Number(this.spendingLimitForm.value[key])
          ? FuseUltils.truncateDecimalCurrency(
              Number(this.spendingLimitForm.value[key]),
              this.tenantCurrency.fractionDigits,
            )
          : 0,
      };
    });

    this._walletService
      .updateSpendingLimits(this.id, { items })
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe({
        next: (data) => {
          this.loading = false;
          this._toast.success(
            this._translocoService.translate(
              'wallet.spending-limit.update-success',
            ),
          );
          this.updateFormData(data.content);
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  getSpendingLimits() {
    this._walletService
      .getAllSpendingLimit(this.id)
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((data) => {
        this.updateFormData(data.content);
      });
  }

  updateFormData(data: IWalletSpendingLimit[]): void {
    this.walletSpendingLimits = data;
    const typeMapping: any = {
      DAY: 'daily',
      WEEK: 'weekly',
      MONTH: 'monthly',
    };
    const formatValue = data.reduce((prev, current) => {
      const value = {
        [typeMapping[current.type]]:
          FuseUltils.formatCurrency(
            current.spendingLimit,
            this.tenantCurrency.fractionDigits,
          ) || null,
      };
      return { ...prev, ...value };
    }, {});

    this.spendingLimitForm.reset();
    this.spendingLimitForm.patchValue(formatValue);
  }

  updateErrorMessages(): void {
    const fractionDigits = this.tenantCurrency.fractionDigits;
    if (!Number(fractionDigits)) {
      return;
    }

    this.errorMessages = {
      ...this.errorMessages,
      daily: {
        ...this.errorMessages.daily,
        pattern: this._translocoService.translate(
          'wallet.daily-limit.message-pattern-2',
          { number: fractionDigits },
        ),
      },
      weekly: {
        ...this.errorMessages.weekly,
        pattern: this._translocoService.translate(
          'wallet.weekly-limit.message-pattern-2',
          { number: fractionDigits },
        ),
      },
      monthly: {
        ...this.errorMessages.monthly,
        pattern: this._translocoService.translate(
          'wallet.monthly-limit.message-pattern-2',
          { number: fractionDigits },
        ),
      },
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.CONFIGURATION_MGMT_WALLET_SETTING,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
        };

        if (!this.permissionList.update) {
          this.spendingLimitForm.disable();
        }
      });
  }

  private initFormGroup() {
    this.spendingLimitForm = this._formBuilder.group({
      daily: [
        null,
        [
          Validators.pattern(
            this.tenantCurrency.fractionDigits
              ? REGEX.PRICE.replace(
                  'n',
                  this.tenantCurrency.fractionDigits.toString(),
                )
              : REGEX.INTEGER,
          ),
          Utils.naturalNumberValidators,
          checkSpendingLimitValidator('weekly'),
          checkSpendingLimitValidator('monthly'),
        ],
      ],
      weekly: [
        null,
        [
          Validators.pattern(
            this.tenantCurrency.fractionDigits
              ? REGEX.PRICE.replace(
                  'n',
                  this.tenantCurrency.fractionDigits.toString(),
                )
              : REGEX.INTEGER,
          ),
          Utils.naturalNumberValidators,
          checkSpendingLimitValidator('monthly'),
        ],
      ],
      monthly: [
        null,
        [
          Validators.pattern(
            this.tenantCurrency.fractionDigits
              ? REGEX.PRICE.replace(
                  'n',
                  this.tenantCurrency.fractionDigits.toString(),
                )
              : REGEX.INTEGER,
          ),
          Utils.naturalNumberValidators,
        ],
      ],
    });

    this.spendingLimitForm.valueChanges
      .pipe(
        takeUntil(this._unsubscribeAll),
        startWith({
          daily: null,
          weekly: null,
          monthly: null,
        }),
        pairwise(),
      )
      .subscribe(([oldValue, currentValue]) => {
        let check = false;
        for (const key in this.spendingLimitForm.controls) {
          if (oldValue[key] !== currentValue[key]) {
            check = true;
            break;
          }
        }

        if (check) {
          for (const key in this.spendingLimitForm.controls) {
            this.spendingLimitForm.controls[key].updateValueAndValidity();
          }
        }
      });
  }
}

const checkSpendingLimitValidator = (comparatorName: string): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value == null || control.value === '') {
      return null;
    }

    const parent = control.parent;
    const currentValue = control.value;

    if (
      parent?.value[comparatorName] == null ||
      parent?.value[comparatorName] === ''
    ) {
      return null;
    }

    if (Number(currentValue) > Number(parent.value[comparatorName])) {
      if (comparatorName === 'weekly') {
        return { maxWeekly: true };
      }

      return { maxMonthly: true };
    }

    return null;
  };
};
