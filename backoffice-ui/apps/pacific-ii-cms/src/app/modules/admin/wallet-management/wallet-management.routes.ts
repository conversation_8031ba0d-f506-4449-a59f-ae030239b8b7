import { Routes } from '@angular/router';
import { ROUTE } from '../../../core/const';
import { WalletInformationComponent } from './wallet-information/wallet-information.component';
import { WalletManagementComponent } from './wallet-management.component';

export default [
  {
    path: ROUTE.WALLET_MANAGEMENT.LIST,
    component: WalletManagementComponent,
  },
  {
    path: ROUTE.WALLET_MANAGEMENT.DETAIL + '/:id',
    component: WalletInformationComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.WALLET_MANAGEMENT.LIST },
] as Routes;