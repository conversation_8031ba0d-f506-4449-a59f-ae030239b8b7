import {
  AfterContentChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormGroup,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { FuseInputComponent } from '@fuse/components/input';
import { MatIconModule } from '@angular/material/icon';
import {
  REGEX,
  USER_ROLE,
  USER_TYPE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { TenantManagementService } from '../../tenant-management/tenant-management.service';
import {
  combineLatest,
  forkJoin,
  of,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { UserManagementService } from '../user-management.service';
import { ToastrService } from 'ngx-toastr';
import { FuseLazyLoadSelectComponent } from '@fuse/components/lazy-load-select/lazy-load-select.component';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { IUser } from '../../../../core/user/user.types';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { UserService } from '../../../../core/user/user.service';
import { findTenantOwnerRole } from '../user.types';

@Component({
  selector: 'app-invite-user',
  standalone: true,
  imports: [
    MatIconModule,
    FuseInputComponent,
    ReactiveFormsModule,
    MatButtonModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    FuseLazyLoadSelectComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './invite-user.component.html',
})
export class InviteUserComponent
  implements AfterContentChecked, OnInit, OnDestroy
{
  tenantId!: string;
  inviteForm!: UntypedFormGroup;
  invitedUsers: Array<any> = [];
  optionListUserRole: Array<any> = [];
  loading = false;
  subdomainStatusList = [
    {
      icon: 'heroicons_solid:check-circle',
      color: '#c4cdd5',
    },
    {
      icon: 'heroicons_solid:check-circle',
      color: '#36b37e',
    },
    {
      icon: 'heroicons_solid:x-circle',
      color: '#d36262',
    },
  ];
  subdomainStatus: any = 0;
  errorMessages = {
    email: {
      required: 'Please enter email!',
      unavailable: 'This email is not available.',
      pattern: 'Invalid email format!',
    },
    userRoleId: {
      required: 'Please select user role!',
    },
  };

  defaultRoleParams: any = {
    'filter.excludeExternalIds': USER_ROLE.CUSTOMER,
    'filter.byRoleTitle': '',
    page: 0,
    size: 10,
  };
  roleParams: Array<any> = [];
  roleAppend: Array<boolean> = [];
  defaultRoles: Array<any> = [];
  userMode = '';

  accountDetail!: IUser;
  userTenantOwner!: IUser;

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  @Output() submitEmitter: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    private _formBuilder: UntypedFormBuilder,
    private _storageService: StorageService,
    private _tenantService: TenantManagementService,
    private _userService: UserManagementService,
    private _toast: ToastrService,
    private _accountService: UserService,
    private _changeDetectorRef: ChangeDetectorRef,
  ) {
    this.initForm();
    this.getTenantId();
    this.userMode = this._storageService.getUserMode();
    this.defaultRoleParams['filter.byTenantId'] = this.tenantId;
    this.defaultRoleParams.size = this.userMode === 'tenant' ? 11 : 10;
  }

  ngOnInit(): void {
    this._accountService.user$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((user: IUser) => {
        this.accountDetail = user;
      });

    this.addNewUser();
    this.handleGetData();
  }

  ngAfterContentChecked() {
    this._changeDetectorRef.detectChanges();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleLazyLoadSelect(event: any, index: number) {
    this.roleAppend[index] = event.page > this.roleParams[index].page;
    this.roleParams[index]['filter.byRoleTitle'] = event.search;
    this.roleParams[index].page = event.page;
    this.roleParams[index].size =
      event.page === 0 && this.userMode === 'tenant' ? 11 : 10;

    this.handleGetData(index);
  }

  handleGetData(index?: number): void {
    const params =
      index != null ? this.roleParams[index] : this.defaultRoleParams;

    this._tenantService
      .getUserRole(params)
      .pipe(
        takeUntil(this._unsubscribeAll),
        switchMap((userRoleListData) => {
          const roleTenantOwner = findTenantOwnerRole(userRoleListData.content);

          return combineLatest([
            of(userRoleListData.content),
            roleTenantOwner && !this.userTenantOwner
              ? this._userService.getUsers(
                  FuseUltils.objectToQueryString({
                    filter: { byRoleIds: roleTenantOwner?.id },
                  }),
                )
              : of(null),
          ]);
        }),
        tap(([userRoleList, userOwnerListData]) => {
          this.updateOptionListUserRole(userRoleList, userOwnerListData, index);
        }),
      )
      .subscribe();
  }

  updateOptionListUserRole(
    userRoleList: any,
    userOwnerListData: any,
    index?: number,
  ) {
    // Process the roles based on tenant owner conditions
    const processedRoles = this.processRoles(userRoleList, userOwnerListData);

    if (index == null) {
      this.handleDefaultRolesUpdate(processedRoles);
    } else {
      this.handleIndexedRolesUpdate(processedRoles, index);
    }

    this.optionListUserRole = this.optionListUserRole.map((roles) => {
      return FuseUltils.removeDuplicateItems(roles, 'id');
    });
    this._changeDetectorRef.markForCheck();
  }

  processRoles(roles: any[], userOwnerListData: any): any[] {
    const shouldHideTenantOwner =
      (userOwnerListData && userOwnerListData.content.length > 0) ||
      this.accountDetail?.userType !== USER_TYPE.SYSTEM_ADMIN;

    if (shouldHideTenantOwner) {
      // Mark tenant owner roles as hidden
      roles.forEach((item: any) => {
        item.hidden = item.externalId === USER_ROLE.TENANT_OWNER;
      });

      // Sort to put tenant owner roles at the end
      return [
        ...roles.filter(
          (item: any) => item.externalId !== USER_ROLE.TENANT_OWNER,
        ),
        ...roles.filter(
          (item: any) => item.externalId === USER_ROLE.TENANT_OWNER,
        ),
      ];
    }

    return roles;
  }

  handleDefaultRolesUpdate(roles: any[]): void {
    // Update the first form control with the first role
    this.inviteArray.controls[0].patchValue({
      userRoleId: roles[0]?.id,
    });

    // Update the default roles
    this.defaultRoles = [...roles];

    // Initialize option lists for all items
    this.optionListUserRole = this.optionListUserRole.map(() => [...roles]);
  }

  handleIndexedRolesUpdate(roles: any[], index: number): void {
    this.optionListUserRole[index] = this.roleAppend[index]
      ? [...this.optionListUserRole[index], ...roles]
      : [...roles];
  }

  get inviteArray(): UntypedFormArray {
    return this.inviteForm.get('users') as UntypedFormArray;
  }

  getInviteFormGroup(user: any) {
    return user as FormGroup;
  }

  addNewUser(row?: any): void {
    const defaultUserRoleId =
      this.defaultRoles.length > 0 ? this.defaultRoles[0].id : null;
    const user = this._formBuilder.group({
      email: [
        row?.email ?? null,
        [Validators.required, Validators.pattern(REGEX.EMAIL)],
      ],
      userRoleId: [row?.userRoleId ?? defaultUserRoleId, [Validators.required]],
      tenantId: [this.tenantId ?? null],
      userType: [
        this.userMode == 'admin'
          ? USER_TYPE.SYSTEM_ADMIN
          : USER_TYPE.BACK_OFFICE,
      ],
    }) as FormGroup;

    // if (this.userMode == 'tenant') {
    //   user.get('userRoleId')?.disable();
    // }
    this.inviteArray.push(user);
    this.roleAppend.push(false);
    this.roleParams.push({ ...this.defaultRoleParams });
    this.optionListUserRole.push([...this.defaultRoles]);
  }

  removeUser(index: number) {
    this.inviteArray.removeAt(index);
    this.roleAppend.splice(index, 1);
    this.optionListUserRole.splice(index, 1);
    this.roleParams.splice(index, 1);
  }

  onInviteUser(): void {
    for (const i in this.inviteForm.controls) {
      this.inviteForm.controls[i].markAsTouched();
      this.inviteForm.controls[i].updateValueAndValidity();
    }

    for (const i of this.inviteArray.controls) {
      i.get('email')?.markAsTouched();
      i.get('userRoleId')?.markAsTouched();
      i.get('email')?.updateValueAndValidity();
      i.get('userRoleId')?.updateValueAndValidity();
    }

    if (this.inviteArray.invalid) return;

    this.loading = true;
    const listUser = [];
    for (let i = 0; i < this.inviteArray.length; i++) {
      listUser.push({
        ...this.inviteArray.controls[i].value,
        tenantId: this.tenantId,
        userRoleId: this.inviteArray.controls[i].get('userRoleId')?.value,
        firstName: this.inviteArray.controls[i].get('email')?.value,
      });
    }
    this.handleInviteUsers(listUser);
  }

  handleInviteUsers(listUser: any): void {
    forkJoin(
      listUser.map((user: any) => this._userService.addUser(user)),
    ).subscribe({
      next: () => {
        this.loading = false;
        this.submitEmitter.emit();
        this._toast.success(
          `Invite ${this.inviteArray.length} User${this.inviteArray.length > 1 ? 's' : ''} successfully`,
        );
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  checkEmailDuplication(): boolean {
    const inviteEmail = this.inviteArray.value.map((item: any) => item.email);
    const emailList = [...inviteEmail];

    return new Set(emailList).size !== emailList.length;
  }

  checkRoleTenantOwnerDuplication(): boolean {
    const roleOwner = this.optionListUserRole[0].find(
      (item: any) => item.externalId == USER_ROLE.TENANT_OWNER,
    );

    const countRoleOwner = this.inviteArray.value.filter(
      (item: any) => item.userRoleId == roleOwner?.id,
    ).length;
    return countRoleOwner > 1 ? true : false;
  }

  private initForm() {
    this.inviteForm = this._formBuilder.group({
      users: this._formBuilder.array([]),
    });
  }

  private getTenantId(): void {
    this.tenantId = this._storageService.getTenantId();
  }
}
