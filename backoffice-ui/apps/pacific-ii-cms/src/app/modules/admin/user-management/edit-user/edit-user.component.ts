import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from '../../../../core/utils/utils';
import { concatMap, Subject, takeUntil } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { StorageService } from '../../../../core/services/storage.service';
import { UserManagementService } from '../user-management.service';
import { AuthorizationService } from '../../../../services/authorization.service';
import { UserInfo } from '../user.types';
import { FuseUltils } from '@fuse/ultils';
import { REGEX, ROUTE } from '../../../../core/const';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { FuseInputComponent } from '@fuse/components/input';
import { MatDialog } from '@angular/material/dialog';
import { UtilityService } from '../../../../services/utility.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { UploadService } from '../../../../services/upload.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatProgressSpinnerModule,
    FuseInputComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './edit-user.component.html',
})
export class EditUserComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('viewAvatarTemplate') viewAvatarTemplate!: TemplateRef<any>;

  id!: string;
  userDetail!: UserInfo;
  userRoleList: Array<any> = [];

  detailForm!: UntypedFormGroup;
  loading = false;

  errorMessages = {
    phoneNumber: {
      required: 'Please enter phone number!',
      pattern: 'Invalid phone format!',
    },
  };

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _uploadService: UploadService,
    private _activatedRoute: ActivatedRoute,
    private _storageService: StorageService,
    private _utilityService: UtilityService,
    private _formBuilder: UntypedFormBuilder,
    private _userService: UserManagementService,
    private _authorizationService: AuthorizationService,
  ) {
    this.initForm();
    this.getUserId();
    this.initUserDetailSubscription();
  }

  ngOnInit(): void {
    this.handleGetData();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetData(): void {
    const tenantId = this._storageService.getTenantId();
    const params = {
      'filter.byTenantId': tenantId,
      page: 0,
      size: 500,
    };

    this._authorizationService
      .getUserRole(FuseUltils.objectToQueryString(params))
      .subscribe((data) => {
        this.userRoleList = data.content;
        this.handleGetUserDetail();
      });
  }

  handleGetUserDetail(): void {
    this._userService.getUserDetail(this.id).subscribe();
  }

  submitForm(): void {
    for (const i in this.detailForm.controls) {
      this.detailForm.controls[i].markAsTouched();
      this.detailForm.controls[i].updateValueAndValidity();
    }

    if (this.detailForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    if (this.detailForm.value.imageFile) {
      this.handleGetUploadAvatarUrl();
    } else {
      const data = this.prepareUserDetailData(this.detailForm.value);
      this.handleUpdateUserInformation(data);
    }
  }

  handleUpdateUserInformation(data: UserInfo): void {
    this._userService.updateUser(data).subscribe({
      next: (response: UserInfo) => {
        this.loading = false;
        this.gotoUserDetail();
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  prepareUserDetailData(data: any): any {
    const detail = {
      ...this.userDetail,
      ...data,
    };

    delete detail.imageFile;
    delete detail.image;
    delete detail.avatar;
    delete detail.avatarUrl;
    delete detail.permissions;
    delete detail.userStatusColor;

    return detail;
  }

  onUploadAvatarFile($event: any): void {
    const file = $event.target.files[0];
    const reader = new FileReader();
    const imageFileType = ['image/png', 'image/jpeg', 'image/jpg'];

    if (file.size > 5242880) {
      this._toast.error(
        'The size limit for images is 5.0 MB. Reduce the file size and try again.',
      );
      this.resetUploadedAvatarField($event);
      return;
    }

    if (imageFileType.indexOf(file.type) == -1) {
      this._toast.error(
        'Please upload the following file types: .jpeg, jpg, .png.',
      );
      this.resetUploadedAvatarField($event);
      return;
    }

    if (file) {
      reader.onload = () => {
        this.detailForm.patchValue({
          imageFile: file,
          image: reader.result,
        });
      };
      reader.readAsDataURL(file);
    }

    this.detailForm.markAsDirty();
    this.fileInput.nativeElement.value = '';
  }

  handleGetUploadAvatarUrl(): void {
    const data = { ...this.detailForm.value };
    const params = {
      fileName: data.imageFile.name,
    };

    this._utilityService
      .getUploadUrl(params)
      .pipe(
        concatMap((urlData: any) => {
          data.avatarPath = urlData.filePath;
          return this._uploadService.uploadFile(
            urlData.presignedUrl,
            data.imageFile,
          );
        }),
      )
      .subscribe({
        next: () => {
          this.loading = false;
          const userInfo = this.prepareUserDetailData(data);
          this.handleUpdateUserInformation(userInfo);
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  resetUploadedAvatarField($event: any): void {
    $event.target.value = null;
    this.detailForm.patchValue({
      imageFile: null,
      image: null,
    });
  }

  viewAvatar(): void {
    this._dialog.open(this.viewAvatarTemplate, {
      width: '472px',
    });
  }

  removeUploadedAvatar() {
    this.detailForm.patchValue({
      avatarPath: null,
      avatarUrl: null,
      imageFile: null,
      image: null,
    });
    this.detailForm.markAsDirty();
  }

  gotoUserDetail(): void {
    this._router.navigate([
      `${ROUTE.USER.MAIN}/${ROUTE.USER.DETAIL}/${this.id}`,
    ]);
  }

  onResendInvitation(): void {
    this._userService.resendInvitation(this.userDetail.id).subscribe(() => {
      this._toast.success('Resend invitation success!');
    });
  }

  getStatusColor(status: string): string {
    return Utils.getStatusColor(status);
  }

  private initUserDetailSubscription(): void {
    this._userService.userDetail$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((userDetail: UserInfo) => {
        const tenantId = this._storageService.getTenantId();
        this.detailForm.patchValue({
          ...userDetail,
          avatarPath: userDetail?.avatar?.path,
          avatarUrl: userDetail?.avatar?.url,
        });

        userDetail.userStatusColor = this.getStatusColor(userDetail.userStatus);
        userDetail.updatePermissions = [...userDetail.permissions];
        if (userDetail.permissions && userDetail.permissions.length > 0) {
          userDetail.permissions = userDetail.permissions
            .filter((item: any) => item.tenantId == tenantId)
            .map((item: any) => {
              const role = this.userRoleList.find(
                (role: any) => role.id == item.userRoleId,
              );

              return {
                ...item,
                userRole: role?.roleTitle,
                permissionStatusColor: this.getStatusColor(
                  item.permissionStatus,
                ),
              };
            });
        }

        this.userDetail = { ...userDetail };
      });
  }

  private getUserId(): void {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }

  private initForm() {
    this.detailForm = this._formBuilder.group({
      firstName: [{ value: null, disabled: true }],
      lastName: [{ value: null, disabled: true }],
      ssoId: [{ value: null, disabled: true }],
      email: [{ value: null, disabled: true }],
      phoneNumber: [null, [Validators.pattern(REGEX.PHONE_NUMBER)]],
      image: [null],
      imageFile: [null],
      avatarPath: [null],
      avatarUrl: [null],
    });
  }
}
