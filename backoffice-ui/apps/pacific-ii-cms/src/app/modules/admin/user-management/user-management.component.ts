import {
  After<PERSON>ontentChecked,
  ChangeDete<PERSON><PERSON><PERSON>,
  <PERSON>mponent,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { UserManagementService } from './user-management.service';
import { Subject, switchMap, takeUntil, tap } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButton } from '@angular/material/button';
import { InviteUserComponent } from './invite-user/invite-user.component';
import { MatIconModule } from '@angular/material/icon';
import { FuseModalDialogService } from '@fuse/services/modal-dialog';
import { FuseUltils } from '@fuse/ultils';
import { StorageService } from '../../../core/services/storage.service';
import {
  DATE_TIME_FORMAT,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
  USER_ACTION,
  USER_STATUS,
  USER_TYPE,
} from '../../../core/const';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../core/user/user.service';
import { AuthorizationService } from '../../../services/authorization.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-user-management',
  standalone: true,
  templateUrl: './user-management.component.html',
  imports: [
    FuseTableComponent,
    MatButton,
    MatIconModule,
    InviteUserComponent,
    FuseHelpLinkComponent,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class UserManagementComponent
  implements OnInit, OnDestroy, AfterContentChecked
{
  profile!: any;
  queryParams!: any;
  userRoleList: Array<any> = [];

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;

  searchForm: any;
  actions: Array<IAction> = [];
  sortDefault: any = {
    field: 'lastName',
    direction: 'asc',
  };

  permissionList = {
    create: false,
    update: false,
    delete: false,
  };

  userMode = this._storageService.getUserMode();
  tenantId = this._storageService.getTenantId();

  roleParams: any = {
    filter: {
      byTenantId: '',
      byUserTypes: '',
    },
    page: 0,
    size: 10,
  };
  roleAppend = false;

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  @ViewChild('inviteUserTpl', { static: false }) inviteUserTpl: any;

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _toast: ToastrService,
    private ref: ChangeDetectorRef,
    private _userService: UserService,
    private _activatedRoute: ActivatedRoute,
    private _storageService: StorageService,
    private _fuseDialogService: FuseModalDialogService,
    private _authorizationService: AuthorizationService,
    private _confirmationService: FuseConfirmationService,
    private _userManagementService: UserManagementService,
    private _userPermissionService: UserPermissionService,
    private _changeDetectorRef: ChangeDetectorRef,
  ) {
    this.getPermission();
    this.handleGetUserRole();
    this.initTableColumn();
    this.initTableAction();
    this.searchForm = this.initSearchForm();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetUsers();
      });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  ngAfterContentChecked() {
    this.ref.detectChanges();
  }

  handleLazyLoadSelect(event: any) {
    this.roleAppend = event.value.page > this.roleParams.page;
    this.roleParams.filter.byRoleTitle = event.value.search;
    this.roleParams.page = event.value.page;

    this.handleGetUserRole();
  }

  handleGetUserRole(): void {
    const userMode = this._storageService.getUserMode();
    const tenantId = this._storageService.getTenantId();

    this.roleParams.filter.byTenantId = tenantId;
    this.roleParams.filter.byUserTypes =
      userMode == 'admin' ? USER_TYPE.SYSTEM_ADMIN : USER_TYPE.BACK_OFFICE;

    this._authorizationService
      .getUserRole(FuseUltils.objectToQueryString(this.roleParams))
      .subscribe((data) => {
        const roleServer = data.content
          .filter((item: any) => item.externalId != 'CUSTOMER')
          .map((item: any) => {
            return {
              ...item,
              label: item.roleTitle,
              value: item.id,
            };
          });

        this.userRoleList = this.roleAppend
          ? [...this.userRoleList, ...roleServer]
          : [{ label: 'All', value: '' }, ...roleServer];
        const updatedForm = { ...this.searchForm };
        updatedForm.basic[1].options = [...this.userRoleList];
        this.searchForm = { ...updatedForm };

        this.ref.markForCheck();
      });
  }

  handleGetDisplayedUserRoles(roleIds: Array<any>) {
    const params = {
      filter: {
        includeRoleIds: [...roleIds],
        byTenantId: this.tenantId,
      },
      page: 0,
      size: 10,
    };

    return this._authorizationService
      .getUserRole(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((res: any) => {
          if (this.displayedColumns) {
            this.displayedColumns[4] = {
              key: 'permissions',
              name: 'Assigned Role',
              selected: true,
              render: (value: any) => {
                const role = value.find(
                  (item: any) => item.tenantId === this.tenantId,
                );
                const displayedRole = res.content.find(
                  (item: any) => item.id == role.userRoleId,
                );
                return displayedRole
                  ? displayedRole.roleTitle
                  : `Unknown Role (${role.userRoleId})`;
              },
            };

            this.displayedColumns = [...this.displayedColumns];
            this._changeDetectorRef.detectChanges();
          }
        }),
      );
  }

  handleGetUsers(): void {
    const mappedData = this.getUserParams(this.queryParams);
    this._userManagementService
      .getUsers(FuseUltils.objectToQueryString(mappedData))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((users: any) => {
          this.dataSource = users.content;
          this.total = users.totalElements;
        }),
        switchMap((users: any) => {
          const roleIds = [
            ...new Set(
              users.content.map((value: any) => {
                const role = value.permissions.find(
                  (item: any) => item.tenantId === this.tenantId,
                );

                return role.userRoleId;
              }),
            ),
          ];

          return this.handleGetDisplayedUserRoles(roleIds);
        }),
      )
      .subscribe();
  }

  getUserParams(queryParams: any): any {
    const filter = {
      byTenantId: this.tenantId,
      byUserTypes:
        this.userMode == 'admin'
          ? USER_TYPE.SYSTEM_ADMIN
          : USER_TYPE.BACK_OFFICE,
      byName: queryParams['name'] || [],
      byRoleIds: queryParams['roleId'] || [],
      byUserStatuses:
        queryParams['statuses'] || this.searchForm.basic[2].defaultValue,
      byUserId: queryParams['id'] || '',
      bySsoId: queryParams['ssoId'] || '',
      byExternalId: queryParams['externalId'] || '',
      byPhoneNumber: queryParams['phoneNumber'] || '',
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  onInviteUserDialog() {
    const dialogRef = this._fuseDialogService.open(this.inviteUserTpl, {
      title: 'Activate Now',
      actions: {
        submit: {
          label: 'OK',
          class: 'btn-contained__warning__medium',
        },
        cancel: {
          label: 'Cancel',
          class: 'btn-text__inherit__medium',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleGetUsers();
      }
    });
  }

  handleViewUserDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.USER.MAIN}/${ROUTE.USER.DETAIL}/${element.id}`,
    ]);
  }

  onOpenActivateUserDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Activate User',
      message: 'Please confirm to activate this user.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Activate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateUserStatus(element, USER_STATUS.ACTIVE);
      }
    });
  }

  onOpenArchiveUserDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Archive User',
      message: 'Please confirm to archive this user.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Archive',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateUserStatus(element, USER_STATUS.ARCHIVED);
      }
    });
  }

  onOpenBlockUserDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Block User',
      message: 'Please confirm to block this user.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Block',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateUserStatus(element, USER_STATUS.BLOCKED);
      }
    });
  }

  handleUpdateUserStatus(user: any, status: string): void {
    const data = {
      ...user,
      userStatus: status,
      avatarPath: user?.avatar?.path || null,
      userGroupId: user?.userGroup?.id || null,
      updatePermissions: user.permissions,
    };

    let message = '';
    switch (status) {
      case USER_STATUS.ACTIVE:
        if (data?.schedulingDeletedAt) {
          data.schedulingDeletedAt = null;
        }
        message = 'Activate user success!';
        break;
      case USER_STATUS.ARCHIVED:
        message = 'Archive user success!';
        break;
      case USER_STATUS.BLOCKED:
        message = 'Block user success!';
        break;
    }

    this._userManagementService.updateUser(data).subscribe(() => {
      this._toast.success(message);
      this.handleGetUsers();
    });
  }

  onOpenDeleteUserDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete User',
      message: `Please confirm to delete this user. <br/>After confirming deletion, the user's status will be changed to "Archived" and the user will be automatically deleted by the system after a set period.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteUser(element);
      }
    });
  }

  onOpenRemoveUserDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Remove User',
      message: 'Please confirm to remove this user.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Remove',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleRemoveUserPermission(element);
      }
    });
  }

  handleDeleteUser(user: any): void {
    const data = {
      byUserId: user.id,
    };
    this._userManagementService.deleteUser(data).subscribe(() => {
      this._toast.success('User deletion schedule processed successfully!');
      this.handleGetUsers();
    });
  }

  handleRemoveUserPermission(user: any): void {
    const permissions = user.permissions.filter(
      (item: any) => item.tenantId !== this.tenantId,
    );

    const data = {
      ...user,
      avatarPath: user?.avatar?.path || null,
      userGroupId: user?.userGroup?.id || null,
      updatePermissions: permissions,
    };
    delete data.permissions;
    this._userManagementService.updateUser(data).subscribe(() => {
      this._toast.success('Remove user success!');
      this.handleGetUsers();
    });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this._userService.userProfile$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((user: any) => {
        this.profile = user;
      });

    this.displayedColumns = [
      {
        key: 'id',
        name: 'ID',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'firstName',
        name: 'First Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'lastName',
        name: 'Last Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      { key: 'email', name: 'Email', sort: true, selected: true },
      {
        key: 'permissions',
        name: 'Assigned Role',
        selected: true,
        render: (value: any) => {
          const role = value.find(
            (value: any) => value.tenantId === this.tenantId,
          );
          return role.userRoleId ? `Unknown Roles (${role.userRoleId})` : '_';
        },
      },
      {
        key: 'createdAt',
        name: 'Registration Date',
        sort: true,
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'schedulingDeletedAt',
        name: 'Deletion Date',
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'userStatus',
        name: 'Status',
        selected: true,
        renderHtml: (value: string) => {
          let chipColor = '';
          switch (value) {
            case 'CREATED':
              chipColor = 'bg-[#21B5D5] text-white';
              break;
            case 'ACTIVE':
              chipColor = 'bg-[#36B37E] text-white';
              break;
            case 'ARCHIVED':
              chipColor = 'bg-[#D0021B] text-white';
              break;
            case 'BLOCKED':
              chipColor = 'bg-[#FFAB00] text-black';
              break;
            default:
              chipColor = 'bg-[#21B5D5] text-white';
              break;
          }

          return `<div class="w-fit px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</div>`;
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewUserDetail(element),
      },
      {
        name: 'Activate',
        type: 'event',
        hidden: (element) =>
          (element.userStatus != USER_STATUS.ARCHIVED &&
            element.userStatus != USER_STATUS.BLOCKED) ||
          (element.userStatus == USER_STATUS.ARCHIVED &&
            element.userNonCompletedActions.includes(
              USER_ACTION.KEYCLOAK_REGISTRATION,
            )) ||
          (element.userStatus == USER_STATUS.BLOCKED &&
            element.userNonCompletedActions.includes(
              USER_ACTION.KEYCLOAK_REGISTRATION,
            )) ||
          element.id == this.profile.id ||
          !this.permissionList.update,
        callback: (element) => this.onOpenActivateUserDialog(element),
      },
      {
        name: 'Archive',
        type: 'event',
        hidden: (element) =>
          element.userStatus != USER_STATUS.ACTIVE ||
          element.id == this.profile.id ||
          !this.permissionList.update,
        callback: (element) => this.onOpenArchiveUserDialog(element),
      },
      {
        name: 'Block',
        type: 'event',
        hidden: (element) =>
          element.userStatus != USER_STATUS.ACTIVE ||
          element.id == this.profile.id ||
          !this.permissionList.update,
        callback: (element) => this.onOpenBlockUserDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: (element) => {
          const permissions = element.permissions.filter(
            (item: any) => item.tenantId !== this.tenantId,
          );

          return (
            element.id == this.profile.id ||
            !this.permissionList.delete ||
            permissions?.length ||
            !(
              element.userStatus !== USER_STATUS.ARCHIVED ||
              (element.userStatus == USER_STATUS.ARCHIVED &&
                !element?.schedulingDeletedAt)
            )
          );
        },
        callback: (element) => this.onOpenDeleteUserDialog(element),
      },
      {
        name: 'Remove',
        type: 'event',
        hidden: (element) => {
          const permissions = element.permissions.filter(
            (item: any) => item.tenantId !== this.tenantId,
          );

          return (
            element.id == this.profile.id ||
            !this.permissionList.update ||
            !permissions?.length
          );
        },
        callback: (element) => this.onOpenRemoveUserDialog(element),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Search by Name',
          name: 'name',
          placeholder: 'Search',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'Assigned Role',
          name: 'roleId',
          type: 'lazy-load-select',
          defaultValue: '',
          showSearch: true,
          options: [],
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: ['CREATED', 'ACTIVE', 'BLOCKED'],
          isMultiple: true,
          options: [
            { label: 'Created', value: 'CREATED' },
            { label: 'Active', value: 'ACTIVE' },
            {
              label: 'Archived',
              value: 'ARCHIVED',
            },
            { label: 'Blocked ', value: 'BLOCKED' },
          ],
        },
      ],
      advanced: [
        {
          label: 'User ID',
          name: 'id',
          placeholder: 'Enter user ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Keycloak ID',
          name: 'ssoId',
          placeholder: 'Enter keycloak ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'External User ID (Specific ID in Customer System)',
          name: 'externalId',
          placeholder: 'Enter external user ID',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Phone',
          name: 'phoneNumber',
          placeholder: 'Enter phone',
          type: 'text',
          defaultValue: '',
        },
        // {
        //   label: 'datetime',
        //   name: 'datetime',
        //   placeholder: 'datetime',
        //   type: 'datetime',
        //   defaultValue: '',
        // },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          create: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.USER_MGMT, scope: ROLE_SCOPES.ADD },
          ]),
          update: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.USER_MGMT, scope: ROLE_SCOPES.UPDATE },
          ]),
          delete: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.USER_MGMT, scope: ROLE_SCOPES.DELETE },
          ]),
        };
      });
  }
}
