import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseUltils } from '@fuse/ultils';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MealTimeManagementService } from '../meal-time-management.service';
import { IMealTime } from '../meal-time.types';
import { FuseDateTimeRangePickerComponent } from '@fuse/components/date-time-range-picker';
import { Utils } from '../../../../core/utils/utils';
import { DATE_TIME_FORMAT } from '../../../../core/const';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-add-meal-time',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseInputComponent,
    FuseDateTimeRangePickerComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './add-meal-time.component.html',
})
export class AddMealTimeComponent implements AfterViewInit {
  mealTimeForm!: UntypedFormGroup;
  loading = false;

  errorMessages = {
    name: {
      required: 'Please enter meal time name!',
      maxlength: 'Meal time name cannot exceed 80 characters!',
    },
    availableTime: [
      {
        required: 'Please select start time!',
      },
      {
        required: 'Please select end time!',
      },
    ],
  };

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _changeDetectorRef: ChangeDetectorRef,
    private _mealTimesService: MealTimeManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  ngAfterViewInit(): void {
    if (this.data) {
      this.updateFormData();
      this._changeDetectorRef.detectChanges();
    }
  }

  submitForm(): void {
    this.mealTimeForm.patchValue({
      ...FuseUltils.trimObjectValues({ ...this.mealTimeForm.value }),
    });

    for (const i in this.mealTimeForm.controls) {
      this.mealTimeForm.controls[i].markAsTouched();
      this.mealTimeForm.controls[i].updateValueAndValidity();
    }

    for (const i in this.availableTimeGroup.controls) {
      this.availableTimeGroup.controls[i].markAsTouched();
      this.availableTimeGroup.controls[i].updateValueAndValidity();
    }

    if (this.mealTimeForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const mealTimeData = this.prepareMealTimeData({
      ...this.mealTimeForm.value,
    });
    if (this.data) {
      this.handleUpdateMealTime(mealTimeData);
    } else {
      this.handleAddMealTime(mealTimeData);
    }
  }

  handleAddMealTime(mealTimeData: IMealTime): void {
    this._mealTimesService.addMealTime(mealTimeData).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Add meal time success!');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  handleUpdateMealTime(mealTimeData: IMealTime): void {
    this._mealTimesService
      .updateMealTime(this.data.id, mealTimeData)
      .subscribe({
        next: () => {
          this.loading = false;
          this._toast.success('Update meal time success!');
          this.onClose('save');
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  prepareMealTimeData(data: any): any {
    const detail = {
      name: data.name,
      startTime: !FuseUltils.isNullOrEmpty(data.availableTime.start)
        ? FuseUltils.tsToLocalTime(
            data.availableTime.start,
            DATE_TIME_FORMAT.TIME_ISO,
          )
        : null,
      endTime: !FuseUltils.isNullOrEmpty(data.availableTime.end)
        ? FuseUltils.tsToLocalTime(
            data.availableTime.end,
            DATE_TIME_FORMAT.TIME_ISO,
          )
        : null,
      color: '#ffffff',
    };

    return detail;
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  updateFormData(): void {
    this.mealTimeForm.patchValue({
      ...this.data,
      availableTime: {
        start: Utils.convertTimeToTimestamp(this.data.startTime),
        end: Utils.convertTimeToTimestamp(this.data.endTime),
      },
    });
    this.mealTimeForm.valueChanges.subscribe(() => {
      this.mealTimeForm.markAsDirty();
    });
  }

  get availableTimeGroup(): UntypedFormGroup {
    return this.mealTimeForm.get('availableTime') as UntypedFormGroup;
  }

  private initForm(): void {
    this.mealTimeForm = this._formBuilder.group({
      name: [null, [Validators.required, Validators.maxLength(80)]],
      availableTime: this._formBuilder.group({}),
      // color: [null],
    });
  }
}
