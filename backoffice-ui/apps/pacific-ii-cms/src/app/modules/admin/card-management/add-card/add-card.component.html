<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="web__h6 text-grey-900">
      {{ data && data.card ? 'Edit Card' : 'Add New Card' }}
    </div>

    <form class="flex flex-col gap-4" [formGroup]="cardForm">
      <fuse-input
        class="w-full"
        [form]="cardForm"
        [label]="'Card ID'"
        [name]="'cardId'"
        [placeholder]="'Enter card ID'"
        [errorMessages]="errorMessages.cardId"
      />
      <fuse-input
        class="w-full"
        [form]="cardForm"
        [label]="'Card Alias'"
        [name]="'cardAlias'"
        [placeholder]="'Enter card alias'"
        [errorMessages]="errorMessages.cardAlias"
      />
    </form>
  </div>

  <div class="flex items-start justify-end gap-2">
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      Cancel
    </button>
    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || cardForm.invalid || !cardForm.dirty"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      {{ data && data.card ? 'Save' : 'Add' }}
    </button>
  </div>
</div>
