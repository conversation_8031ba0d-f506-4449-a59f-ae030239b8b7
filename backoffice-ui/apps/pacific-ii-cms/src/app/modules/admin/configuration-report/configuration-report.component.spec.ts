import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ConfigurationReportComponent } from './configuration-report.component';
import { provideHttpClient } from '@angular/common/http';
import { ConfigurationDashboardService } from '../configuration-dashboard/configuration-dashboard.service';
import { of } from 'rxjs';
import { ReactiveFormsModule } from '@angular/forms';
import { FuseSelectComponent } from '@fuse/components/select';
import { MatButtonModule } from '@angular/material/button';
import { FuseSafePipe } from '@fuse/pipes/safe';
import { provideIcons } from '../../../core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideTranslate } from '../../../core/transloco/transloco.provider';

describe('ConfigurationReportComponent', () => {
  let component: ConfigurationReportComponent;
  let fixture: ComponentFixture<ConfigurationReportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ConfigurationReportComponent,
        ReactiveFormsModule,
        FuseSelectComponent,
        MatButtonModule,
        FuseSafePipe,
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ConfigurationDashboardService,
          useValue: {
            dashboardConfig$: of([]),
            getDashboardConfig: jest.fn().mockReturnValue(of([])),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ConfigurationReportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
