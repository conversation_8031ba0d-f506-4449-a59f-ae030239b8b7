import { provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import {
  API,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { UserService } from 'apps/pacific-ii-cms/src/app/core/user/user.service';
import { KeycloakService } from 'keycloak-angular';
import { of } from 'rxjs';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { TenantDetailComponent } from './tenant-detail.component';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('TenantDetailComponent', () => {
  let component: TenantDetailComponent;
  let fixture: ComponentFixture<TenantDetailComponent>;
  let httpTestingController: HttpTestingController;

  const mockTenantId = '135261622300581888';

  const mockTenantDetail = {
    tenantId: 116035193997042688,
    name: 'A1. Son Test 1 ',
    businessRegNo: 'B121',
    businessType: 'CORPORATE',
    status: 'UNDER_REVIEW',
    email: '<EMAIL>',
    phoneNumber: '12332213',
    contactRemarks: '',
    email2: '',
    phoneNumber2: '',
    contactRemarks2: '',
    email3: '',
    phoneNumber3: '',
    contactRemarks3: '',
    website: '',
    logo: {
      path: 'pacific:image/2024/9/30/png-transparent-avatar-user-computer-icons-software-developer-avatar-child-face-heroes.png',
      url: 'https://*************:31805/pacific/image/2024/9/30/png-transparent-avatar-user-computer-icons-software-developer-avatar-child-face-heroes.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241003T074903Z&X-Amz-SignedHeaders=host&X-Amz-Expires=300&X-Amz-Credential=pacific-readonly%2F20241003%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Signature=fff3d90fbd080235650e7f51229169666bfa5392617bfb5e1406ccd1c3b1e99b',
    },
    realmId: '9c21accf-aae2-449f-a06f-60fe05a6324a',
    addressLine1: '12A',
    addressLine2: '',
    city: 'Ho Chi Minh',
    country: 'VN',
    postalCode: '',
    createdAt: 1727664946080,
    updatedAt: 1727666798737,
    activatedAt: 0,
    settings: {
      defaultDomain: 'testing.com.pacific.styl.solutions.com',
      timeZone: {
        zoneId: 'Asia/Ho_Chi_Minh',
        gtmOffset: 'GMT+07:00',
        displayName: '(GMT+07:00) Asia/Ho_Chi_Minh',
      },
      currency: {
        displayName: 'Vietnamese Dong',
        numericCode: 704,
        currencyCode: 'VND',
        symbol: '₫',
        fractionDigits: 0,
      },
      dateFormat: 'dd-MM-yyyy',
      timeFormat: 'HH:mm:ss',
    },
  };

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.TENANT_MGMT}_${ROLE_SCOPES.UPDATE}`,
    },
  ];

  const mockUserData = {
    avatar: undefined,
    email: '<EMAIL>',
    id: 'test',
    name: '<EMAIL>',
    roles: [],
    tenantIds: ['0'],
    userId: '**********',
    userMode: 'admin',
    userType: 'SYSTEM_ADMIN',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TenantDetailComponent],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: { id: mockTenantId },
            }),
          },
        },
        {
          provide: KeycloakService,
          useValue: {},
        },
        {
          provide: UserService,
          useValue: {
            userMode: 'admin',
            userData: {
              ...mockUserData,
            },
            selectedTenantId: null,
            user: null,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TenantDetailComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);
    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService']['permissionList'] = mockPermissionList;

    jest.spyOn(component['_router'], 'navigate');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should get tenant id and assign it to tenantDetail when run ngOnInit', () => {
      component.ngOnInit();

      const req = httpTestingController.match(
        API.TENANT.DETAIL.replace('{id}', mockTenantId),
      );
      req[1].flush(mockTenantDetail);

      expect(component.tenantDetail).toEqual(mockTenantDetail);
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get the permission and update the permission list when run getPermission', () => {
      expect(component.permissionList).toEqual({
        update: false,
      });

      component['getPermissions']();

      expect(component.permissionList).toEqual({
        update: true,
      });
    });
  });

  describe('Utility Function', () => {
    it('should navigate to edit tenant page when run gotoEditTenant method', () => {
      component.id = mockTenantId;

      component.gotoEditTenant();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.TENANT.MAIN + '/' + ROUTE.TENANT.EDIT + '/' + mockTenantId,
      ]);
    });

    it('should navigate to tenant management page when run gotoTenantManagement method', () => {
      component.gotoTenantManagement();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.TENANT.MAIN,
      ]);
    });

    it('should change the value of user and selectedId in UserService when run handleLoginAsTenant', () => {
      expect(component['_userService'].selectedTenantId).toBe(null);
      expect(component['_userService'].user).toBe(null);
      component.id = mockTenantId;

      component.handleLoginAsTenant();

      expect(component['_userService'].selectedTenantId).toEqual(mockTenantId);
      expect(component['_userService'].user).toEqual({
        ...mockUserData,
        userMode: 'tenant',
      });
    });
  });
});
