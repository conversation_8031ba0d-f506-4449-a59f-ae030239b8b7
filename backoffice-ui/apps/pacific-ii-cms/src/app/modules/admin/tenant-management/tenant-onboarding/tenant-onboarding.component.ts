import { Component, OnDestroy, OnInit } from '@angular/core';
import { FuseStepComponent } from '@fuse/components/step';
import { AddTenantInformationComponent } from '../add-tenant-information/add-tenant-information.component';
import { AddTenantConfigurationsComponent } from '../add-tenant-configurations/add-tenant-configurations.component';
import { AddBusinessFeaturesComponent } from '../add-business-features/add-business-features.component';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { ROUTE } from '../../../../core/const';
import { InviteUserComponent } from '../invite-user/invite-user.component';
import { Subject, takeUntil } from 'rxjs';
import { UserService } from '../../../../core/user/user.service';
import { IUser } from '../../../../core/user/user.types';
import { TenantManagementService } from '../tenant-management.service';

@Component({
  selector: 'app-tenant-onboarding',
  standalone: true,
  imports: [MatIconModule, FuseStepComponent],
  templateUrl: './tenant-onboarding.component.html',
})
export class TenantOnboardingComponent implements OnInit, OnDestroy {
  user!: IUser;
  steps: any = [
    {
      label: 'General Information',
      component: AddTenantInformationComponent,
    },
    {
      label: 'Tenant Configurations',
      component: AddTenantConfigurationsComponent,
    },
    {
      label: 'Business Features',
      component: AddBusinessFeaturesComponent,
    },
  ];

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  constructor(
    private _router: Router,
    private _userService: UserService,
    private _tenantService: TenantManagementService,
  ) {}

  ngOnInit(): void {
    this._tenantService
      .getBusinessFeaturesList()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((data: any) => {
        if (!data || !data.content || data.content.length === 0) {
          const businessFeatureIndex = this.steps.findIndex(
            (value: any) => value.label === 'Business Features',
          );
          this.steps.splice(businessFeatureIndex, 1);
          this.steps[businessFeatureIndex - 1].data = {
            ...(this.steps[businessFeatureIndex - 1].data || {}),
            isFinalStep: true,
          };
        }
      });

    this._userService.user$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((user: IUser) => {
        this.user = user;

        this.steps = this.steps.map((item: any) => ({
          ...item,
          data: {
            ...(item.data || {}),
            userMode: user.userMode,
          },
        }));

        if (this.user.userMode == 'admin') {
          this.steps.push({
            label: 'Invite user',
            component: InviteUserComponent,
            data: {
              userMode: user.userMode,
            },
          });
        }
      });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  gotoTenantManagement(): void {
    this._router.navigate([ROUTE.TENANT.MAIN]);
  }
}
