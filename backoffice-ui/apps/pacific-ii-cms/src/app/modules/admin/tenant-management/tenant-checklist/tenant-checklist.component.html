<div class="flex flex-col gap-4">
  <div class="text-3xl font-bold leading-8 font-onest text-grey-800">
    Checklist
  </div>

  <!-- CHECKLIST -->
  <div class="flex flex-col p-2">
    @for (item of checklists; track item) {
      <div class="flex gap-2 py-1.5">
        <ng-container>
          @if (item.completed) {
            <mat-icon
              class="text-success-main"
              [svgIcon]="'heroicons_solid:check-circle'"
            ></mat-icon>
          } @else {
            <mat-icon
              class="text-error-main"
              [svgIcon]="'heroicons_solid:x-circle'"
            ></mat-icon>
          }
        </ng-container>

        <div class="flex gap-4">
          <div class="text-base font-normal leading-[22px] text-grey-800">
            {{ item.name }}
          </div>

          @if (item.allowedManualUpdate && permissionList.checklist_update) {
            <button
              class="text-base font-bold leading-6 text-center"
              [ngClass]="{
                'text-grey-600': item.completed,
                'text-primary': !item.completed,
              }"
              (click)="onMarkAsDone(item)"
            >
              {{ item.completed ? 'Un-done' : 'Mark as Done' }}
            </button>
          }
        </div>
      </div>
    }
  </div>

  <!-- ACTION -->
  @if (permissionList.activate || permissionList.checklist_update) {
    <div class="flex gap-1">
      @if (permissionList.activate) {
        <button
          class="btn-contained__primary__medium"
          [disabled]="disableActivate"
          (click)="onOpenActivateNowDialog()"
        >
          Activate Now
        </button>
      }

      @if (permissionList.checklist_update) {
        <button
          class="btn-text__inherit__medium"
          [disabled]="!disableActivate"
          (click)="onSyncChecklists()"
        >
          Sync
        </button>
      }
    </div>
  }
</div>
