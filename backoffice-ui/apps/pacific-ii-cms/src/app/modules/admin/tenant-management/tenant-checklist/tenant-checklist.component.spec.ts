import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TenantChecklistComponent } from './tenant-checklist.component';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ToastrModule } from 'ngx-toastr';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import {
  API,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { TenantChecklist } from '../tenant.types';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { of } from 'rxjs';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('TenantChecklistComponent', () => {
  let component: TenantChecklistComponent;
  let fixture: ComponentFixture<TenantChecklistComponent>;
  let httpTestingController: HttpTestingController;

  const mockTenantId = '135261622300581888';

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.TENANT_ONBOARD_MGMT}_${ROLE_SCOPES.ACTIVATE}`,
    },
    {
      externalId: `${ROLE_MODULES.TENANT_MGMT_CHECKLIST}_${ROLE_SCOPES.UPDATE}`,
    },
  ];

  const mockCheckListsDone: Array<TenantChecklist> = [
    {
      itemId: '1',
      tenantId: '142182303970343936',
      name: 'Keycloak is setup successfully',
      description: 'Keycloak is setup successfully',
      allowedManualUpdate: true,
      completed: true,
      updatedAt: *************,
    },
    {
      itemId: '2',
      tenantId: '142182303970343936',
      name: 'Domain is already pointed to Pacific II',
      description: 'Domain is already pointed to Pacific II',
      allowedManualUpdate: true,
      completed: true,
      updatedAt: *************,
    },
    {
      itemId: '3',
      tenantId: '142182303970343936',
      name: 'Business Owner account needs to be activated',
      description: 'Business Owner account needs to be activated',
      allowedManualUpdate: true,
      completed: true,
      updatedAt: *************,
    },
  ];

  const mockCheckListsUndone: Array<TenantChecklist> = [
    {
      itemId: '1',
      tenantId: '142182303970343936',
      name: 'Keycloak is setup successfully',
      description: 'Keycloak is setup successfully',
      allowedManualUpdate: true,
      completed: false,
      updatedAt: *************,
    },
    {
      itemId: '2',
      tenantId: '142182303970343936',
      name: 'Domain is already pointed to Pacific II',
      description: 'Domain is already pointed to Pacific II',
      allowedManualUpdate: true,
      completed: false,
      updatedAt: *************,
    },
    {
      itemId: '3',
      tenantId: '142182303970343936',
      name: 'Business Owner account needs to be activated',
      description: 'Business Owner account needs to be activated',
      allowedManualUpdate: true,
      completed: false,
      updatedAt: *************,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TenantChecklistComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: FuseConfirmationService,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TenantChecklistComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);
    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService']['permissionList'] = mockPermissionList;

    jest.spyOn(component['_toast'], 'success');
    jest.spyOn(component['_toast'], 'error');
    jest.spyOn(component['_router'], 'navigate');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hooks', () => {
    it('should call the handleGetCheckLists when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetTenantChecklists');

      component.ngOnInit();

      expect(component.handleGetTenantChecklists).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get the check list and can activate when checklist is all done when run handleGetTenantCheckList', () => {
      component.tenantId = mockTenantId;

      component.handleGetTenantChecklists();

      const req = httpTestingController.expectOne(
        API.TENANT.CHECKLISTS.MAIN.replace('{id}', mockTenantId),
      );
      req.flush(mockCheckListsDone);

      expect(component.checklists).toEqual(mockCheckListsDone);
      expect(component.disableActivate).toBe(false);
    });

    it('should get the check list and can not activate when checklist is not done when run handleGetTenantCheckList', () => {
      component.tenantId = mockTenantId;

      component.handleGetTenantChecklists();

      const req = httpTestingController.expectOne(
        API.TENANT.CHECKLISTS.MAIN.replace('{id}', mockTenantId),
      );
      req.flush(mockCheckListsUndone);

      expect(component.checklists).toEqual(mockCheckListsUndone);
      expect(component.disableActivate).toBe(true);
    });

    it('should get the permission and update the permission list when run getPermission', () => {
      expect(component.permissionList).toEqual({
        activate: false,
        checklist_update: false,
      });

      component['getPermissions']();

      expect(component.permissionList).toEqual({
        activate: true,
        checklist_update: true,
      });
    });
  });

  describe('Function that send request to server', () => {
    beforeEach(() => {
      jest.spyOn(component, 'handleGetTenantChecklists');
      component.tenantId = mockTenantId;
    });

    it('should send request to server to activate tenant and show toast and navigate to tenant management page when run handleActivateTenant', () => {
      component.handleActivateTenant();

      const req = httpTestingController.expectOne(
        API.TENANT.ACTIVATE.replace('{id}', mockTenantId),
      );
      req.flush(null);

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.TENANT.MAIN,
      ]);
      expect(component['_toast'].success).toHaveBeenCalledWith(
        'Tenant has been activated successfully!',
      );
    });

    it('should send request to mark list item as done and call handleGetTenantCheckLists after success when run handleMarkAsDone', () => {
      component.handleMarkAsDoneChecklistItem(mockCheckListsUndone[0].itemId);

      const req = httpTestingController.expectOne(
        API.TENANT.CHECKLISTS.MARK_AS_DONE.replace(
          '{id}',
          mockTenantId,
        ).replace('{itemId}', `${mockCheckListsUndone[0].itemId}`),
      );
      req.flush(null);

      expect(component.handleGetTenantChecklists).toHaveBeenCalled();
    });

    it('should send request to mark list item as undone and call handleGetTenantCheckLists after success when run handleMarkAsUnDone', () => {
      component.handleMarkAsUndoneChecklistItem(mockCheckListsUndone[0].itemId);

      const req = httpTestingController.expectOne(
        API.TENANT.CHECKLISTS.MARK_AS_UNDONE.replace(
          '{id}',
          mockTenantId,
        ).replace('{itemId}', `${mockCheckListsUndone[0].itemId}`),
      );
      req.flush(null);

      expect(component.handleGetTenantChecklists).toHaveBeenCalled();
    });

    it('should send request sync the check list and call handleGetTenantCheckLists after success when run onSyncChecklist', () => {
      component.onSyncChecklists();

      const req = httpTestingController.expectOne(
        API.TENANT.CHECKLISTS.SYNC.replace('{id}', mockTenantId),
      );
      req.flush(null);

      expect(component.handleGetTenantChecklists).toHaveBeenCalled();
    });
  });

  describe('Utility Function', () => {
    beforeEach(() => {
      jest.spyOn(component, 'handleMarkAsDoneChecklistItem');
      jest.spyOn(component, 'handleMarkAsUndoneChecklistItem');
      jest.spyOn(component, 'handleActivateTenant');
    });

    it('should call the handleMarkAsDoneChecklistItem method when run onMarkAsDone for item that is incomplete', () => {
      component.onMarkAsDone(mockCheckListsUndone[0]);

      expect(component.handleMarkAsDoneChecklistItem).toHaveBeenCalled();
    });

    it('should call the handleMarkAsUndoneChecklistItem method when run onMarkAsDone for item that is complete', () => {
      component.onMarkAsDone(mockCheckListsDone[0]);

      expect(component.handleMarkAsUndoneChecklistItem).toHaveBeenCalled();
    });

    it('should open the confirmation and call handleActivateTenant when run onOpenActivateNow ', () => {
      component.onOpenActivateNowDialog();

      expect(component['_fuseConfirmationService'].open).toHaveBeenCalledWith({
        title: 'Activate Now',
        message:
          'Please confirm to activate this tenant. <br/> This action cannot be undone.',
        icon: {
          show: false,
        },
        actions: {
          confirm: {
            label: 'OK',
            class: 'btn-contained__warning__medium',
          },
          cancel: {
            label: 'Cancel',
            class: 'btn-text__inherit__medium',
          },
        },
        dismissible: false,
      });

      expect(component.handleActivateTenant).toHaveBeenCalled();
    });
  });
});
