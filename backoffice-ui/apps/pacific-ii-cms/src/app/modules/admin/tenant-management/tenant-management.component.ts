import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { TenantManagementService } from './tenant-management.service';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { AdvancedSearchComponent } from '@fuse/components/advanced-search';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButton } from '@angular/material/button';
import { FuseUltils } from '@fuse/ultils';
import {
  BUSINESS_TYPE,
  DATE_TIME_FORMAT,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
  TENANT_STATUS,
} from '../../../core/const';
import { Utils } from '../../../core/utils/utils';
import { UserService } from '../../../core/user/user.service';
import { MatChipsModule } from '@angular/material/chips';
import { UtilityService } from '../../../services/utility.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';

@Component({
  selector: 'app-tenant-management',
  standalone: true,
  templateUrl: './tenant-management.component.html',
  imports: [
    FuseTableComponent,
    AdvancedSearchComponent,
    MatButton,
    MatChipsModule,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class TenantManagementComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private _unsubscribeAll: Subject<any> = new Subject<any>();
  dataSource: Array<any> = [];
  displayedColumns: any = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };
  searchForm: any = {};
  actions: Array<IAction> = [];
  total = 0;

  permissionList = {
    onboard: false,
    update: false,
  };

  /** http://localhost:4200/tenant-management?name=&businessType=SCHOOL&statuses=NEW&statuses=ACTIVATED
   * Constructor
   */
  constructor(
    private _tenantManagementService: TenantManagementService,
    private _userService: UserService,
    private _utilityService: UtilityService,
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _changeDetectorRef: ChangeDetectorRef,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.handleGetBusinessType();
    this.searchForm = this.initSearchForm();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        switchMap((queryParams) => {
          const filter = {
            name: queryParams['name'] || '',
            businessRegNo: queryParams['businessRegNo'] || '',
            businessType: queryParams['businessType'] || '',
            statuses: queryParams['statuses'] || [],
          };

          const mappedData = {
            filter,
            size: queryParams['size'] || 10,
            page: queryParams['page'] || 0,
            sortDirection:
              queryParams['sortDirection'] || this.sortDefault.direction,
            sortFields: queryParams['sortFields'] || [this.sortDefault.field],
          };

          if (queryParams['sortFields']) {
            mappedData.sortFields = mappedData.sortFields.replace(
              'tenantId',
              'id',
            );
          }
          return this._tenantManagementService.getTenants(
            FuseUltils.objectToQueryString(mappedData),
          );
        }),
      )
      .subscribe((tenants: any) => {
        this.dataSource = tenants.content;
        this.total = tenants.totalElements;
      });
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * Handles get business type list and update searchForm
   * @return {void}
   */
  handleGetBusinessType(): void {
    this._utilityService.getBusinessType().subscribe((data: any) => {
      const businessTypes = [{ label: 'All', value: '' }];
      data.map((item: string) => {
        businessTypes.push({
          label: item,
          value: item,
        });
      });

      this.searchForm.basic[1].options = businessTypes;
    });
  }

  /**
   * Navigates to the onboarding tenant page.
   *
   * @returns {void}
   */
  handleOnboardingTenant(): void {
    this._router.navigate([`${ROUTE.TENANT.MAIN}/${ROUTE.TENANT.ONBOARDING}`]);
  }

  /**
   * Handles the view tenant detail action.
   *
   * @param {any} element - The element containing the details of the tenant.
   *
   * @return {void}
   */
  handleViewTenantDetail(element: any): void {
    if (
      element.status == TENANT_STATUS[0].value &&
      this.permissionList.update
    ) {
      this._router.navigate(
        [`${ROUTE.TENANT.MAIN}/${ROUTE.TENANT.ONBOARDING}`],
        {
          queryParams: {
            id: element.tenantId,
          },
        },
      );
    } else {
      this._router.navigate([
        `${ROUTE.TENANT.MAIN}/${ROUTE.TENANT.DETAIL}/${element.tenantId}`,
      ]);
    }
  }

  handleLoginAsTenant(element: any): void {
    const user = { ...this._userService.userData, userMode: 'tenant' };
    this._userService.user = user;
    this._userService.selectedTenantId = element.tenantId;
    this._router.navigate(['/']);
  }

  /**
   * Downloads Keycloak realm setting for a given element.
   *
   * @param {any} element - The element object containing the tenantId.
   * @return {void} - This method does not return anything.
   */
  handleDownloadKeycloakSetting(element: any): void {
    this._tenantManagementService
      .downloadKeycloakRealmSetting(element.tenantId)
      .subscribe((data) => {
        Utils.downLoadFile(
          JSON.stringify(data),
          'application/json',
          data.displayName,
          'json',
        );
      });
  }

  onRowClick = (row: any) => {
    this.handleViewTenantDetail(row);
  };

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      { key: 'tenantId', name: 'ID', sort: true, selected: true },
      { key: 'name', name: 'Name', sort: true, selected: true },
      {
        key: 'businessRegNo',
        name: 'Business Registration No',
        selected: true,
      },
      {
        key: 'createdAt',
        name: 'Creation Date',
        sort: true,
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'updatedAt',
        name: 'Update Date',
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'activatedAt',
        name: 'Activation Date',
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      { key: 'businessType', name: 'Business Type', selected: true },
      {
        key: 'status',
        name: 'Status',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          let chipColor = '';
          switch (value) {
            case 'NEW':
              chipColor = 'bg-[#21B5D5] text-white';
              break;
            case 'ACTIVATED':
              chipColor = 'bg-[#36B37E] text-white';
              break;
            case 'UNDER_REVIEW':
              chipColor = 'bg-[#DFE3E8] text-black';
              break;
            default:
              chipColor = 'bg-[#21B5D5] text-white';
              break;
          }
          return `<mat-chip class="px-2 py-1 rounded-3 ${chipColor}">${value}</mat-chip>`;
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewTenantDetail(element),
      },
      {
        name: 'Login as Tenant',
        type: 'event',
        hidden: (element) => element.status != TENANT_STATUS[1].value,
        callback: (element) => this.handleLoginAsTenant(element),
      },
      {
        name: 'Download Keycloak',
        type: 'event',
        callback: (element) => this.handleDownloadKeycloakSetting(element),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Search by Name',
          name: 'name',
          placeholder: 'Search...',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'Business Type',
          name: 'businessType',
          type: 'select',
          defaultValue: '',
          options: BUSINESS_TYPE,
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: ['NEW', 'ACTIVATED', 'UNDER_REVIEW'],
          isMultiple: true,
          options: [
            { label: 'New', value: 'NEW' },
            { label: 'Activated', value: 'ACTIVATED' },
            { label: 'Under Review', value: 'UNDER_REVIEW' },
          ],
        },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          onboard: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.TENANT_MGMT, scope: ROLE_SCOPES.NEW_TENANT },
          ]),
          update: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.TENANT_MGMT, scope: ROLE_SCOPES.UPDATE },
          ]),
        };
      });
  }
}
