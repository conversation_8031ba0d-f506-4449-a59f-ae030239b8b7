import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TenantOnboardSuccessComponent } from './tenant-onboard-success.component';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { API, ROUTE } from 'apps/pacific-ii-cms/src/app/core/const';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('TenantOnboardSuccessComponent', () => {
  let component: TenantOnboardSuccessComponent;
  let fixture: ComponentFixture<TenantOnboardSuccessComponent>;
  let httpTestingController: HttpTestingController;

  const mockTenantDetail = {
    tenantId: 116035193997042688,
    name: 'A1. Son Test 1 ',
    businessRegNo: 'B121',
    businessType: 'CORPORATE',
    status: 'UNDER_REVIEW',
    email: '<EMAIL>',
    phoneNumber: '12332213',
    contactRemarks: '',
    email2: '',
    phoneNumber2: '',
    contactRemarks2: '',
    email3: '',
    phoneNumber3: '',
    contactRemarks3: '',
    website: '',
    logo: {
      path: 'pacific:image/2024/9/30/png-transparent-avatar-user-computer-icons-software-developer-avatar-child-face-heroes.png',
      url: 'https://*************:31805/pacific/image/2024/9/30/png-transparent-avatar-user-computer-icons-software-developer-avatar-child-face-heroes.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241003T074903Z&X-Amz-SignedHeaders=host&X-Amz-Expires=300&X-Amz-Credential=pacific-readonly%2F20241003%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Signature=fff3d90fbd080235650e7f51229169666bfa5392617bfb5e1406ccd1c3b1e99b',
    },
    realmId: '9c21accf-aae2-449f-a06f-60fe05a6324a',
    addressLine1: '12A',
    addressLine2: '',
    city: 'Ho Chi Minh',
    country: {
      countryCode: 'VN',
      countryName: 'VN',
    },
    postalCode: '',
    createdAt: 1727664946080,
    updatedAt: 1727666798737,
    activatedAt: 0,
    settings: {
      defaultDomain: 'testing.com.pacific.styl.solutions.com',
      timeZone: {
        zoneId: 'Asia/Ho_Chi_Minh',
        gtmOffset: 'GMT+07:00',
        displayName: '(GMT+07:00) Asia/Ho_Chi_Minh',
      },
      currency: {
        displayName: 'Vietnamese Dong',
        numericCode: 704,
        currencyCode: 'VND',
        symbol: '₫',
        fractionDigits: 0,
      },
      dateFormat: 'dd-MM-yyyy',
      timeFormat: 'HH:mm:ss',
    },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TenantOnboardSuccessComponent],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: { id: mockTenantDetail.tenantId },
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TenantOnboardSuccessComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);

    jest.spyOn(component['_router'], 'navigate');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockTenantDetail.tenantId);
  });

  it('should get the tenant detail when run ngOnInit', () => {
    component.ngOnInit();

    const req = httpTestingController.match(
      API.TENANT.DETAIL.replace('{id}', mockTenantDetail.tenantId.toString()),
    );
    req[1].flush(mockTenantDetail);

    expect(component.tenantDetail).toEqual(mockTenantDetail);
  });

  it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
    const unsubscribeNextSpy = jest.spyOn(component['_unsubscribeAll'], 'next');
    const unsubscribeCompleteSpy = jest.spyOn(
      component['_unsubscribeAll'],
      'complete',
    );

    component.ngOnDestroy();

    expect(unsubscribeNextSpy).toHaveBeenCalled();
    expect(unsubscribeCompleteSpy).toHaveBeenCalled();
  });

  it('should navigate to Tenant Information page when run backToHome method with the tenant detail is Under Review', () => {
    component.tenantDetail = mockTenantDetail;

    component.backToHome();

    expect(component['_router'].navigate).toHaveBeenCalledWith([
      `/${ROUTE.TENANT.MAIN}/${ROUTE.TENANT.INFORMATION}`,
    ]);
  });

  it('should navigate to default page when run backToHome method without the tenant detail', () => {
    component.tenantDetail = null;

    component.backToHome();

    expect(component['_router'].navigate).toHaveBeenCalledWith(['/']);
  });

  it('should navigate to default page when run backToHome method with the tenant detail that is not Under Review', () => {
    component.tenantDetail = mockTenantDetail;
    component.tenantDetail.status = 'ACTIVE';

    component.backToHome();

    expect(component['_router'].navigate).toHaveBeenCalledWith(['/']);
  });
});
