import { Component, Inject } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { PreOrderMenuItemsService } from '../pre-order-menu-items.service';
import { MatRadioModule } from '@angular/material/radio';
import { NgClass } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-edit-recurrent-pre-order-menu-item',
  standalone: true,
  imports: [
    NgClass,
    ReactiveFormsModule,
    MatRadioModule,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './edit-recurrent-pre-order-menu-item.component.html',
})
export class EditRecurrentPreOrderMenuItemComponent {
  followingForm!: UntypedFormGroup;

  optionListFollowingType = [
    {
      label: 'This item only',
      value: false,
    },
    {
      label: 'This item and following items',
      value: true,
    },
  ];
  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _preOrderMenuItemsService: PreOrderMenuItemsService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  submitForm(): void {
    if (this.data.type == 'delete') {
      this.handleDeletePreOrderMenuItem();
    } else {
      this.handleUpdatePreOrderMenuItem();
    }
  }

  handleUpdatePreOrderMenuItem(): void {
    const following = this.followingForm.value.following;
    const orderItemData = {
      status: this.data.detail.status,
      capacity: this.data.detail.capacity,
      following: following,
    };

    this._preOrderMenuItemsService
      .updatePreOrderMenuItem(
        this.data.menuId,
        this.data.detail.id,
        orderItemData,
      )
      .subscribe({
        next: () => {
          this._toast.success(
            `Update pre-order menu item${following ? ' and the following items' : ''} success!`,
          );
          this.onClose('save');
        },
        error: () => {
          this.onClose('save');
        },
      });
  }

  handleDeletePreOrderMenuItem(): void {
    const following = this.followingForm.value.following;
    this._preOrderMenuItemsService
      .deletePreOrderMenuItem(this.data.menuId, this.data.detail.id, following)
      .subscribe({
        next: () => {
          this._toast.success(
            `Delete pre-order menu item${following ? ' and the following items' : ''} success!`,
          );
          this.onClose('save');
        },
        error: () => {
          this.onClose('save');
        },
      });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  private initForm(): void {
    this.followingForm = this._formBuilder.group({
      following: [false],
    });
  }
}
