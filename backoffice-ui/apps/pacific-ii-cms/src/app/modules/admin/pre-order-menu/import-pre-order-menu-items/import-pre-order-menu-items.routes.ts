import { Routes } from '@angular/router';
import { ROUTE } from '../../../../core/const';
import { PreviewPresetComponent } from '../../../../components/preset-list/preview-preset/preview-preset.component';
import { ImportPreOrderMenuItemsComponent } from './import-pre-order-menu-items.component';

export default [
  {
    path: ROUTE.PRE_ORDER_MENU.IMPORT.LIST,
    component: ImportPreOrderMenuItemsComponent,
  },
  {
    path: ROUTE.PRE_ORDER_MENU.IMPORT.PREVIEW + '/:id',
    component: PreviewPresetComponent,
  },

  { path: '', pathMatch: 'full', redirectTo: ROUTE.PRE_ORDER_MENU.IMPORT.LIST },
] as Routes;
