<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex items-center gap-2">
      Pre-order Menu
      <fuse-help-link
        [url]="
          '/pre-order-menu/pre-order-management/#view-menu-list-and-change-status'
        "
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.update) {
        <button
          class="btn-contained__primary__medium"
          (click)="gotoImportPreOrderMenuItems()"
        >
          Import Menu Items
        </button>
      }

      @if (permissionList.create) {
        <button
          class="btn-contained__primary__medium"
          (click)="handleAddNewPreOrderMenu()"
        >
          Create Pre-order Menu
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [bulkActions]="bulkActions"
        [total]="total"
        [sortDefault]="sortDefault"
        [rowClick]="handleViewPreOrderMenuDetail"
        [selectElement]="permissionList.delete"
        (selectedElementChanged)="onChangeSelectedElement($event)"
        [searchForm]="searchForm"
        (searchSelectChanged)="handleLazyLoadSelect($event)"
      ></fuse-table-component>
    </div>
  </div>
</div>
