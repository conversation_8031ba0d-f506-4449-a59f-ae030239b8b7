import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import {
  DATE_TIME_FORMAT,
  ORDER_STATUS,
  ORDER_TYPE_LIST,
  PAYMENT_STATUS,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { CancelOrderComponent } from '../cancel-order/cancel-order.component';
import { OrderHistoryService } from '../order-history.service';
import { forkJoin, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { FuseUltils } from '@fuse/ultils';
import { PaymentTransactionDetailComponent } from '../../payment-transaction/payment-transaction-detail/payment-transaction-detail.component';
import { UserPermissionService } from 'apps/pacific-ii-cms/src/app/core/user/user-permission.service';
import { DateTime } from 'luxon';
import { StoreManagementService } from '../../store-management/store-management.service';
import { Utils } from '../../../../core/utils/utils';
import { FuseConfirmationService } from '../../../../../../../../libs/fuse/src/lib/services/confirmation';
import { DownloadPrintLabelComponent } from '../download-print-label/download-print-label.component';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
  selector: 'app-order-information',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatTableModule,
    MatSortModule,
    DatePipe,
    MatButtonModule,
    FuseHelpLinkComponent,
    MatTooltip,
  ],
  templateUrl: './order-information.component.html',
})
export class OrderInformationComponent implements OnInit {
  id!: string;
  orderDetail!: any;
  displayedColumns: string[] = [
    'productName',
    'column',
    'quantity',
    'unitPrice',
    'totalDiscount',
    'totalAmount',
  ];
  initDataSource!: MatTableDataSource<any>;
  paymentTransactionDetail!: any;
  storeDetail!: any;
  loading = false;

  statusListNotShowDownload = [
    ORDER_STATUS.CANCELLED,
    ORDER_STATUS.CANCELLING,
    ORDER_STATUS.CREATED,
    ORDER_STATUS.PENDING,
  ];

  permissionList = {
    update: false,
  };

  utils = FuseUltils;

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _orderService: OrderHistoryService,
    private _storeService: StoreManagementService,
    private _activatedRoute: ActivatedRoute,
    private _userPermissionService: UserPermissionService,
    private _confirmationService: FuseConfirmationService,
  ) {
    this.getPermission();
    this.getOrderId();
  }

  ngOnInit(): void {
    this.handleGetOrderDetail();
  }

  getStatusColor(status: string): string {
    return Utils.getOrderStatusColor(status);
  }

  getPaymentStatusColor(status: string): string {
    let color = '';
    switch (status) {
      case PAYMENT_STATUS.PAID:
        color = 'bg-[#D8FBDE] text-black';
        break;
      case PAYMENT_STATUS.PENDING:
        color = 'bg-[#FFF9CC] text-black';
        break;
      case PAYMENT_STATUS.FAILED:
        color = 'bg-[#FBD8D8] text-black';
        break;
      case PAYMENT_STATUS.REFUNDING:
        color = 'bg-[#D5ECFB] text-black';
        break;
      case PAYMENT_STATUS.REFUNDED:
        color = 'bg-[#FBD5FA] text-black';
        break;
      default:
        color = 'bg-[#D8FBDE] text-black';
        break;
    }
    return color;
  }

  handleGetOrderDetail(): void {
    this._orderService
      .getOrderDetail(this.id)
      .pipe(
        tap((orderDetail: any) => {
          const type = ORDER_TYPE_LIST.find(
            (item) => item.value == orderDetail.type,
          );
          orderDetail.type = type ? type.label : orderDetail.type;
          orderDetail.serviceChargeNames = orderDetail?.serviceCharges?.map(
            (item: any) => item.name,
          );

          this.orderDetail = { ...orderDetail };
          this.initDataSource = new MatTableDataSource(orderDetail.lineItems);
        }),
        switchMap((orderDetail: any) => {
          return forkJoin([
            orderDetail?.paymentTransactionId
              ? this.handleGetPaymentTransactionDetail(
                  orderDetail?.paymentTransactionId,
                )
              : of(null),
            orderDetail?.storeId
              ? this.handleGetStoreDetail(orderDetail?.storeId)
              : of(null),
          ]);
        }),
      )
      .subscribe();
  }

  checkCanCancelOrder(): boolean {
    if (!this.permissionList.update) {
      return false;
    }

    const currentStatus = this.orderDetail?.status || '';
    return ![ORDER_STATUS.CANCELLED, ORDER_STATUS.CANCELLING].includes(
      currentStatus,
    );
  }

  showConfirmationDialog(status: string): boolean {
    return [ORDER_STATUS.CREATED, ORDER_STATUS.PENDING].includes(status);
  }

  onOpenConfirmationCancelDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: `Confirm Cancel Order`,
      message: `The customer may currently be in the process of making a payment. Canceling the order now could lead to issues if the payment is successfully completed. <br/> <br/> Do you still want to cancel this order?`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Confirm',
        },
        cancel: {
          label: 'Cancel',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result === 'confirmed') {
        this.cancelOrder();
      }
    });
  }

  cancelOrder() {
    const dialogRef = this._dialog.open(CancelOrderComponent, {
      width: '60%',
      data: {
        detail: this.orderDetail,
        type: 'order',
      },
      autoFocus: false,
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetOrderDetail();
      }
    });
  }

  gotoOrderHistory(): void {
    this._router.navigate([ROUTE.ORDER_HISTORY.MAIN]);
  }

  transactionDetail() {
    this._dialog.open(PaymentTransactionDetailComponent, {
      width: '60%',
      height: '90vh',
      data: { id: this.orderDetail.paymentTransactionId },
    });
  }

  handleGetPaymentTransactionDetail(paymentTransactionId: any) {
    return this._orderService
      .getPaymentTransactionById(paymentTransactionId)
      .pipe(tap((txtDetail) => (this.paymentTransactionDetail = txtDetail)));
  }

  handleGetStoreDetail(storeId: any) {
    return this._storeService
      .getStoreDetail(storeId)
      .pipe(tap((storeDetail) => (this.storeDetail = storeDetail)));
  }

  formatPriceValue(value: any): string {
    return FuseUltils.formatPrice(value, this.orderDetail?.currency) ?? '';
  }

  getCollectionDateFormat(date: string) {
    return FuseUltils.tsToLocalTime(
      DateTime.fromFormat(date, DATE_TIME_FORMAT.DATE_ISO).toMillis(),
      DATE_TIME_FORMAT.DATE_DEFAULT,
    );
  }

  checkDownloadLabel() {
    if (!this.orderDetail?.status) {
      return false;
    }

    return !this.statusListNotShowDownload.includes(this.orderDetail.status);
  }

  openDownloadLabelDialog() {
    this._dialog.open(DownloadPrintLabelComponent, {
      width: '60%',
      data: {
        orderIds: [this.orderDetail.id],
      },
      autoFocus: false,
      disableClose: true,
    });
  }

  private getOrderId(): void {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id.replace('orderId', '');
      }
    });
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.ORDER_MGMT,
              scope: ROLE_SCOPES.CANCEL,
            },
          ]),
        };
      });
  }
}
