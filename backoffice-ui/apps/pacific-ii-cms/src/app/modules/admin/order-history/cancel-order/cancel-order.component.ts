import { Component, Inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { FuseInputComponent } from '@fuse/components/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { OrderHistoryService } from '../order-history.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { PAYMENT_STATUS } from 'apps/pacific-ii-cms/src/app/core/const';
import { FuseUltils } from '@fuse/ultils';
import { PreOrderHistoryService } from '../../pre-order-history/pre-order-history.service';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-cancel-order',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FuseInputComponent,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './cancel-order.component.html',
})
export class CancelOrderComponent implements OnInit, OnDestroy {
  cancelForm!: UntypedFormGroup;
  loading = false;

  errorMessages = {
    reason: {
      required: 'Please enter cancel reason!',
    },
  };

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _orderService: OrderHistoryService,
    private _preOrderService: PreOrderHistoryService,
    private _formBuilder: FormBuilder,
    private _confirmationService: FuseConfirmationService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    if (this.data.detail && this.data.detail.refundable) {
      this.cancelForm.patchValue({
        refundable: true,
      });
    }
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  checkCanRefund(): boolean {
    return (
      this.data &&
      this.data.detail.paymentStatus == PAYMENT_STATUS.PAID &&
      this.data.detail.refundable
    );
  }

  submitForm(): void {
    for (const i in this.cancelForm.controls) {
      this.cancelForm.controls[i].markAsTouched();
      this.cancelForm.controls[i].updateValueAndValidity();
    }

    if (this.cancelForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.confirmCancelOrder();
  }

  confirmCancelOrder(): void {
    const dialogRef = this._confirmationService.open({
      title: `Cancel ${this.data?.type === 'pre-order' ? 'Pre-order' : 'Order'}`,
      message: `Please confirm to cancel the selected ${this.data?.type ?? 'order'}.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: `Cancel ${this.data?.type === 'pre-order' ? 'Pre-order' : 'Order'}`,
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.loading = true;
        if (this.data?.type === 'pre-order') {
          this.handleCancelPreOrder();
        } else {
          this.handleCancelOrder();
        }
      }
    });
  }

  handleCancelOrder(): void {
    const dataParams = {
      ids: [this.data.detail.id],
      reason: this.cancelForm.value.reason,
      refundable: this.cancelForm.value.refundable ?? false,
    };

    this._orderService.cancelOrder(dataParams).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success(`Cancel order success!`);
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  handleCancelPreOrder(): void {
    const dataParams = {
      reason: this.cancelForm.value.reason,
      refundable: this.cancelForm.value.refundable ?? false,
    };

    this._preOrderService
      .cancelPreOrder(this.data.detail.id, dataParams)
      .subscribe({
        next: () => {
          this.loading = false;
          this._toast.success(`Cancel pre-order success!`);
          this.onClose('save');
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  formatCurrency() {
    return FuseUltils.formatPrice(
      this.data.detail.totalAmount,
      this.data.detail.currency,
    );
  }

  private initForm() {
    this.cancelForm = this._formBuilder.group({
      reason: [null, Validators.required],
      refundable: [null],
    });
  }
}
