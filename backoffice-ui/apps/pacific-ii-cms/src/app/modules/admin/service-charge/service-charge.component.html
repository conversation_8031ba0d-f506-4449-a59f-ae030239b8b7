<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex items-center gap-2">
      {{ 'service-charge.management' | transloco }}
      <fuse-help-link [url]="''"></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.add) {
        <button
          [color]="'primary'"
          type="button"
          mat-flat-button
          (click)="navigateUpdateServiceCharge()"
        >
          {{ 'service-charge.add' | transloco }}
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [pagination]="false"
        [hideColBtn]="true"
        [template]="template"
        [rowClick]="navigateServiceChargeDetail"
      ></fuse-table-component>
    </div>
  </div>
</div>

<ng-template #template let-data="data" let-header="header">
  <ng-container>
    @if (header.key === 'platformOrderTypes') {
      <div class="flex flex-wrap items-center gap-1 my-1">
        @for (type of data.platformOrderTypes; track $index) {
          @if ($index < 2) {
            <div class="primary-card">
              {{ type }}
            </div>
          }
        }

        @if (data.platformOrderTypes.length > 2) {
          <button type="button" class="text-primary">
            +{{ data.platformOrderTypes.length - 2 }} more
          </button>
        }
      </div>
    }
  </ng-container>
</ng-template>
