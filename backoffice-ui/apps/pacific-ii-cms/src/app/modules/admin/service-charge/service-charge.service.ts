import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  IServiceCharge,
  IServiceChargeDetail,
  IServiceChargesResponse,
} from './service-charge.types';
import { API } from '../../../core/const';

@Injectable({
  providedIn: 'root',
})
export class ServiceChargeService {
  private _httpClient = inject(HttpClient);

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all service charge
   */
  getServiceCharges(queryParams = ''): Observable<IServiceChargesResponse> {
    return this._httpClient.get<IServiceChargesResponse>(
      `${API.SERVICE_CHARGE.LIST}?${queryParams}`,
    );
  }

  /**
   * Get service charge detail
   */
  getServiceChargeDetail(id: string): Observable<IServiceChargeDetail> {
    return this._httpClient.get<IServiceChargeDetail>(
      API.SERVICE_CHARGE.DETAIL.replace('{id}', id),
    );
  }

  /**
   * Add new service charge
   */
  addServiceCharge(data: IServiceCharge): Observable<any> {
    return this._httpClient.post(API.SERVICE_CHARGE.ADD, data);
  }

  /**
   * Update the existing service charge
   */
  updateServiceCharge(id: string, data: IServiceCharge) {
    return this._httpClient.put(API.SERVICE_CHARGE.UPDATE.replace('{id}', id), data);
  }

  /**
   * Delete the existing service charge
   */
  deleteServiceCharge(id: string): Observable<any> {
    return this._httpClient.delete(API.SERVICE_CHARGE.DELETE.replace('{id}', id));
  }
}
