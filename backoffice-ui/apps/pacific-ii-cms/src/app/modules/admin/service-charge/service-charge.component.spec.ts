import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ServiceChargeComponent } from './service-charge.component';
import { provideHttpClient } from '@angular/common/http';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { provideIcons } from '../../../core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { provideTranslate } from '../../../core/transloco/transloco.provider';
import { ServiceChargeService } from './service-charge.service';
import { ROUTE } from '../../../core/const';
import { FuseConfirmationService } from '../../../../../../../libs/fuse/src/lib/services/confirmation';

describe('ServiceChargeComponent', () => {
  let component: ServiceChargeComponent;
  let fixture: ComponentFixture<ServiceChargeComponent>;

  let router: Router;
  let toast: ToastrService;
  let serviceChargeService: ServiceChargeService;
  let confirmationService: FuseConfirmationService;

  let mockTranslocoService: any;

  const mockServiceChargeResponse: any = {
    content: [
      {
        id: '11111111',
        tenantId: '11111112',
        isActive: true,
        name: 'SC1',
        description: 'string',
        chargeFixedAmount: 500,
        chargeRate: 0.05,
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        platformOrderTypes: ['PRE_ORDER', 'TEST'],
        createdAt: 1742442049000,
        updatedAt: 1742442049000,
        createdBy: 'string',
        updatedBy: 'string',
      },
    ],
  };

  const mockPermission = [
    {
      externalId: 'TEST',
      displayOrder: 10,
      children: [],
    },
    {
      externalId: 'SERVICE_CHARGE_MGMT_ADD',
      displayOrder: 10,
      children: [],
    },
    {
      externalId: 'SERVICE_CHARGE_MGMT_UPDATE',
      displayOrder: 10,
      children: [],
    },
    {
      externalId: 'SERVICE_CHARGE_MGMT_DELETE',
      displayOrder: 10,
      children: [],
    },
  ];

  beforeEach(async () => {
    mockTranslocoService = {
      translate: jest.fn((key, params) => {
        if (key === 'service-charge.dialog.delete-title') {
          return 'Delete Service Charge';
        } else if (key === 'service-charge.dialog.delete-message') {
          return 'Please confirm to delete this service charge. <br/> Please be careful for this action. When this is removed, you can not recover it.';
        } else if (key === 'service-charge.message.delete-success') {
          return 'You have deleted the service charge successfully!';
        }
        return key;
      }),
    };

    await TestBed.configureTestingModule({
      imports: [
        ServiceChargeComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              params: {},
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ServiceChargeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    router = TestBed.inject(Router);
    toast = TestBed.inject(ToastrService);
    serviceChargeService = TestBed.inject(ServiceChargeService);
    confirmationService = TestBed.inject(FuseConfirmationService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should call handleGetServiceCharges with correct params on init', () => {
      const spy = jest.spyOn(component, 'handleGetServiceCharges');
      component.ngOnInit();

      // expect(component.queryParams).toEqual(mockQueryParams);
      expect(spy).toHaveBeenCalled();
    });

    it('should initialize the table columns and table actions when run the ngAfterViewInit', () => {
      const initTableColumnSpy = jest.spyOn(
        Object.getPrototypeOf(component),
        'initTableColumn',
      );
      const initTableActionSpy = jest.spyOn(
        Object.getPrototypeOf(component),
        'initTableAction',
      );
      const detectChangesSpy = jest.spyOn(
        component['_changeDetectorRef'],
        'detectChanges',
      );

      component.ngAfterViewInit();

      expect(initTableColumnSpy).toHaveBeenCalled();
      expect(initTableActionSpy).toHaveBeenCalled();
      expect(detectChangesSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions on destroy', () => {
      fixture.detectChanges();
      const nextSpy = jest.spyOn(component['_destroy$'], 'next');
      const completeSpy = jest.spyOn(component['_destroy$'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    let getServiceChargesSpy: any;

    beforeEach(() => {
      getServiceChargesSpy = jest.spyOn(
        serviceChargeService,
        'getServiceCharges',
      );

      // component.type = mockPresetType.PRODUCT;
    });

    it('should update table data after fetching service charge list successfully', () => {
      getServiceChargesSpy.mockReturnValue(of(mockServiceChargeResponse));
      component.handleGetServiceCharges();

      expect(getServiceChargesSpy).toHaveBeenCalled();
      expect(component.dataSource).toEqual([
        {
          ...mockServiceChargeResponse.content[0],
          platformOrderTypes: ['Pre-Order', 'TEST'],
        },
      ]);
    });

    it('should not update table data and total after fetching preset list unsuccessfully', () => {
      getServiceChargesSpy.mockReturnValue(
        throwError(() => new Error(undefined)),
      );
      component.handleGetServiceCharges();

      expect(getServiceChargesSpy).toHaveBeenCalled();
      expect(component.dataSource).toEqual([]);
    });
  });

  describe('Navigation', () => {
    let navigateSpy: any;

    beforeEach(() => {
      navigateSpy = jest.spyOn(router, 'navigate');
    });

    it('should navigate to the add service charge page when service charge does not exist', () => {
      component.navigateUpdateServiceCharge();
      expect(navigateSpy).toHaveBeenCalledWith([
        `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}/${ROUTE.SERVICE_CHARGE.ADD}`,
      ]);
    });

    it('should navigate to the update service charge page when service charge exists', () => {
      component.navigateUpdateServiceCharge(
        mockServiceChargeResponse.content[0],
      );
      expect(navigateSpy).toHaveBeenCalledWith([
        `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}/${ROUTE.SERVICE_CHARGE.EDIT}/${mockServiceChargeResponse.content[0].id}`,
      ]);
    });

    it('should navigate to the service charge detail page', () => {
      component.navigateServiceChargeDetail(
        mockServiceChargeResponse.content[0],
      );
      expect(navigateSpy).toHaveBeenCalledWith([
        `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}/${ROUTE.SERVICE_CHARGE.DETAIL}/${mockServiceChargeResponse.content[0].id}`,
      ]);
    });
  });

  describe('Open Dialog', () => {
    let confirmationOpenSpy: any;
    let handleDeleteServiceChargeSpy: any;

    const expectedConfirmationConfig = {
      title: 'Delete Service Charge',
      message:
        'Please confirm to delete this service charge. <br/> Please be careful for this action. When this is removed, you can not recover it.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'common.action.delete',
        },
        cancel: {
          label: 'common.action.close',
        },
      },
      dismissible: false,
    };

    beforeEach(() => {
      confirmationOpenSpy = jest.spyOn(confirmationService, 'open');
      handleDeleteServiceChargeSpy = jest.spyOn(
        component,
        'handleDeleteServiceCharge',
      );
      component['_translocoService'] = mockTranslocoService;
    });

    it('should open delete service charge confirmation dialog with correct configuration', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteServiceChargeDialog(
        mockServiceChargeResponse.content[0].id,
      );

      expect(confirmationOpenSpy).toHaveBeenCalledWith(
        expectedConfirmationConfig,
      );
    });

    it('should delete service charge when confirmation dialog closes with result is confirmed', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteServiceChargeDialog(
        mockServiceChargeResponse.content[0].id,
      );

      expect(handleDeleteServiceChargeSpy).toHaveBeenCalled();
    });

    it('should not delete service charge when confirmation dialog closes with result is cancelled', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('cancelled'),
      });
      component.onOpenDeleteServiceChargeDialog(
        mockServiceChargeResponse.content[0].id,
      );

      expect(handleDeleteServiceChargeSpy).not.toHaveBeenCalled();
    });
  });

  describe('Delete Service Charge', () => {
    let deleteServiceChargeSpy: any;
    let toastSuccessSpy: any;
    let handleGetServiceChargesSpy: any;

    const mockServiceChargeId = mockServiceChargeResponse.content[0].id;

    beforeEach(() => {
      deleteServiceChargeSpy = jest.spyOn(
        serviceChargeService,
        'deleteServiceCharge',
      );
      toastSuccessSpy = jest.spyOn(toast, 'success');
      handleGetServiceChargesSpy = jest.spyOn(
        component,
        'handleGetServiceCharges',
      );
      component['_translocoService'] = mockTranslocoService;
    });

    it('should show success toast and get service charge list after deleting service charge successfully', () => {
      deleteServiceChargeSpy.mockReturnValue(of(null));
      component.handleDeleteServiceCharge(mockServiceChargeId);

      expect(deleteServiceChargeSpy).toHaveBeenCalledWith(mockServiceChargeId);
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'You have deleted the service charge successfully!',
      );
      expect(handleGetServiceChargesSpy).toHaveBeenCalled();
    });

    it('should not show success toast and get service charge list after deleting service charge unsuccessfully', () => {
      deleteServiceChargeSpy.mockReturnValue(
        throwError(() => new Error(undefined)),
      );
      component.handleDeleteServiceCharge(mockServiceChargeId);

      expect(deleteServiceChargeSpy).toHaveBeenCalledWith(mockServiceChargeId);
      expect(toastSuccessSpy).not.toHaveBeenCalled();
      expect(handleGetServiceChargesSpy).not.toHaveBeenCalled();
    });
  });

  describe('Table Column Initialization', () => {
    const mockServiceChargeDetail = mockServiceChargeResponse.content[0];

    it('should initialize table columns', () => {
      component['initTableColumn']();

      expect(component.displayedColumns).toEqual([
        {
          key: 'name',
          name: 'common.name',
          sort: false,
          selected: true,
        },
        {
          key: 'platformOrderTypes',
          name: 'service-charge.platform-order-type',
          sort: false,
          selected: true,
          custom: true,
        },
        {
          key: 'chargeFixedAmount',
          name: 'service-charge.charge-amount',
          sort: false,
          selected: true,
          headerAlign: 'right',
          renderHtml: expect.any(Function),
        },
        {
          key: 'chargeRate',
          name: 'service-charge.charge-rate',
          sort: false,
          selected: true,
          renderHtml: expect.any(Function),
        },
        {
          key: 'createdAt',
          name: 'common.creation-date',
          sort: false,
          selected: true,
          render: expect.any(Function),
        },
        {
          key: 'isActive',
          name: 'common.status.main',
          sort: false,
          selected: true,
          renderHtml: expect.any(Function),
        },
      ]);
    });

    it('should render HTML correctly for charge amount column', () => {
      component['initTableColumn']();
      const chargeAmountColumn = component.displayedColumns.find(
        (col: any) => col.key === 'chargeFixedAmount',
      );
      const renderedHtml = chargeAmountColumn?.renderHtml(
        500,
        mockServiceChargeDetail,
      );

      expect(renderedHtml).toEqual('<div class="flex justify-end">$5.00</div>');
    });

    it('should render HTML correctly for chargeRate column', () => {
      component['initTableColumn']();
      const chargeRateColumn = component.displayedColumns.find(
        (col: any) => col.key === 'chargeRate',
      );
      const renderedHtml = chargeRateColumn?.renderHtml('0.0500');

      expect(renderedHtml).toEqual('5.00%');
    });

    it('should render HTML correctly for creation date column', () => {
      component['initTableColumn']();
      const createdAtColumn = component.displayedColumns.find(
        (col: any) => col.key === 'createdAt',
      );
      const renderedHtml = createdAtColumn?.render(1734416501942);

      expect(renderedHtml).toEqual('17/12/2024');
    });

    it('should render HTML correctly for status column', () => {
      component['initTableColumn']();
      const statusColumn = component.displayedColumns.find(
        (col: any) => col.key === 'isActive',
      );

      let renderedHtml = statusColumn?.renderHtml(true);
      expect(renderedHtml).toEqual(
        '<mat-chip class="px-2 py-1 rounded-3 bg-[#36B37E] text-white">Active</mat-chip>',
      );

      renderedHtml = statusColumn?.renderHtml(false);
      expect(renderedHtml).toEqual(
        '<mat-chip class="px-2 py-1 rounded-3 bg-[#DFE3E8] text-black">Inactive</mat-chip>',
      );
    });
  });

  describe('Table Action Initialization', () => {
    const mockServiceChargeDetail = mockServiceChargeResponse.content[0];

    it('should initialize table actions', () => {
      component['initTableAction']();

      expect(component.actions).toEqual([
        {
          name: 'common.action.view',
          type: 'event',
          callback: expect.any(Function),
        },
        {
          name: 'common.action.edit',
          type: 'event',
          hidden: expect.any(Function),
          callback: expect.any(Function),
        },
        {
          name: 'common.action.delete',
          type: 'event',
          hidden: expect.any(Function),
          callback: expect.any(Function),
        },
      ]);
    });

    it('should call function navigateServiceChargeDetail after clicking view action', () => {
      const spy = jest.spyOn(component, 'navigateServiceChargeDetail');

      component['initTableAction']();
      const viewAction = component.actions.find(
        (col: any) => col.name === 'common.action.view',
      );

      if (viewAction?.callback) {
        viewAction.callback(mockServiceChargeDetail);
        expect(spy).toHaveBeenCalledWith(mockServiceChargeDetail);
      }
    });

    it('should call function navigateUpdateServiceCharge after clicking edit action', () => {
      const spy = jest.spyOn(component, 'navigateUpdateServiceCharge');

      component['initTableAction']();
      const editAction = component.actions.find(
        (col: any) => col.name === 'common.action.edit',
      );

      if (editAction?.hidden) {
        component.permissionList.update = true;
        let result = editAction.hidden(mockServiceChargeDetail);
        expect(result).toBeFalsy();

        component.permissionList.update = false;
        result = editAction.hidden(mockServiceChargeDetail);
        expect(result).toBeTruthy();
      }

      if (editAction?.callback) {
        editAction.callback(mockServiceChargeDetail);
        expect(spy).toHaveBeenCalledWith(mockServiceChargeDetail);
      }
    });

    it('should call function onOpenDeleteServiceChargeDialog after clicking edit action', () => {
      const spy = jest.spyOn(component, 'onOpenDeleteServiceChargeDialog');

      component['initTableAction']();
      const deleteAction = component.actions.find(
        (col: any) => col.name === 'common.action.delete',
      );

      if (deleteAction?.hidden) {
        component.permissionList.delete = true;
        let result = deleteAction.hidden(mockServiceChargeDetail);
        expect(result).toBeFalsy();

        component.permissionList.delete = false;
        result = deleteAction.hidden(mockServiceChargeDetail);
        expect(result).toBeTruthy();
      }

      if (deleteAction?.callback) {
        deleteAction.callback(mockServiceChargeDetail);
        expect(spy).toHaveBeenCalledWith(mockServiceChargeDetail.id);
      }
    });
  });

  describe('Get Permission', () => {
    it('should set update in permissionList to true when update permission is found', () => {
      component.permissionList = {
        add: false,
        update: false,
        delete: false,
      };

      component['_userPermissionService']['permissions'] = mockPermission;
      component['_userPermissionService']['permissionList'] = mockPermission;
      component['getPermission']();

      expect(component.permissionList).toEqual({
        add: true,
        update: true,
        delete: true,
      });
    });

    it('should set update in permissionList to false when update permission is not found', () => {
      component.permissionList = {
        add: true,
        update: true,
        delete: true,
      };

      component['_userPermissionService']['permissions'] = [mockPermission[0]];
      component['_userPermissionService']['permissionList'] = [
        mockPermission[0],
      ];
      component['getPermission']();

      expect(component.permissionList).toEqual({
        add: false,
        update: false,
        delete: false,
      });
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
