import { Currency } from '../tenant-management/tenant.types';

export interface PaymentMethod {
  id: string;
  processorId: string;
  tenantId: string;
  displayName: string;
  icon?: {
    url: string;
    path: string;
  };
  description: string;
  paymentInstruction: string;
  surchargeRate: string;
  fixedSurcharge: number;
  currency: Currency;
  surchargeTitle: string;
  connectedAccount: ConnectedAccount | null;
  processorConfig: NetsPaymentConfig | StripePaymentConfig;
  acceptedApplications: string[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  updatedBy: string;
}

export interface PaymentMethodDataSource extends PaymentMethod {
  status: string;
}

export interface PaymentMethodResponse {
  content: PaymentMethod[];
  totalElements: number;
  totalPages: number;
  page: number;
  sort: string[];
}

export interface PaymentProcessor {
  processorId: string;
  processorName: string;
  processorDescription: string;
  acceptedApplications: string[];
}

export interface RenderObject<T> {
  type: string;
  label: string;
  name: string;
  placeholder: string;
  errorMsg: ErrorMessagesObject;
  options?: Array<T>;
  optionLabel?: string;
  optionValue?: string;
  description?: string;
}

export interface ErrorMessagesObject {
  required?: string;
  min?: string;
  pattern?: string;
  minlength?: string;
  maxlength?: string;
  match?: string;
  unavailable?: string;
}

export interface ConnectedAccount {
  id: string;
  isActive: boolean;
  tenantId: string;
  processorId: string;
  ownerEmail?: string | null;
  clientExternalId: string;
  createdAt: number;
  updatedAt: number;
}

export interface NetsPaymentConfig {
  processorId: string;
  netsFamilyCardType: string;
}

export interface StripePaymentConfig {
  processorId: string;
  apiKey: string;
  secretKey?: string;
}

export interface WebhookEndpoints {
  id: string;
  paymentMethodId: string;
  paymentProcessorId: string;
  externalClientId: string;
  eventType: string;
  tenantId: string;
  url: string;
  signingSecretKey: string;
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  updatedBy: string;
}

export interface PaymentConnectedAccountResponse {
  content: ConnectedAccount[];
  totalElements: number;
  totalPages: number;
  page: number;
  sort: Array<string>[];
}

export interface PaymentConnectedAccount {
  connectedAccountId?: string | null;
  refreshUrl: string;
  returnUrl: string;
}

export interface ConnectPaymentAccountResponse {
  paymentMethod: PaymentMethod;
  connectedAccountLink: ConnectedAccountLink;
}

export interface ConnectedAccountLink {
  redirectUrl: string;
  createdAt: number;
  expiredAt: number;
}

export function getPaymentAccountName(
  connectedAccount: ConnectedAccount,
): string {
  const { ownerEmail, clientExternalId } = connectedAccount;

  if (ownerEmail && clientExternalId) {
    return `${ownerEmail} (${clientExternalId})`;
  }

  return ownerEmail || clientExternalId || 'N/A';
}
