<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex gap-2 items-center">
      Payment Method
      <fuse-help-link
        [url]="'/configuration/payment-wallet-tax/#manage-payment-methods'"
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.create) {
        <button
          [color]="'primary'"
          type="button"
          mat-flat-button
          (click)="navigateAddPaymentMethodPage()"
        >
          Add New Method
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        #tableComponent
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [total]="total"
        [sortDefault]="sortDefault"
        [rowClick]="navigatePaymentMethodDetailsPage"
        [searchForm]="searchForm"
      ></fuse-table-component>
    </div>
  </div>
</div>
