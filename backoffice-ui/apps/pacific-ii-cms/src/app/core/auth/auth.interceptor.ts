import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandlerFn,
  HttpRequest,
} from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, Observable, throwError, defer, concatMap } from 'rxjs';
import { AuthService } from './auth.service';
import { AuthUtils } from './auth.utils';
import { StorageService } from '../services/storage.service';
import { ToastrService } from 'ngx-toastr';
import { ROUTE, SPECIAL_ERROR_CODE } from '../const';

export const authInterceptor = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const _authService = inject(AuthService);
  const _storageService = inject(StorageService);
  const _toast = inject(ToastrService);

  return defer(() => {
    // Initial request setup
    let newReq = req.clone();

    // Check if we need to refresh token
    const needsRefresh =
      _authService?.accessToken &&
      AuthUtils.isTokenExpired(_authService.accessToken);

    // Create an observable that will either refresh token or just pass through
    const tokenOperation$ = needsRefresh
      ? _authService.refreshToken()
      : Promise.resolve();

    return defer(() => tokenOperation$).pipe(
      concatMap(() => {
        // After potential refresh, set up the request with latest token
        if (_authService?.accessToken) {
          newReq = req.clone({
            headers: req.headers.set(
              'Authorization',
              'Bearer ' + _authService.accessToken,
            ),
          });
        }

        // Add tenant header if needed
        const userMode = _storageService.getUserMode();
        const tenantId = _storageService.getTenantId();

        if (window?.location?.pathname?.includes(ROUTE.DEVICES.MAIN)) {
          if (
            userMode == 'tenant' &&
            tenantId &&
            !newReq.headers.has('X-Tenant-ID')
          ) {
            newReq = newReq.clone({
              headers: newReq.headers.set('X-Tenant-ID', tenantId),
            });
          }
        } else {
          if (!newReq.headers.has('X-Tenant-ID')) {
            newReq = newReq.clone({
              headers: newReq.headers.set('X-Tenant-ID', tenantId ?? '0'),
            });
          }
        }

        // Execute the request
        return next(newReq).pipe(
          catchError((error) => {
            // Catch "401 Unauthorized" responses
            if (error instanceof HttpErrorResponse && error.status === 401) {
              // Sign out
              _authService.logout();
              // Reload the app
              location.reload();
            } else {
              if (SPECIAL_ERROR_CODE.includes(error?.error?.code)) {
                _toast.error(error.error.details.join(', '));
              } else if (error?.error?.message) {
                _toast.error(error.error.message);
              } else {
                _toast.error('An unknown error occurred.');
              }
            }

            return throwError(error);
          }),
        );
      }),
    );
  });
};
