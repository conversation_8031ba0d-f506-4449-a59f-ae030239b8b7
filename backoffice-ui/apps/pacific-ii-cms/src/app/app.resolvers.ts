import { inject } from '@angular/core';

import { forkJoin, switchMap } from 'rxjs';
import { NavigationService } from './core/navigation/navigation.service';
import { UserService } from './core/user/user.service';
import { NotificationsService } from './layout/common/notifications/notifications.service';

export const initialDataResolver = () => {
  const _navigationService = inject(NavigationService);
  const _notificationsService = inject(NotificationsService);
  const _userService = inject(UserService);

  // Fork join multiple API endpoint calls to wait all of them to finish
  return forkJoin([_navigationService.get(), _userService.get()]).pipe(
    switchMap((data) => {
      return _userService.syncUserProfile(data[1]);
    }),
  );
};
