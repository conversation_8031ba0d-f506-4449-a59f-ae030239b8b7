import { TestBed } from '@angular/core/testing';
import { NotificationsService } from './notifications.service';
import { provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { DateTime } from 'luxon';
import { INotification } from './notifications.types';
import { FuseUltils } from '@fuse/ultils';
import { API } from '../../../core/const';

describe.only('NotificationsService', () => {
  let service: NotificationsService;
  let httpTestingController: HttpTestingController;

  const now = DateTime.now();

  const mockTenantId = '0';

  const mockUserId = '116024617432315904';

  const mockNotifications: Array<INotification> = [
    {
      id: '493190c9-5b61-4912-afe5-78c21f1044d7',
      icon: 'heroicons_mini:star',
      title: 'Daily challenges',
      content: 'Your submission has been accepted',
      createdAt: now.minus({ minute: 25 }).valueOf(), // 25 minutes ago
      tenantId: mockTenantId,
      userId: mockUserId,
      read: false,
    },
    {
      id: '6e3e97e5-effc-4fb7-b730-52a151f0b641',
      content:
        '<strong>Leo Gill</strong> added you to <em>Top Secret Project</em> group and assigned you as a <em>Project Manager</em>',
      createdAt: now.minus({ minute: 50 }).valueOf(), // 50 minutes ago
      tenantId: mockTenantId,
      userId: mockUserId,
      read: false,
    },
    {
      id: 'b91ccb58-b06c-413b-b389-87010e03a120',
      icon: 'heroicons_mini:envelope',
      title: 'Mailbox',
      content: 'You have 15 unread mails across 3 mailboxes',
      createdAt: now.minus({ hour: 3 }).valueOf(), // 3 hours ago
      tenantId: mockTenantId,
      userId: mockUserId,
      read: false,
    },
    {
      id: '541416c9-84a7-408a-8d74-27a43c38d797',
      icon: 'heroicons_mini:arrow-path',
      title: 'Cron jobs',
      content: 'Your <em>Docker container</em> is ready to publish',
      createdAt: now.minus({ hour: 5 }).valueOf(), // 5 hours ago
      tenantId: mockTenantId,
      userId: mockUserId,
      read: false,
    },
  ];

  const mockNotificationsResponse = {
    content: mockNotifications,
    lastEvaluatedKey: {},
  };

  const mockTrackingData = {
    userId: mockUserId,
    tenantId: mockTenantId,
    hasNewNotification: false,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(NotificationsService);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should send request to server get all the notification based on the user id when run getAll method', (done) => {
    service.notifications$.subscribe((value) => {
      expect(value).toEqual(mockNotificationsResponse);
      done();
    });

    service
      .getAll(
        FuseUltils.objectToQueryString({
          filter: {
            userId: mockUserId,
          },
        }),
      )
      .subscribe();

    const req = httpTestingController.expectOne(
      `${API.NOTIFICATION.LIST}?filter.userId=${mockUserId}`,
    );

    expect(req.request.method).toBe('GET');

    req.flush(mockNotificationsResponse);
  });

  it('should send request to server to mark one or all notification as read when run markAsRead method', () => {
    service.markAsRead(mockUserId, [mockNotifications[0].id]).subscribe();

    const req = httpTestingController.expectOne(API.NOTIFICATION.READ);

    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual({
      userId: mockUserId,
      ids: [mockNotifications[0].id],
    });

    req.flush(null);
  });

  it('should send request to server to delete one or all notification when run delete method', () => {
    service.delete(mockUserId, [mockNotifications[0].id]).subscribe();

    const req = httpTestingController.expectOne(API.NOTIFICATION.DELETE);

    expect(req.request.method).toBe('DELETE');
    expect(req.request.body).toEqual({
      userId: mockUserId,
      ids: [mockNotifications[0].id],
    });

    req.flush(null);
  });

  it('should send request to server to get the tracking data when run tracking method', (done) => {
    service.tracking().subscribe((value) => {
      expect(value).toEqual(mockTrackingData);
      done();
    });

    const req = httpTestingController.expectOne(API.NOTIFICATION.TRACKING);

    expect(req.request.method).toBe('GET');

    req.flush(mockTrackingData);
  });

  it('should send request to server to mark that you have view newest notification when run markView method', () => {
    service.markView(mockUserId).subscribe();

    const req = httpTestingController.expectOne(API.NOTIFICATION.TRACKING);

    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual({ userId: mockUserId });

    req.flush(null);
  });
});
