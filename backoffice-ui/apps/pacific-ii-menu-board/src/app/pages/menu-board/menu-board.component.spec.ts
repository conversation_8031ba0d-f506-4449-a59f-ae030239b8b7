import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MenuBoardComponent } from './menu-board.component';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { MenuBoardSessionDetail } from '../../types/menu-board.types';
import { MenuBoardService } from '../../services/menu-board.service';

describe('MenuBoardComponent', () => {
  let component: MenuBoardComponent;
  let fixture: ComponentFixture<MenuBoardComponent>;

  let menuBoardService: MenuBoardService;

  const mockMenuBoardSessionDetail: MenuBoardSessionDetail = {
    menuBoard: {
      images: [
        { image: { url: 'image1.jpg' } },
        { image: { url: 'image2.jpg' } },
      ],
    },
  } as MenuBoardSessionDetail;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MenuBoardComponent],
      providers: [provideHttpClient(), provideAnimationsAsync()],
    }).compileComponents();

    menuBoardService = TestBed.inject(MenuBoardService);

    fixture = TestBed.createComponent(MenuBoardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should initialize menuBoardSessionDetail subscription on init', () => {
      const initSpy = jest.spyOn(
        component as any,
        'initMenuBoardSessionDetailSubscription',
      );

      component.ngOnInit();

      expect(initSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions on destroy', () => {
      fixture.detectChanges();
      const nextSpy = jest.spyOn(component['_destroy$'], 'next');
      const completeSpy = jest.spyOn(component['_destroy$'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Initialize Menu Board Session Detail Subscription', () => {
    it('should update menuBoardSessionDetail and slides when detail has images', () => {
      fixture.detectChanges();
      menuBoardService.menuBoardSessionDetail = mockMenuBoardSessionDetail;

      (component as any).initMenuBoardSessionDetailSubscription();

      expect(component.menuBoardSessionDetail).toBe(mockMenuBoardSessionDetail);
      expect(component.slides).toEqual(['image1.jpg', 'image2.jpg']);
    });

    it('should update menuBoardSessionDetail and set empty slides when detail has no images', () => {
      const detailWithoutImages: MenuBoardSessionDetail = {
        menuBoard: { images: [] },
      } as unknown as MenuBoardSessionDetail;

      fixture.detectChanges();
      menuBoardService.menuBoardSessionDetail = detailWithoutImages;

      (component as any).initMenuBoardSessionDetailSubscription();

      expect(component.menuBoardSessionDetail).toBe(detailWithoutImages);
      expect(component.slides).toEqual([]);
    });

    it('should update menuBoardSessionDetail and set empty slides when detail is null', () => {
      fixture.detectChanges();
      menuBoardService.menuBoardSessionDetail = null;

      (component as any).initMenuBoardSessionDetailSubscription();

      expect(component.menuBoardSessionDetail).toBeNull();
      expect(component.slides).toEqual([]);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
