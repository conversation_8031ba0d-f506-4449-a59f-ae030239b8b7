import {
  afterNextR<PERSON>,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  contentChildren,
  EventEmitter,
  inject,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { MatSort, MatSortModule, SortDirection } from '@angular/material/sort';
import {
  MatColumnDef,
  MatTable,
  MatTableDataSource,
  MatTableModule,
} from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { FuseCustomPaginatorDirective } from '@fuse/components/table/custom-paginator.directive';
import { MatDialog } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule, NgClass } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseSafePipe } from '@fuse/pipes/safe';
import { SelectionModel } from '@angular/cdk/collections';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseUltils } from '../../ultils';
import { FuseErrorNoDataComponent } from '../error-no-data/error-no-data.component';
import { AdvancedSearchComponent } from '../advanced-search';
import { TranslocoPipe } from '@jsverse/transloco';
import { debounceTime } from 'rxjs';

export interface IAction {
  name: string;
  type: 'link' | 'event';
  url?: string;
  hidden?: (element?: any) => boolean;
  callback?: (element?: any) => void;
}

@Component({
  selector: 'fuse-table-component',
  styleUrls: ['table.component.scss'],
  templateUrl: 'table.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    MatIconModule,
    MatPaginatorModule,
    MatMenuModule,
    MatButtonModule,
    MatCheckboxModule,
    FormsModule,
    FuseCustomPaginatorDirective,
    NgClass,
    FuseSafePipe,
    TranslocoPipe,
    MatTooltipModule,
    FuseErrorNoDataComponent,
    AdvancedSearchComponent,
  ],
  hostDirectives: [MatSort],
})
export class FuseTableComponent implements AfterViewInit, OnInit, OnChanges {
  @Input() dataSource: Array<any> = [];
  @Input() displayedColumns: Array<{
    key: string;
    name: string;
    selected: boolean;
    custom?: boolean;
    sort?: boolean;
    render?: any;
    renderHtml?: any;
    description?: any;
    headerClass?: string;
    headerAlign?: 'left' | 'right' | 'center';
    cellClass?: string;
    cellAlign?: 'left' | 'right' | 'center';
  }> = [];
  @Input() total: any = 0;
  @Input() heightClass = 'h-[calc(100vh_-_521px)]';
  @Input() actions: Array<IAction> = [];
  @Input() bulkActions: Array<IAction> = [];
  @Input() template: any = ``;
  @Input() handleClickDeleteSelected: any;
  @Input() sortDefault: any;
  @Input() selectElement = false;
  @Input() hideColBtn = false;
  @Input() pagination = true;
  @Input() rowClick?: (row: any) => void;
  @Input() searchForm: any;

  columnsToDisplay!: string[];
  initDataSource!: MatTableDataSource<any>;
  private matSort = inject(MatSort);
  private customCols = contentChildren(MatColumnDef);
  private injector = inject(Injector);
  private dialog = inject(MatDialog);
  @ViewChild(MatTable, { static: true }) matTable!: MatTable<any>;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild('searchComponent') searchComponent!: AdvancedSearchComponent;

  // Selected element
  selectedElement = new SelectionModel<any>(true, []);
  @Output() selectedElementChanged = new EventEmitter();
  @Output() searchSelectChanged = new EventEmitter();

  currentDirection: SortDirection = '';
  isNoData = false;

  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
  ) {}

  ngOnInit() {
    this.currentDirection = this.sortDefault?.direction ?? '';
    this.initDataSource = new MatTableDataSource(this.dataSource);
  }

  ngAfterViewInit() {
    // this.initDataSource.sort = this.matSort;
    // this.initDataSource.paginator = this.paginator;
    this.customCols().forEach((col) => {
      const columnName = col.name;
      this.matTable?.addColumnDef(col);
      this.addColumnWhenReady(columnName);
    });

    this.paginator.page.pipe(debounceTime(500)).subscribe((value) => {
      if (value) {
        this.updateQueryParams();
      }
    });

    this.paginator._changePageSize = (pageSize: number) => {
      this.paginator.pageIndex = 0;
      this.paginator.pageSize = pageSize;
      this.updateQueryParams();
    };

    this.matSort.sortChange.subscribe(() => {
      this.updateQueryParams();
    });

    this._route.queryParams.subscribe((params: any) => {
      const pageIndex = params['page'] ? +params['page'] : 0;
      const pageSize = params['size'] ? +params['size'] : 10;
      const sortField = params['sortFields'] || '';
      const sortDirection = params['sortDirection'] || '';

      if (this.paginator) {
        this.paginator.pageIndex = pageIndex;
        this.paginator.pageSize = pageSize;
      }

      if (this.matSort) {
        this.matSort.active = sortField ? sortField : this.sortDefault?.field;
        this.matSort.direction = sortDirection
          ? sortDirection
          : this.sortDefault?.direction;
      }
    });
  }

  ngOnChanges(changes: any) {
    if (this.initDataSource) {
      this.initDataSource.data = this.dataSource;
      this.matTable.renderRows();
    }

    if (changes['displayedColumns']) {
      this.columnsToDisplay = this.displayedColumns
        .filter((col) => col.selected)
        .map((col) => col.key);

      if (this.selectElement) {
        this.columnsToDisplay.unshift('select');
      }

      if (this.actions && this.actions.length) {
        this.columnsToDisplay.push('actions');
      }
    }

    if (changes['dataSource']) {
      this.selectedElement.clear();
      this.selectedElementChanged.emit([]);

      this.paginator?.page.next(null as any);
    }

    if (changes['total'] && this.paginator) {
      this.paginator.length = this.total;
      this.paginator.page.next(null as any);
    }

    if (!this.selectElement) {
      const index = this.columnsToDisplay.findIndex(
        (value) => value === 'select',
      );
      if (index >= 0) {
        this.columnsToDisplay.splice(index, 1);
      }
    }

    this.isNoData = !(this.dataSource && this.dataSource.length > 0);
  }

  /**
   * Whether the total elements selected elements matches the total elements.
   */
  isAllSelected() {
    const totalRowSelected = this.selectedElement.selected.length;
    const totalRows = this.dataSource.length;
    return totalRowSelected === totalRows;
  }

  /**
   * Selects all elements if they are not all selected; otherwise clear selection.
   */
  toggleAllElements() {
    if (this.isAllSelected()) {
      this.selectedElement.clear();
      this.selectedElementChanged.emit([]);
      return;
    }
    this.selectedElement.select(...this.dataSource);
    this.selectedElementChanged.emit(this.selectedElement.selected);
  }

  /**
   * Select element if it is not selected; otherwise clear selection.
   */
  toggleElement(element: any): void {
    if (this.selectedElement.isSelected(element)) {
      this.selectedElement.deselect(element);
    } else {
      this.selectedElement.select(element);
    }
    this.selectedElementChanged.emit(this.selectedElement.selected);
  }

  toggleColumn(column: { key: string; selected: boolean }) {
    const columnIndex = this.displayedColumns.findIndex(
      (col) => col.key === column.key,
    );

    if (columnIndex !== -1) {
      this.displayedColumns[columnIndex].selected = column.selected;
      this.columnsToDisplay = this.displayedColumns
        .filter((col) => col.selected)
        .map((col) => col.key);

      if (this.selectElement) {
        this.columnsToDisplay.unshift('select');
      }

      if (this.actions && this.actions.length) {
        this.columnsToDisplay.push('actions');
      }
      this.matTable.renderRows();
    }
  }

  addColumnWhenReady(columnName: string): Promise<number> {
    return new Promise<number>((resolve) => {
      this._executeOnStable(() =>
        resolve(this.columnsToDisplay.push(columnName)),
      );
    });
  }

  isNullOrEmpty(value: any): boolean {
    return FuseUltils.isNullOrEmpty(value);
  }

  checkActions(actions: IAction[], element?: any) {
    return actions.some(
      (action) => action.hidden == null || !action.hidden(element),
    );
  }

  checkBulkActions(): boolean {
    return (
      this.bulkActions &&
      !!this.bulkActions.length &&
      this.selectedElement.selected &&
      !!this.selectedElement.selected.length &&
      this.checkActions(this.bulkActions)
    );
  }

  stopPropagation(event: MouseEvent): void {
    event.stopPropagation();
  }

  onChangeSearchSelect(event: any): void {
    this.searchSelectChanged.emit(event);
  }

  getHeaderClass(column: any) {
    return `${column.headerClass || ''} text-${column.headerAlign || ''} ${column.headerAlign || ''}`;
  }

  getCellClass(column: any) {
    return `${column.cellClass || ''} text-${column.cellAlign || ''}`;
  }

  returnCellWithClass(column: any) {
    return `<div class="${column.headerClass} text-${column.headerAlign}">_</div>`;
  }

  private _executeOnStable(fn: () => any): void {
    afterNextRender(fn, { injector: this.injector });
  }

  private updateQueryParams() {
    if (!this.matSort.direction) {
      if (this.currentDirection == 'desc') {
        this.currentDirection = 'asc';
      } else if (this.currentDirection == 'asc') {
        this.currentDirection = 'desc';
      }
      this.matSort.direction = this.currentDirection;
    } else {
      this.currentDirection = this.matSort.direction;
    }

    this._router.navigate([], {
      relativeTo: this._route,
      queryParams: {
        page: this.paginator.pageIndex,
        size: this.paginator.pageSize,
        sortFields: this.matSort.active,
        sortDirection: this.matSort.direction,
      },
      queryParamsHandling: 'merge',
    });
  }
}
