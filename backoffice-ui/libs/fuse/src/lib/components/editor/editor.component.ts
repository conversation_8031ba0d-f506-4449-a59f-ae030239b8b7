/* eslint-disable @typescript-eslint/no-empty-function */
import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import { TinymceComponent } from 'ngx-tinymce';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'fuse-editor',
  standalone: true,
  imports: [ReactiveFormsModule, TinymceComponent],
  templateUrl: './editor.component.html',
})
export class FuseEditorComponent implements OnInit, OnChanges {
  @Input() public form!: UntypedFormGroup;
  @Input() name = '';
  @Input() disabled = false;
  @Input() width!: number;
  @Input() height!: number;
  @Input() config: any = {
    selector: '.editor',
    plugins: 'table lists image media link directionality codesample wordcount', // quickbars
    toolbar:
      'undo redo | blocks | fontsizeinput | bold italic underline forecolor backcolor | align numlist bullist outdent indent | image media link | table | removeformat',
    menubar: false,
    statusbar: true,
    entity_encoding: 'raw',
    paste_as_text: true,
    remove_trailing_brs: false,
    force_br_newlines: true,
    force_p_newlines: true,
    placeholder: 'Type something...',
    file_picker_callback: (callback: any, value: any, meta: any) => {
      const input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', 'image/*');

      const validateFile = (
        file: File,
      ): { isValid: boolean; error?: string } => {
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
          return {
            isValid: false,
            error: 'Please upload the following file types: .jpeg, jpg, .png.',
          };
        }

        const maxSize = 0.05 * 1024 * 1024;
        if (file.size > maxSize) {
          return {
            isValid: false,
            error:
              'The size limit for images is 50 KB. Reduce the file size and try again.',
          };
        }

        return { isValid: true };
      };

      input.onchange = (event: Event) => {
        const target = event.target as HTMLInputElement;
        if (target.files && target.files.length > 0) {
          const file = target.files[0];

          const validation = validateFile(file);
          if (!validation.isValid) {
            this._toast.error(validation.error);
            return;
          }

          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result as string;
            callback(base64, { title: file.name });
          };
          reader.onerror = () => {
            this._toast.error('Unable to upload file. Please try again.');
          };
          reader.readAsDataURL(file);
        }
      };

      input.click();
    },
    setup: (editor: any) => {},
  };

  constructor(
    private _toast: ToastrService,
    private _changeDetectorRef: ChangeDetectorRef,
  ) {}

  onChange = (value: string) => {};
  onTouched = () => {};

  writeValue(obj: any): void {}

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnInit(): void {
    if (this.width) {
      this.config = { ...this.config, width: this.width };
    }

    if (this.height) {
      this.config = { ...this.config, height: this.height };
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['disabled'] && this.form.controls[this.name]) {
      if (this.disabled) {
        this.form.controls[this.name].disable();
      } else {
        this.form.controls[this.name].enable();
      }

      this._changeDetectorRef.detectChanges();
    }
  }
}
