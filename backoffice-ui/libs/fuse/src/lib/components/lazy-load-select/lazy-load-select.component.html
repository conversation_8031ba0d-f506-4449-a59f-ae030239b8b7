<div [formGroup]="form">
  <mat-form-field
    class="select-field"
    floatLabel="always"
    [subscriptSizing]="'dynamic'"
  >
    <mat-label>
      {{ label | transloco }}
      @if (required) {
        <span> * </span>
      }
      @if (description) {
        <mat-icon
          matPrefix
          [svgIcon]="'heroicons_outline:question-mark-circle'"
          class="w-3.5 h-3.5 cursor-pointer"
          #tooltip="matTooltip"
          [matTooltip]="description | transloco"
          [matTooltipPosition]="'above'"
        ></mat-icon>
      }
    </mat-label>
    <mat-select
      [id]="name"
      [placeholder]="placeholder | transloco"
      [disabled]="disabled"
      [multiple]="multiple"
      [hideSingleSelectionIndicator]="true"
      (change)="onValueChange($event)"
      [formControlName]="name"
      [panelClass]="'select-dropdown-panel'"
      (opened)="onSelectOpenedChange()"
      #select
    >
      @if (customTrigger) {
        <mat-select-trigger>
          <ng-content />
        </mat-select-trigger>
      }

      @if (showSearch) {
        <div class="sticky top-0 z-10 bg-white select-search">
          <mat-icon
            [svgIcon]="'heroicons_outline:magnifying-glass'"
            class="w-4 h-4"
            [style.color]="'#868FA0'"
          ></mat-icon>
          <input
            matInput
            id="select-search-input"
            [placeholder]="'common.search-placeholder' | transloco"
            [formControl]="searchControl"
          />
        </div>
      }

      @if (options && options.length && !checkNumberOfHidden()) {
        <cdk-virtual-scroll-viewport
          [orientation]="'vertical'"
          [itemSize]="itemSize || 10"
          [style.height]="viewportHeight || '138px'"
          [appendOnly]="true"
          #viewport
        >
          <div
            *cdkVirtualFor="let item of options"
            [ngClass]="{ hidden: item?.hidden }"
          >
            <mat-option [value]="item[optionValue]">
              {{ item[optionLabel] }}
            </mat-option>
          </div>
        </cdk-virtual-scroll-viewport>
      } @else {
        <mat-option [disabled]="true" [value]="null">
          {{ 'common.no-data-found' | transloco }}
        </mat-option>
      }

      @for (item of selectedData; track $index) {
        <div class="hidden">
          <mat-option [value]="item[optionValue]">
            {{ item[optionLabel] }}
          </mat-option>
        </div>
      }
    </mat-select>

    <!--  Custom Arrow  -->
    <mat-icon
      class="w-5 h-5 text-grey-800"
      matSuffix
      [svgIcon]="'styl:CaretDownOutlineRegular'"
    />
  </mat-form-field>

  @if (errorMessages) {
    <fuse-error-message
      [control]="form.get(name)"
      [messages]="errorMessages"
    ></fuse-error-message>
  }
</div>
