import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Mat<PERSON>enu, MatMenuTrigger } from '@angular/material/menu';
import { FormsModule } from '@angular/forms';
import { MatOption, MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { FuseInputComponent } from '@fuse/components/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import * as _ from 'lodash';
import moment from 'moment';

export interface CalendarDate {
  mDate: moment.Moment;
  selected?: boolean;
  today?: boolean;
}

@Component({
  selector: 'fuse-calendar',
  templateUrl: './calendar.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatOption,
    MatMenu,
    FuseInputComponent,
    MatMenuTrigger,
    MatIcon,
  ],
})
export class FuseCalendarComponent implements OnInit {
  public currentDate: any;
  public namesOfDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  public weeks: Array<CalendarDate[]> = [];
  public weeksFull: Array<CalendarDate[]> = [];

  public selectedDate: any;
  public selectedStartWeek: any;
  public selectedEndWeek: any;

  dateIsoFormat = 'YYYY-MM-DD';

  @Output() selectedDateChange = new EventEmitter<any>();

  ngOnInit() {
    this.currentDate = moment();
    this.selectedDate = moment();
    this.selectedDate = moment(this.currentDate).valueOf();
    this.generateCalendar();
    this.emitSelectedDate();
  }

  private generateCalendar(): void {
    const dates = this.fillDates(this.currentDate);
    const weeks = [];
    while (dates.length > 0) {
      weeks.push(dates.splice(0, 7));
    }
    this.weeks = weeks;
  }

  protected generateCalendarFull(): void {
    const dates = this.fillDatesFull(this.currentDate);
    const weeks = [];
    while (dates.length > 0) {
      weeks.push(dates.splice(0, 7));
    }
    this.weeksFull = weeks;
  }

  private fillDates(currentMoment: moment.Moment | undefined) {
    let startCalendar = moment(currentMoment).startOf('month').date(1);
    let endCalendar = moment(currentMoment).startOf('month').date(18);

    if (moment(this.selectedDate).isBefore(startCalendar)) {
      startCalendar = moment(this.selectedDate).subtract(18, 'days');
      endCalendar = moment(this.selectedDate);
    } else if (moment(this.selectedDate).isAfter(endCalendar)) {
      startCalendar = moment(this.selectedDate);
      endCalendar = moment(this.selectedDate).add(18, 'days');
    }

    return _.range(0, endCalendar.diff(startCalendar, 'days') + 1).map(
      (index) => {
        const newDate = moment(startCalendar).add(index, 'days');
        return {
          today: this.isToday(newDate),
          selected: this.isSelected(newDate),
          mDate: newDate,
          rank: index + 1, // Set the rank based on the index
        };
      },
    );
  }

  private fillDatesFull(currentMoment: moment.Moment | undefined) {
    const firstOfMonth = moment(currentMoment).startOf('month').day();
    const lastOfMonth = moment(currentMoment).endOf('month').day();

    const firstDayOfGrid = moment(currentMoment)
      .startOf('month')
      .subtract(firstOfMonth, 'days');
    const lastDayOfGrid = moment(currentMoment)
      .endOf('month')
      .subtract(lastOfMonth, 'days')
      .add(7, 'days');
    const startCalendar = firstDayOfGrid.date();

    return _.range(
      startCalendar,
      startCalendar + lastDayOfGrid.diff(firstDayOfGrid, 'days'),
    ).map((date) => {
      const newDate = moment(firstDayOfGrid).date(date);
      return {
        today: this.isToday(newDate),
        selected: this.isSelected(newDate),
        mDate: newDate,
      };
    });
  }

  private isToday(date: moment.Moment): boolean {
    return moment().isSame(moment(date), 'day');
  }

  protected isSelected(date: moment.Moment): boolean {
    return (
      moment(this.selectedDate).startOf('day').valueOf() ===
      moment(date).startOf('day').valueOf()
    );
  }

  public prevMonth(): void {
    this.currentDate = moment(this.currentDate).subtract(1, 'months');
    this.generateCalendar();
  }

  public nextMonth(): void {
    this.currentDate = moment(this.currentDate).add(1, 'months');
    this.generateCalendar();
  }

  public isDisabledMonth(currentDate: moment.MomentInput): boolean {
    const today = moment();
    return moment(currentDate).isBefore(today, 'months');
  }

  public isSelectedMonth(date: moment.Moment): boolean {
    const today = moment();
    return (
      moment(date).isSame(this.currentDate, 'month') &&
      moment(date).isSameOrBefore(today)
    );
  }

  public selectDate(date: CalendarDate) {
    this.currentDate = moment(date.mDate);
    this.selectedDate = moment(this.currentDate).valueOf();
    this.generateCalendar();
    this.emitSelectedDate();
  }

  private isSelectedWeek(date: moment.Moment): boolean {
    return (
      (moment(date).isBefore(this.selectedEndWeek) &&
        moment(date).isAfter(this.selectedStartWeek)) ||
      moment(date.format('YYYY-MM-DD')).isSame(
        this.selectedStartWeek.format('YYYY-MM-DD'),
      ) ||
      moment(date.format('YYYY-MM-DD')).isSame(
        this.selectedEndWeek.format('YYYY-MM-DD'),
      )
    );
  }

  public isDayBeforeLastSat(date: moment.Moment): boolean {
    const lastSat = moment().weekday(-1);
    return moment(date).isSameOrBefore(lastSat);
  }

  public goToToday(): void {
    this.currentDate = moment();
    this.selectedDate = moment(this.currentDate).valueOf();
    this.generateCalendar();
    this.emitSelectedDate();
  }

  public prevDate(): void {
    this.currentDate = moment(this.currentDate).subtract(1, 'days');
    this.selectedDate = moment(this.currentDate).valueOf();
    this.generateCalendar();
    this.emitSelectedDate();
  }

  public nextDay(): void {
    this.currentDate = moment(this.currentDate).add(1, 'days');
    this.selectedDate = moment(this.currentDate).valueOf();
    this.generateCalendar();
    this.emitSelectedDate();
  }

  private emitSelectedDate(): void {
    this.selectedDateChange.emit(this.selectedDate);
  }
}
