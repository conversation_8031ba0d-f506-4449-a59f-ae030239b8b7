<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.4</version>
        <relativePath/>
    </parent>

    <groupId>com.styl.pacific</groupId>
    <artifactId>pacific-microservices</artifactId>
    <version>1.2.5</version>
    <packaging>pom</packaging>

    <modules>
        <module>aggregate-jacoco-report</module>
        <module>aggregator</module>
        <module>authorization-service</module>
        <module>backoffice-ui</module>
        <module>catalog-service</module>
        <module>common</module>
        <module>docker-compose</module>
        <module>gateway</module>
        <module>lambda</module>
        <module>load-test</module>
        <module>notification-service</module>
        <module>openapi</module>
        <module>order-service</module>
        <module>payment-service</module>
        <module>sfpt-adapter</module>
        <module>store-service</module>
        <module>tenant-service</module>
        <module>user-service</module>
        <module>utility-service</module>
        <module>wallet-service</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>gitlab-maven</id>
            <url>https://git.styl.solutions/api/v4/projects/1655/packages/maven</url>
        </repository>
    </distributionManagement>

    <properties>
        <apt-maven-plugin.version>1.1.3</apt-maven-plugin.version>
        <avro.version>1.12.0</avro.version>
        <aws.java.sdk.version>2.27.24</aws.java.sdk.version>
        <caffeine.version>3.1.8</caffeine.version>
        <commons-codec.version>1.16.1</commons-codec.version>
        <commons-io.version>2.16.1</commons-io.version>
        <depgraph-maven-plugin.version>4.0.3</depgraph-maven-plugin.version>
        <docker.package>com.styl.pacific</docker.package>
        <feign-micrometer.version>13.5</feign-micrometer.version>
        <flyway-core.version>10.16.0</flyway-core.version>
        <flyway-database-postgresql.version>10.16.0</flyway-database-postgresql.version>
        <freemarker.version>2.3.33</freemarker.version>
        <gatling-maven-plugin.version>4.11.0</gatling-maven-plugin.version>
        <gatling.version>3.13.1</gatling.version>
        <!-- Hypersistence: Hibernate TreeType -->
        <hypersistence.version>3.7.5</hypersistence.version>
        <itext-core.version>9.1.0</itext-core.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <java.version>21</java.version>
        <json-web-token.version>0.12.6</json-web-token.version>
        <kafka-avro-serializer.version>7.7.2</kafka-avro-serializer.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <lombok.version>1.18.34</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>3.7.1</maven-dependency-plugin.version>
        <maven-failsafe-plugin.version>3.3.1</maven-failsafe-plugin.version>
        <maven.deploy.skip>true</maven.deploy.skip>
        <micrometer-registry-prometheus.version>1.14.1</micrometer-registry-prometheus.version>
        <mockito-core.version>5.12.0</mockito-core.version>
        <opencsv.version>5.9</opencsv.version>
        <org.testcontainers.version>1.20.0</org.testcontainers.version>
        <shedlock-spring.version>5.16.0</shedlock-spring.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <spotless-maven-plugin.version>2.44.0.BETA1</spotless-maven-plugin.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-kafka.version>3.3.0</spring-kafka.version>
        <springdoc-openapi-starter-webmvc-ui.version>2.8.6</springdoc-openapi-starter-webmvc-ui.version>
        <stripe-java.version>26.8.0</stripe-java.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-core</artifactId>
                <version>${itext-core.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>
            <!-- COMMON -->
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-feign</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-mapstruct</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-notification</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-tracing</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>common-validator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- KAFKA -->
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>kafka-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>kafka-consumer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>kafka-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>kafka-producer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific</groupId>
                <artifactId>openapi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- AGGREGATION -->
            <dependency>
                <groupId>com.styl.pacific.aggregator</groupId>
                <artifactId>device-aggregator</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- AUTHORIZATION SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.authz.service</groupId>
                <artifactId>authorization-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- CATALOG SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.catalog.service</groupId>
                <artifactId>catalog-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.common</groupId>
                <artifactId>aws-dynamodb</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- AWS -->
            <dependency>
                <groupId>com.styl.pacific.common</groupId>
                <artifactId>aws-s3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.common</groupId>
                <artifactId>common-constant</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.common</groupId>
                <artifactId>common-logging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.common.aws</groupId>
                <artifactId>aws-secretsmanager</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- GATEWAY -->
            <dependency>
                <groupId>com.styl.pacific.gateway</groupId>
                <artifactId>gateway-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.gateway</groupId>
                <artifactId>gateway-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.gateway</groupId>
                <artifactId>gateway-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.lambda.datasync</groupId>
                <artifactId>lambda-datasync</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- NOTIFICATION SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.notification.service</groupId>
                <artifactId>notification-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- ORDER -->
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-data-access-dynamodb</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-data-access-jpa</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-data-ingestion</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-scheduling</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.order.service</groupId>
                <artifactId>order-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- PAYMENT SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-loadtest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-processor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service</groupId>
                <artifactId>payment-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-cash</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-ewallet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-nets-terminal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-offline</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-stripe</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.styl.pacific.payment.service.integration</groupId>
                <artifactId>payment-stripe-connect</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- SFPT ADAPTER -->
            <dependency>
                <groupId>com.styl.pacific.sfpt.adapter</groupId>
                <artifactId>sfpt-adapter-application</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- STORE SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.store.service</groupId>
                <artifactId>store-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- TENANT SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.tenant.service</groupId>
                <artifactId>tenant-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- USER SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-scheduling</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.user.service</groupId>
                <artifactId>user-test</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- UTILITY SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-data-access-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-data-access-jpa</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-data-access-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-scheduling</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service</groupId>
                <artifactId>utility-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.styl.pacific.utility.service.datasync</groupId>
                <artifactId>utility-datasync-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service.datasync</groupId>
                <artifactId>utility-datasync-scheduling</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.styl.pacific.utility.service.datasync</groupId>
                <artifactId>utility-datasync-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.styl.pacific.utility.service.import</groupId>
                <artifactId>utility-import-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service.import</groupId>
                <artifactId>utility-import-customer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service.import</groupId>
                <artifactId>utility-import-menu</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service.import</groupId>
                <artifactId>utility-import-pre-order-menu</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.utility.service.import</groupId>
                <artifactId>utility-import-product</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- WALLET SERVICE -->
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-data-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-domain-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-domain-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-messaging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-rest</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-scheduling</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.styl.pacific.wallet.service</groupId>
                <artifactId>wallet-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-micrometer</artifactId>
                <version>${feign-micrometer.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>io.hypersistence</groupId>
                <artifactId>hypersistence-utils-hibernate-63</artifactId>
                <version>${hypersistence.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${json-web-token.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-api</artifactId>
                <version>1.43.0</version>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-postgresql</artifactId>
                <version>${flyway-database-postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${org.testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${jacoco-maven-plugin.version}</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven2</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
        <repository>
            <id>confluent</id>
            <url>https://packages.confluent.io/maven/</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless-maven-plugin.version}</version>
                <configuration>
                    <pom>
                        <includes>
                            <include>pom.xml</include>
                        </includes>
                        <sortPom>
                            <expandEmptyElements>false</expandEmptyElements>
                            <nrOfIndentSpace>4</nrOfIndentSpace>
                            <sortModules>true</sortModules>
                            <sortDependencies>groupId,artifactId,scope</sortDependencies>
                            <sortDependencyManagement>groupId,artifactId,scope</sortDependencyManagement>
                            <sortDependencyExclusions>groupId,artifactId</sortDependencyExclusions>
                            <sortPlugins>groupId,artifactId</sortPlugins>
                            <sortProperties>true</sortProperties>
                            <sortModules>true</sortModules>
                            <sortExecutions>true</sortExecutions>
                        </sortPom>
                        <lineEndings>UNIX</lineEndings>
                    </pom>
                    <java>
                        <includes>
                            <include>src/main/java/**/*.java</include>
                            <include>src/test/java/**/*.java</include>
                        </includes>
                        <eclipse>
                            <file>eclipse-formatter.xml</file>
                        </eclipse>
                        <importOrder/>
                        <removeUnusedImports/>
                        <replaceRegex>
                            <name>Remove wildcard imports</name>
                            <searchRegex>import\s+[^\*\s]+\*;(\r\n|\r|\n)</searchRegex>
                            <replacement>$1</replacement>
                        </replaceRegex>
                        <lineEndings>UNIX</lineEndings>
                        <licenseHeader>
                            <file>COPYRIGHT</file>
                        </licenseHeader>
                    </java>
                    <sql>
                        <includes>
                            <include>src/main/resources/**/*.sql</include>
                        </includes>
                        <dbeaver/>
                        <lineEndings>UNIX</lineEndings>
                    </sql>
                    <json>
                        <includes>
                            <!-- You have to set the target manually -->
                            <include>*.json</include>
                        </includes>
                        <gson>
                            <indentSpaces>4</indentSpaces>
                            <sortByKeys>false</sortByKeys>
                        </gson>
                    </json>
                    <upToDateChecking>
                        <enabled>true</enabled>
                        <indexFile>${project.basedir}/.spotless-index</indexFile>
                    </upToDateChecking>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                            <goal>apply</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.ferstl</groupId>
                <artifactId>depgraph-maven-plugin</artifactId>
                <version>${depgraph-maven-plugin.version}</version>
            </plugin>

            <plugin>
                <groupId>io.gatling</groupId>
                <artifactId>gatling-maven-plugin</artifactId>
                <version>${gatling-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Amapstruct.unmappedTargetPolicy=ERROR</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>jacoco-prepare</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <phase>test</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
