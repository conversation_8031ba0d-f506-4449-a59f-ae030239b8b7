/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.order;

import com.styl.pacific.loadtest.commons.BaseLoadTestAPIsSimulation;
import com.styl.pacific.loadtest.commons.LoadTestConfigProperties;
import com.styl.pacific.loadtest.commons.ScenarioApiLoadTestProvider;
import com.styl.pacific.loadtest.order.apis.CoreGwOrderCashPaymentScenarioApiProvider;
import java.util.List;

public class CoreGwOrderCashPaymentSimulation extends BaseLoadTestAPIsSimulation {

	@Override
	protected List<ScenarioApiLoadTestProvider> loadScenarioProviders(
			LoadTestConfigProperties loadTestConfigProperties) {
		return List.of(new CoreGwOrderCashPaymentScenarioApiProvider(loadTestConfigProperties.getApiConnection(),
				loadTestConfigProperties.getApiLoadTestConfigs()
						.getOrder()));
	}
}