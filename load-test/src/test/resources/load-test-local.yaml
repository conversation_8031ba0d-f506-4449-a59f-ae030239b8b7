coreGatewayUrl: https://bo-pacific-ii-sit.styl.solutions
userToken: xxxx.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xxx


strategies:
  - strategy: CONSTANT_USERS_PER_SEC
    ## Ideas: Every seconds Amount of Users would fill in.
    ## Type: SUSTAINED_LOAD
    config:
      ## Ideas: 1 user = 5 API calls.
      ## Every seconds fill in 1 users meaning, 5 API calls.
      ## API Call Per User: 5 API calls
      ## UserRate: 5
      ## Total API Calls: 5 * 5 * 1 * 60 = 1500 API calls
      ## Request Rate: 1500/60 ~ 25 req/ seconds
      userRate: 5
      duration: PT5M

  - strategy: RAMP_USERS_PER_SEC
    isDefault: true
    ## Ideas: Ramp up users from Amount to max Amount during the time
    ## Type: RAMPING_LOAD
    config:
      fromUserRate: 10
      toUserRate: 50
      duration: PT4M

  - strategy: CONSTANT_CONCURRENT_USERS
    ## Ideas: Always Fixed Amount Users in System during the time. (1 User Completed, Another will be back-fill)
    ## Type: STRESS_LOAD
    config:
      userRate: 1000
      duration: PT5M

  - strategy: EXPLOSION_USERS
    ## Ideas: Make an explosive users rate at once.
    ## Spike Load
    ## Type: SPIKE_LOAD
    config:
      usersRate: 1000 * 5

apiLoadTestConfigs:
  cashPaymentSessionApiConfigProperties:
    tenantId: 124712395262848000
    cashPaymentMethodId: 126650278231917568
    amount: 12300
    netAmount: 12300
    fee: 0
    currencyCode: SGD
    customerEmail: <EMAIL>
    customerName: Vinh Pham
    systemSource: Load Test

  walletPaymentSessionApiConfigProperties:
    tenantId: 124712395262848000
    ewalletPaymentMethodId: 126599243912168448
    amount: 12300
    netAmount: 12300
    fee: 100
    currencyCode: SGD
    customerEmail: <EMAIL>
    customerName: Vinh Pham
    systemSource: Load Test

  stripePaymentSessionApiConfigProperties:
    tenantId: 124712395262848000
    stripePaymentMethodId: 126324165124780032
    amount: 12768
    netAmount: 12300
    fee: 468
    currencyCode: SGD
    customerEmail: <EMAIL>
    customerName: Vinh Pham
    systemSource: Load Test
    cancelRedirectUrl: https://cp-pacific-ii-sit.styl.solutions/portal/order/history
    successRedirectUrl: https://cp-pacific-ii-sit.styl.solutions/portal/order/history?success=true

  offlinePaymentSessionApiConfigProperties:
    tenantId: 124712395262848000
    offlinePaymentMethodId: 126324165124780032
    amount: 12768
    netAmount: 12300
    fee: 468
    currencyCode: SGD
    customerEmail: <EMAIL>
    customerName: Vinh Pham
    systemSource: Load Test

  netsPaymentSessionApiConfigProperties:
    tenantId: 124712395262848000
    netsPaymentMethodId: 126599413496267776
    amount: 12768
    netAmount: 12300
    fee: 468
    currencyCode: SGD
    customerEmail: <EMAIL>
    customerName: Vinh Pham
    systemSource: Load Test

