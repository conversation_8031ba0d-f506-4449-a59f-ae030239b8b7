/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.publisher;

import com.styl.pacific.kafka.notification.avro.model.QueueNotificationCreatedAvroEvent;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.notification.service.domain.config.NotificationKafkaConfigProperties;
import com.styl.pacific.notification.service.domain.event.QueueNotificationCreatedEvent;
import com.styl.pacific.notification.service.domain.output.publisher.NotificationQueuePublisher;
import com.styl.pacific.notification.service.mapper.NotificationMessagingDataMapper;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class NotificationQueueKafkaPublisher implements NotificationQueuePublisher {

	private static final Logger logger = LoggerFactory.getLogger(NotificationQueueKafkaPublisher.class);
	private final KafkaProducer<Serializable, SpecificRecordBase> kafkaProducer;
	private final NotificationKafkaConfigProperties notificationKafkaConfigProperties;

	@Async
	@Override
	public void publish(QueueNotificationCreatedEvent event) {
		QueueNotificationCreatedAvroEvent queueNotificationEvent = NotificationMessagingDataMapper.INSTANCE
				.queueNotificationCreatedEventToAvroQueueNotificationCreatedEvent(event);
		kafkaProducer.send(notificationKafkaConfigProperties.getQueueTopic(), queueNotificationEvent.getId(),
				queueNotificationEvent, (result, error) -> {
					if (error == null) {
						logger.info("Sent QueueNotificationAvroEvent with eventId: {}", queueNotificationEvent.getId());
					} else {
						logger.error("Error in sending message to topic: [{}] with eventId: {}",
								notificationKafkaConfigProperties.getQueueTopic(), queueNotificationEvent.getId(),
								error);
						logger.error(error.getMessage(), error);
					}
				});
	}
}
