/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.application;

import com.styl.pacific.domain.valueobject.InAppNotificationTrackingId;
import com.styl.pacific.notification.service.domain.entity.InAppNotificationTrackingIdGenerator;
import com.styl.pacific.utils.snowflake.id.Snowflake;

/**
 * <AUTHOR>
 *
 */
public class InAppNotificationTrackingIdGeneratorImpl implements InAppNotificationTrackingIdGenerator {

	private final Snowflake snowflake;

	public InAppNotificationTrackingIdGeneratorImpl() {
		this.snowflake = new Snowflake();
	}

	@Override
	public InAppNotificationTrackingId nextId() {
		return new InAppNotificationTrackingId(snowflake.nextId());
	}
}
