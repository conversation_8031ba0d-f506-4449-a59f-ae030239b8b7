---
server:
  port: 9207
logging:
  level:
    com.styl.pacific: DEBUG
management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
      metrics:
        enabled: true
  endpoints:
    web:
      exposure:
        include: "*"

pacific:
  aws:
    dynamodb:
      init-table:
        enable: false
        packageName: com.styl.pacific.notification.service.data.access.entity
      endpoint:
      accessKey:
      secretKey:
      region: ap-southeast-1
  template:
    email:
      path: classpath:templates/email/
  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317
  clients:
    user-service:
      url: http://user-svc.application.svc.cluster.local:9202
    tenant-service:
      url: http://tenant-svc.application.svc.cluster.local:9201
  kafka:
    notification-service:
      consumers:
        retry-attempts: 3
        retry-interval-ms: 3000
        retry-auto-create-topics: false
        notification-event:
          enabled: true
          group-id: notification-service-notification-command
          topic-name: notification-service-notification-command
        notification-queue:
          enabled: true
          group-id: notification-service-notification-queue
          topic-name: notification-service-notification-queue
        notification-queue-email:
          enabled: true
          group-id: notification-service-notification-queue-email
          topic-name: notification-service-notification-queue-email
        notification-queue-in-app:
          enabled: true
          group-id: notification-service-notification-queue-in-app
          topic-name: notification-service-notification-queue-in-app
      publisher:
        notification-queue:
          topic-name: notification-service-notification-queue
        notification-queue-email:
          topic-name: notification-service-notification-queue-email
        notification-queue-in-app:
          topic-name: notification-service-notification-queue-in-app
micrometer:
  observations:
    annotations:
      enabled: true
spring:
  application:
    name: notification-service
  jpa:
    hibernate.ddl-auto: none
    open-in-view: false
    show-sql: true
  datasource:
    url: ***************************************************************************
    username: postgres
    password: postgres
    platform: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    baselineOnMigrate: true
    validateOnMigrate: true
    locations: classpath:db/migration
  mail:
    host: localhost
    username: <EMAIL>
    password: tfazkhlivnooo
    port: 25
    from: ""
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

kafka-config:
  bootstrap-servers: "localhost:19092, localhost:29092, localhost:39092"
  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:8081
  num-of-partitions: 3
  replication-factor: 3
kafka-producer-config:
  key-serializer-class: org.apache.kafka.common.serialization.UUIDSerializer
  value-serializer-class: io.confluent.kafka.serializers.KafkaAvroSerializer
  compression-type: none
  acks: all
  batch-size: 16384
  batch-size-boost-factor: 100
  linger-ms: 5
  request-timeout-ms: 60000
  retry-count: 5
kafka-consumer-config:
  key-deserializer: org.apache.kafka.common.serialization.UUIDDeserializer
  value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  auto-offset-reset: earliest
  specific-avro-reader-key: specific.avro.reader
  specific-avro-reader: true
  batch-listener: false
  auto-startup: true
  concurrency-level: 3
  session-timeout-ms: 10000
  heartbeat-interval-ms: 3000
  max-poll-interval-ms: 300000
  max-poll-records: 500
  max-partition-fetch-bytes-default: 1048576
  max-partition-fetch-bytes-boost-factor: 1
  poll-timeout-ms: 150
