/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.notification.service.apis.NotificationsApi;
import com.styl.pacific.notification.service.domain.NotificationDomainService;
import com.styl.pacific.notification.service.domain.dto.FindInAppNotificationsQuery;
import com.styl.pacific.notification.service.domain.dto.InAppNotificationsFilterQuery;
import com.styl.pacific.notification.service.domain.dto.SaveInAppNotificationTrackingCommand;
import com.styl.pacific.notification.service.requests.DeleteInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.FindInAppNotificationRequest;
import com.styl.pacific.notification.service.requests.ReadInAppNotificationRequest;
import com.styl.pacific.notification.service.response.InAppNotificationTrackingResponse;
import com.styl.pacific.notification.service.response.ListInAppNotificationResponse;
import com.styl.pacific.notification.service.rest.mapper.NotificationControllerMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class NotificationController implements NotificationsApi {

	private final NotificationDomainService notificationDomainService;

	private final RequestContext requestContext;

	@Override
	public ListInAppNotificationResponse findInAppNotifications(FindInAppNotificationRequest request) {
		InAppNotificationsFilterQuery filterQuery = NotificationControllerMapper.INSTANCE
				.inAppNotificationsFilterRequestToInAppNotificationFilterQuery(request.getFilter(), requestContext
						.getTenantId(), requestContext.getTokenClaim()
								.getUserId());
		FindInAppNotificationsQuery query = NotificationControllerMapper.INSTANCE
				.findsInAppNotificationRequestToFindsInAppNotificationQuery(request, filterQuery);
		return notificationDomainService.findInAppNotifications(query);
	}

	public void deleteInAppNotifications(DeleteInAppNotificationRequest request) {
		notificationDomainService.deleteInAppNotifications(NotificationControllerMapper.INSTANCE
				.deleteInAppNotificationRequestToDeleteInAppNotificationCommand(request, requestContext.getTenantId(),
						requestContext.getTokenClaim()
								.getUserId()));
	}

	public void readInAppNotifications(ReadInAppNotificationRequest request) {
		notificationDomainService.readInAppNotifications(NotificationControllerMapper.INSTANCE
				.readInAppNotificationRequestToReadInAppNotificationCommand(request, requestContext.getTenantId(),
						requestContext.getTokenClaim()
								.getUserId()));
	}

	@Override
	public void saveInAppNotificationTracking() {
		notificationDomainService.saveInAppNotificationTracking(SaveInAppNotificationTrackingCommand.builder()
				.userId(Long.parseLong(requestContext.getTokenClaim()
						.getUserId()))
				.tenantId(requestContext.getTenantId())
				.hasNewNotification(false)
				.build());
	}

	@Override
	public InAppNotificationTrackingResponse getInAppNotificationTracking() {
		return NotificationControllerMapper.INSTANCE.inAppNotificationTrackingToResponse(notificationDomainService
				.getInAppNotificationTracking(Long.parseLong(requestContext.getTokenClaim()
						.getUserId())));
	}
}
