/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander.helper;

import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.domain.output.repository.UserPreferenceRepository;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class NotificationChannelHelper {

	private final UserPreferenceRepository userPreferenceRepository;

	public List<NotificationChannel> getNotificationChannelUserPreference(Long customerId, String action) {
		return switch (action) {
		case Action.BALANCE_CHANGE -> userPreferenceRepository.getChannelNotificationUserPreference(customerId,
				UserPreferenceKey.NOTIFICATION_WALLET_BALANCE_CHANGED);
		case Action.PAYMENT_SUCCESS -> userPreferenceRepository.getChannelNotificationUserPreference(customerId,
				UserPreferenceKey.NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED);
		case Action.UPDATE_ORDER_STATUS -> userPreferenceRepository.getChannelNotificationUserPreference(customerId,
				UserPreferenceKey.NOTIFICATION_ORDER_STATUS_UPDATED);
		default -> Collections.emptyList();
		};
	}
}
