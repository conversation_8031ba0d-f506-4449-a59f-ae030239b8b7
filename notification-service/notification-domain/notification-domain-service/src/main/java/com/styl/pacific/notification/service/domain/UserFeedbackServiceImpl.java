/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain;

import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.FeedbackEmailDto;
import com.styl.pacific.notification.service.domain.dto.SendUserFeedbackDto;
import com.styl.pacific.notification.service.domain.entity.dto.CustomerDto;
import com.styl.pacific.notification.service.domain.output.repository.TenantRepository;
import com.styl.pacific.notification.service.domain.output.repository.UserRepository;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class UserFeedbackServiceImpl implements UserFeedbackService {
	private final TenantRepository tenantRepository;
	private final NotificationDomainService notificationDomainService;
	private final UserRepository userRepository;

	@Override
	public void sendUserFeedback(SendUserFeedbackDto dto) {
		FeedbackEmailDto feedbackEmail = tenantRepository.findFeedbackEmail();
		if (StringUtils.hasText(feedbackEmail.getEmail())) {
			CustomerDto user = userRepository.getCustomerById(dto.getUserId()
					.getValue());
			notificationDomainService.createNotification(CreateNotificationCommand.builder()
					.id(UUID.randomUUID())
					.channel(NotificationChannel.EMAIL)
					.userId(dto.getUserId()
							.getValue())
					.tenantId(dto.getTenantId()
							.getValue())
					.action(Action.USER_FEEDBACK)
					.source("notification-service")
					.data(Map.of("name", Optional.ofNullable(user.getName())
							.filter(org.apache.commons.lang3.StringUtils::isNotBlank)
							.orElse(""), EmailNotificationKey.TO, feedbackEmail.getEmail(), "email", Optional
									.ofNullable(user.getEmail())
									.filter(org.apache.commons.lang3.StringUtils::isNotBlank)
									.orElse(""), "title", dto.getTitle(), "description", dto.getDescription()))
					.createdAt(Instant.now()
							.toEpochMilli())
					.build());
		}
	}
}
