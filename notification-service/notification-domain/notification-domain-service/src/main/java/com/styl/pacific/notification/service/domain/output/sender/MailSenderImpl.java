/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.output.sender;

import com.styl.pacific.notification.service.domain.config.EmailConfigProperties;
import com.styl.pacific.notification.service.domain.entity.EmailNotification;
import com.styl.pacific.notification.service.domain.exception.NotificationDomainException;
import jakarta.mail.Multipart;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
public class MailSenderImpl implements MailSender {
	private final JavaMailSender javaMailSender;
	private final EmailConfigProperties emailConfigProperties;

	@Value("${spring.mail.whitelist}")
	private List<String> whitelist; //

	public MailSenderImpl(JavaMailSender javaMailSender, EmailConfigProperties emailConfigProperties) {
		this.javaMailSender = javaMailSender;
		this.emailConfigProperties = emailConfigProperties;
	}

	@Override
	public void sendMail(EmailNotification mailModel) {
		try {
			MimeMessage message = javaMailSender.createMimeMessage();
			MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
			helper.setTo(mailModel.getTo());
			if (StringUtils.hasText(emailConfigProperties.getFrom())) {
				helper.setFrom(emailConfigProperties.getFrom());
			}
			if (!CollectionUtils.isEmpty(List.of(mailModel.getCc()))) {
				helper.setCc(mailModel.getCc());
			}
			if (!CollectionUtils.isEmpty(List.of(mailModel.getBcc()))) {
				helper.setBcc(mailModel.getBcc());
			}
			helper.setSubject(mailModel.getSubject());
			Multipart multipart = new MimeMultipart();
			MimeBodyPart htmlBodyPart = new MimeBodyPart();
			htmlBodyPart.setContent(mailModel.getBody(), "text/html; charset=UTF-8");
			multipart.addBodyPart(htmlBodyPart);
			message.setContent(multipart);
			javaMailSender.send(message);
		} catch (Exception ex) {
			throw new NotificationDomainException("Error send mail ", ex);
		}
	}

	private boolean isValidEmail(String ...email) {
		
	}
}
