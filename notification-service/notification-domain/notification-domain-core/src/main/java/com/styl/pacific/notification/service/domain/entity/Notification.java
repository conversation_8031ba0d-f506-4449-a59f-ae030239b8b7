/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.entity;

import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public class Notification {

	private UUID id;
	private Long userId;
	private Long tenantId;
	private String action;
	private String source;
	private NotificationChannel channel;
	private Map<String, String> data;
	private Instant createdAt;
	private Instant expiresAt;

	private Notification(Builder builder) {
		id = builder.id;
		userId = builder.userId;
		tenantId = builder.tenantId;
		action = builder.action;
		source = builder.source;
		channel = builder.channel;
		data = builder.data;
		createdAt = builder.createdAt;
		expiresAt = builder.expiresAt;
	}

	public static Builder builder() {
		return new Builder();
	}

	public static final class Builder {
		private UUID id;
		private Long userId;
		private Long tenantId;
		private String action;
		private String source;
		private NotificationChannel channel;
		private Map<String, String> data;
		private Instant createdAt;
		private Instant expiresAt;

		private Builder() {
		}

		public static Builder newBuilder() {
			return new Builder();
		}

		public Builder id(UUID id) {
			this.id = id;
			return this;
		}

		public Builder userId(Long userId) {
			this.userId = userId;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder action(String action) {
			this.action = action;
			return this;
		}

		public Builder source(String source) {
			this.source = source;
			return this;
		}

		public Builder channel(NotificationChannel channel) {
			this.channel = channel;
			return this;
		}

		public Builder data(Map<String, String> data) {
			this.data = data;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder expiresAt(Instant expiresAt) {
			this.expiresAt = expiresAt;
			return this;
		}

		public Notification build() {
			return new Notification(this);
		}
	}
}
